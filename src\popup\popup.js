/**
 * @file AI Side Panel 弹窗脚本
 * @description 处理扩展弹窗的交互逻辑和快速设置功能
 */

// #region 初始化
document.addEventListener('DOMContentLoaded', async () => {
    await aisp_initializePopup();
    aisp_setupEventListeners();
});

/**
 * @function aisp_initializePopup - 初始化弹窗
 * @description 加载设置和状态信息
 */
async function aisp_initializePopup() {
    try {
        // 初始化多语言支持
        await aisp_initializeLanguage();

        // 加载配置
        const result = await chrome.storage.local.get('aisp_config');
        const config = result.aisp_config || {};

        // 更新UI状态
        aisp_updateUIFromConfig(config);

        // 更新状态信息
        await aisp_updateStatusInfo();

        console.log('✅ 弹窗初始化完成');
    } catch (error) {
        console.error('❌ 弹窗初始化失败:', error);
    }
}

/**
 * @function aisp_initializeLanguage - 初始化多语言支持
 * @description 加载语言资源并设置当前语言
 */
async function aisp_initializeLanguage() {
    try {
        // 检查language-manager是否可用
        if (typeof lang_initialize !== 'function') {
            console.warn('⚠️ Language Manager 未加载，跳过多语言初始化');
            return;
        }

        // 获取保存的语言设置
        const result = await chrome.storage.local.get('aisp_config');
        const config = result.aisp_config || {};
        const savedLanguage = config.currentLanguage || 'zh_CN';

        // 初始化语言管理器
        await lang_initialize(savedLanguage);

        // 添加语言变更监听器
        lang_addChangeListener((newLang, oldLang) => {
            console.log(`🌐 语言已切换: ${oldLang} -> ${newLang}`);
        });

        console.log(`✅ 多语言支持初始化完成，当前语言: ${savedLanguage}`);
    } catch (error) {
        console.error('❌ 多语言初始化失败:', error);
    }
}

/**
 * @function aisp_updateUIFromConfig - 根据配置更新UI
 * @param {Object} config - 配置对象
 */
function aisp_updateUIFromConfig(config) {
    // 更新复选框状态
    const autoAnalysisCheckbox = document.getElementById('aisp-auto-analysis');
    if (autoAnalysisCheckbox) {
        autoAnalysisCheckbox.checked = config.autoAnalysis || false;
    }
    
    const templateEnabledCheckbox = document.getElementById('aisp-template-enabled');
    if (templateEnabledCheckbox) {
        templateEnabledCheckbox.checked = config.templateEnabled !== false;
    }
    
    // 更新语言选择
    const languageSelect = document.getElementById('aisp-language-select');
    if (languageSelect && config.currentLanguage) {
        languageSelect.value = config.currentLanguage;
    }
}

/**
 * @function aisp_updateStatusInfo - 更新状态信息
 */
async function aisp_updateStatusInfo() {
    try {
        // 检查硬编码API密钥状态
        let isApiConfigured = false;
        if (typeof getApiKeyStatus === 'function') {
            const keyStatus = getApiKeyStatus();
            isApiConfigured = keyStatus.gemini.available;
        }

        const result = await chrome.storage.local.get('aisp_config');
        const config = result.aisp_config || {};

        // 更新AI状态
        const aiStatus = document.getElementById('aisp-ai-status');
        if (aiStatus) {
            const statusText = isApiConfigured
                ? (typeof lang_getPopupText === 'function' ? lang_getPopupText('configured') : '已配置')
                : (typeof lang_getPopupText === 'function' ? lang_getPopupText('notConfigured') : '未配置');

            aiStatus.textContent = statusText;
            aiStatus.className = `aisp-status-value ${isApiConfigured ? 'status-ok' : 'status-warning'}`;
        }

        // 更新模板状态
        const templateStatus = document.getElementById('aisp-template-status');
        if (templateStatus) {
            const isEnabled = config.templateEnabled !== false;
            const statusText = isEnabled
                ? (typeof lang_getPopupText === 'function' ? lang_getPopupText('enabled') : '已启用')
                : (typeof lang_getPopupText === 'function' ? lang_getPopupText('disabled') : '已禁用');

            templateStatus.textContent = statusText;
            templateStatus.className = `aisp-status-value ${isEnabled ? 'status-ok' : 'status-disabled'}`;
        }

        // 更新同步状态
        const syncStatus = document.getElementById('aisp-sync-status');
        if (syncStatus) {
            const isConnected = config.googleDriveEnabled;
            const statusText = isConnected
                ? (typeof lang_getPopupText === 'function' ? lang_getPopupText('connected') : '已连接')
                : (typeof lang_getPopupText === 'function' ? lang_getPopupText('disconnected') : '未连接');

            syncStatus.textContent = statusText;
            syncStatus.className = `aisp-status-value ${isConnected ? 'status-ok' : 'status-disabled'}`;
        }
    } catch (error) {
        console.error('❌ 更新状态信息失败:', error);
    }
}
// #endregion

// #region 事件监听器
/**
 * @function aisp_setupEventListeners - 设置事件监听器
 */
function aisp_setupEventListeners() {
    // 打开侧边栏按钮
    const openSidepanelBtn = document.getElementById('aisp-open-sidepanel');
    if (openSidepanelBtn) {
        openSidepanelBtn.addEventListener('click', aisp_openSidePanel);
    }
    
    // 查看分析结果按钮
    const viewAnalysisBtn = document.getElementById('aisp-view-analysis');
    if (viewAnalysisBtn) {
        viewAnalysisBtn.addEventListener('click', aisp_viewAnalysis);
    }
    
    // 自动分析复选框
    const autoAnalysisCheckbox = document.getElementById('aisp-auto-analysis');
    if (autoAnalysisCheckbox) {
        autoAnalysisCheckbox.addEventListener('change', aisp_toggleAutoAnalysis);
    }
    
    // 模板启用复选框
    const templateEnabledCheckbox = document.getElementById('aisp-template-enabled');
    if (templateEnabledCheckbox) {
        templateEnabledCheckbox.addEventListener('change', aisp_toggleTemplateEnabled);
    }
    
    // 语言选择
    const languageSelect = document.getElementById('aisp-language-select');
    if (languageSelect) {
        languageSelect.addEventListener('change', aisp_changeLanguage);
    }
    
    // 详细设置链接
    const openSettingsLink = document.getElementById('aisp-open-settings');
    if (openSettingsLink) {
        openSettingsLink.addEventListener('click', aisp_openSettings);
    }
    
    // 帮助链接
    const openHelpLink = document.getElementById('aisp-open-help');
    if (openHelpLink) {
        openHelpLink.addEventListener('click', aisp_openHelp);
    }
}
// #endregion

// #region 功能函数
/**
 * @function aisp_openSidePanel - 打开侧边栏
 */
async function aisp_openSidePanel() {
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        
        await chrome.sidePanel.setOptions({
            tabId: tab.id,
            enabled: true,
            path: 'src/sidepanel/sidepanel.html'
        });
        
        // 关闭弹窗
        window.close();
    } catch (error) {
        console.error('打开侧边栏失败:', error);
        aisp_showMessage('打开侧边栏失败，请重试', 'error');
    }
}

/**
 * @function aisp_viewAnalysis - 查看分析结果
 * @description 打开侧边栏查看自动分析结果
 */
async function aisp_viewAnalysis() {
    try {
        // 直接打开侧边栏，自动分析会在侧边栏初始化时执行
        await aisp_openSidePanel();

        aisp_showMessage('正在打开侧边栏...', 'success');

        // 延迟关闭弹窗
        setTimeout(() => window.close(), 500);
    } catch (error) {
        console.error('打开侧边栏失败:', error);
        aisp_showMessage('打开侧边栏失败，请重试', 'error');
    }
}

/**
 * @function aisp_toggleAutoAnalysis - 切换自动分析设置
 * @param {Event} event - 事件对象
 */
async function aisp_toggleAutoAnalysis(event) {
    try {
        const enabled = event.target.checked;
        
        const result = await chrome.storage.local.get('aisp_config');
        const config = result.aisp_config || {};
        config.autoAnalysis = enabled;
        
        await chrome.storage.local.set({ 'aisp_config': config });
        
        aisp_showMessage(`自动分析已${enabled ? '启用' : '禁用'}`, 'success');
    } catch (error) {
        console.error('切换自动分析设置失败:', error);
        aisp_showMessage('设置保存失败', 'error');
    }
}

/**
 * @function aisp_toggleTemplateEnabled - 切换模板启用设置
 * @param {Event} event - 事件对象
 */
async function aisp_toggleTemplateEnabled(event) {
    try {
        const enabled = event.target.checked;
        
        const result = await chrome.storage.local.get('aisp_config');
        const config = result.aisp_config || {};
        config.templateEnabled = enabled;
        
        await chrome.storage.local.set({ 'aisp_config': config });
        
        // 更新状态显示
        await aisp_updateStatusInfo();
        
        aisp_showMessage(`快捷模板已${enabled ? '启用' : '禁用'}`, 'success');
    } catch (error) {
        console.error('切换模板设置失败:', error);
        aisp_showMessage('设置保存失败', 'error');
    }
}

/**
 * @function aisp_changeLanguage - 更改语言设置
 * @param {Event} event - 事件对象
 */
async function aisp_changeLanguage(event) {
    try {
        const language = event.target.value;

        // 使用语言管理器切换语言
        if (typeof lang_setCurrentLanguage === 'function') {
            const success = await lang_setCurrentLanguage(language);
            if (!success) {
                throw new Error('语言切换失败');
            }
        }

        // 保存语言设置到配置
        const result = await chrome.storage.local.get('aisp_config');
        const config = result.aisp_config || {};
        config.currentLanguage = language;

        await chrome.storage.local.set({ 'aisp_config': config });

        // 更新状态信息（使用新语言）
        await aisp_updateStatusInfo();

        const successMessage = typeof lang_getSuccessText === 'function'
            ? lang_getSuccessText('languageSaved')
            : '语言设置已保存';
        aisp_showMessage(successMessage, 'success');

    } catch (error) {
        console.error('❌ 更改语言设置失败:', error);

        // 恢复到之前的选择
        const result = await chrome.storage.local.get('aisp_config');
        const config = result.aisp_config || {};
        if (config.currentLanguage) {
            event.target.value = config.currentLanguage;
        }

        const errorMessage = typeof lang_getErrorText === 'function'
            ? lang_getErrorText('languageChangeFailed')
            : '语言切换失败';
        aisp_showMessage(errorMessage, 'error');
    }
}

/**
 * @function aisp_openSettings - 打开详细设置页面
 * @param {Event} event - 事件对象
 */
function aisp_openSettings(event) {
    event.preventDefault();
    
    chrome.tabs.create({
        url: chrome.runtime.getURL('src/settings/settings.html')
    });
    
    window.close();
}

/**
 * @function aisp_openHelp - 打开帮助页面
 * @param {Event} event - 事件对象
 */
function aisp_openHelp(event) {
    event.preventDefault();
    
    chrome.tabs.create({
        url: chrome.runtime.getURL('src/help/help.html')
    });
    
    window.close();
}

/**
 * @function aisp_showMessage - 显示消息提示
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 ('success', 'error', 'warning')
 */
function aisp_showMessage(message, type = 'info') {
    // 创建消息元素
    const messageEl = document.createElement('div');
    messageEl.className = `aisp-message aisp-message-${type}`;
    messageEl.textContent = message;
    
    // 添加到页面
    document.body.appendChild(messageEl);
    
    // 自动移除
    setTimeout(() => {
        if (messageEl.parentNode) {
            messageEl.parentNode.removeChild(messageEl);
        }
    }, 3000);
}
// #endregion

console.log('AI Side Panel Popup Script 已加载');
