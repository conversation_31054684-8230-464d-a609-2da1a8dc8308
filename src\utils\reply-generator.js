/**
 * @file AI Side Panel 智能回复建议系统
 * @description 基于客服视角生成多种回复建议，支持多语言，提供一键复制功能
 */

// #region 全局变量
let rg_apiManager = null;
let rg_languageManager = null;
let rg_currentLanguage = 'zh_CN';
let rg_isInitialized = false;
let rg_replyCache = new Map();
let rg_generationHistory = [];

// 回复类型配置
const REPLY_TYPES = {
    HELPFUL: 'helpful',      // 有帮助的回复
    PROFESSIONAL: 'professional', // 专业的回复
    FRIENDLY: 'friendly'     // 友好的回复
};

// 语言配置
const SUPPORTED_LANGUAGES = {
    'zh_CN': '中文',
    'en_US': 'English'
};

// 客服回复模板配置
const CUSTOMER_SERVICE_TEMPLATES = {
    zh_CN: {
        helpful: {
            greeting: "感谢您的咨询！",
            closing: "如有其他问题，请随时联系我们。",
            tone: "helpful_chinese"
        },
        professional: {
            greeting: "您好，",
            closing: "祝您工作顺利！",
            tone: "professional_chinese"
        },
        friendly: {
            greeting: "您好！很高兴为您服务～",
            closing: "希望能帮到您！😊",
            tone: "friendly_chinese"
        }
    },
    en_US: {
        helpful: {
            greeting: "Thank you for your inquiry!",
            closing: "Please feel free to contact us if you have any other questions.",
            tone: "helpful_english"
        },
        professional: {
            greeting: "Dear valued customer,",
            closing: "Best regards,",
            tone: "professional_english"
        },
        friendly: {
            greeting: "Hi there! Happy to help! 😊",
            closing: "Hope this helps!",
            tone: "friendly_english"
        }
    }
};
// #endregion

// #region 初始化
/**
 * @function rg_initialize - 初始化回复生成器
 * @description 初始化API管理器和语言管理器
 * @returns {Promise<boolean>} 是否初始化成功
 */
async function rg_initialize() {
    if (rg_isInitialized) return true;
    
    try {
        console.log('🤖 智能回复生成器初始化中...');
        
        // 获取API管理器
        if (typeof getAPIManager === 'function') {
            rg_apiManager = getAPIManager();
            if (!rg_apiManager.isReady()) {
                await rg_apiManager.initialize();
            }
        } else {
            console.warn('⚠️ API管理器不可用，回复生成功能将受限');
        }
        
        // 获取语言管理器
        if (typeof lang_getCurrentLanguage === 'function') {
            rg_currentLanguage = lang_getCurrentLanguage();
        }
        
        // 添加语言变更监听器
        if (typeof lang_addChangeListener === 'function') {
            lang_addChangeListener((newLang, oldLang) => {
                rg_currentLanguage = newLang;
                console.log(`🌐 回复生成器语言已切换: ${oldLang} -> ${newLang}`);
            });
        }
        
        rg_isInitialized = true;
        console.log('✅ 智能回复生成器初始化完成');
        return true;
        
    } catch (error) {
        console.error('❌ 智能回复生成器初始化失败:', error);
        return false;
    }
}

/**
 * @function rg_isReady - 检查回复生成器是否就绪
 * @returns {boolean} 是否就绪
 */
function rg_isReady() {
    return rg_isInitialized && rg_apiManager && rg_apiManager.isReady();
}
// #endregion

// #region 核心回复生成功能
/**
 * @function rg_generateReplies - 生成智能回复建议
 * @description 基于输入内容和上下文生成三种不同风格的回复建议
 * @param {string} inputContent - 用户输入的内容
 * @param {Object} context - 上下文信息
 * @param {Object} options - 生成选项
 * @returns {Promise<Array>} 回复建议数组
 */
async function rg_generateReplies(inputContent, context = {}, options = {}) {
    try {
        if (!rg_isReady()) {
            throw new Error('回复生成器未就绪，请检查API配置');
        }
        
        // 参数验证
        if (!inputContent || typeof inputContent !== 'string') {
            throw new Error('输入内容不能为空');
        }
        
        // 生成缓存键
        const cacheKey = rg_generateCacheKey(inputContent, context, options);
        
        // 检查缓存
        if (rg_replyCache.has(cacheKey)) {
            console.log('📋 使用缓存的回复建议');
            return rg_replyCache.get(cacheKey);
        }
        
        console.log('🤖 正在生成智能回复建议...');
        
        // 准备生成参数
        const generateOptions = {
            language: options.language || rg_currentLanguage,
            maxLength: options.maxLength || 200,
            includeEmoji: options.includeEmoji !== false,
            customerServiceMode: true,
            ...options
        };
        
        // 并行生成三种类型的回复
        const replyPromises = [
            rg_generateSingleReply(inputContent, context, REPLY_TYPES.HELPFUL, generateOptions),
            rg_generateSingleReply(inputContent, context, REPLY_TYPES.PROFESSIONAL, generateOptions),
            rg_generateSingleReply(inputContent, context, REPLY_TYPES.FRIENDLY, generateOptions)
        ];
        
        const replies = await Promise.all(replyPromises);
        
        // 格式化回复结果
        const formattedReplies = replies.map((reply, index) => ({
            id: `reply_${Date.now()}_${index}`,
            type: Object.values(REPLY_TYPES)[index],
            content: reply.content,
            confidence: reply.confidence || 0.8,
            language: generateOptions.language,
            timestamp: Date.now(),
            metadata: {
                inputLength: inputContent.length,
                generationTime: reply.generationTime || 0,
                template: reply.template || 'default'
            }
        }));
        
        // 缓存结果
        rg_replyCache.set(cacheKey, formattedReplies);
        
        // 记录生成历史
        rg_generationHistory.push({
            input: inputContent,
            context: context,
            replies: formattedReplies,
            timestamp: Date.now()
        });
        
        // 限制历史记录数量
        if (rg_generationHistory.length > 50) {
            rg_generationHistory = rg_generationHistory.slice(-50);
        }
        
        console.log('✅ 智能回复建议生成完成');
        return formattedReplies;
        
    } catch (error) {
        console.error('❌ 生成回复建议失败:', error);
        
        // 返回后备回复
        return rg_getFallbackReplies(inputContent, options.language || rg_currentLanguage);
    }
}

/**
 * @function rg_generateSingleReply - 生成单个回复
 * @description 生成指定类型的单个回复建议
 * @param {string} inputContent - 输入内容
 * @param {Object} context - 上下文信息
 * @param {string} replyType - 回复类型
 * @param {Object} options - 生成选项
 * @returns {Promise<Object>} 回复对象
 */
async function rg_generateSingleReply(inputContent, context, replyType, options) {
    const startTime = Date.now();

    try {
        // 获取语言模板
        const language = options.language || rg_currentLanguage;
        const template = CUSTOMER_SERVICE_TEMPLATES[language]?.[replyType] ||
                        CUSTOMER_SERVICE_TEMPLATES['zh_CN'][replyType];

        // 构建提示词
        const prompt = rg_buildPrompt(inputContent, context, replyType, template, options);

        // 调用API生成回复
        const apiResponse = await rg_apiManager.generateReplies(prompt, {
            type: replyType,
            tone: template.tone,
            language: language,
            maxLength: options.maxLength,
            temperature: rg_getTemperatureByType(replyType)
        });

        // 处理API响应
        let replyContent = '';
        if (apiResponse && apiResponse.length > 0) {
            replyContent = apiResponse[0];
        } else {
            throw new Error('API返回空回复');
        }

        // 后处理回复内容
        replyContent = rg_postProcessReply(replyContent, template, options);

        return {
            content: replyContent,
            confidence: 0.85,
            generationTime: Date.now() - startTime,
            template: template.tone
        };

    } catch (error) {
        console.error(`生成${replyType}回复失败:`, error);

        // 返回模板回复
        return rg_generateTemplateReply(inputContent, replyType, options.language);
    }
}

/**
 * @function rg_buildPrompt - 构建AI提示词
 * @description 根据输入内容和回复类型构建合适的提示词
 * @param {string} inputContent - 输入内容
 * @param {Object} context - 上下文信息
 * @param {string} replyType - 回复类型
 * @param {Object} template - 语言模板
 * @param {Object} options - 选项
 * @returns {string} 构建的提示词
 */
function rg_buildPrompt(inputContent, context, replyType, template, options) {
    const language = options.language || rg_currentLanguage;

    // 基础提示词模板
    const basePrompts = {
        zh_CN: {
            helpful: "作为专业客服，请对以下内容提供有帮助的回复。回复应该详细、准确，能够解决用户的问题。",
            professional: "作为专业客服，请对以下内容提供正式、专业的回复。回复应该严谨、礼貌，体现专业水准。",
            friendly: "作为友好客服，请对以下内容提供亲切、友好的回复。回复应该温暖、亲近，让用户感到舒适。"
        },
        en_US: {
            helpful: "As a professional customer service representative, please provide a helpful reply to the following content. The reply should be detailed, accurate, and solve the user's problem.",
            professional: "As a professional customer service representative, please provide a formal, professional reply to the following content. The reply should be rigorous, polite, and demonstrate professionalism.",
            friendly: "As a friendly customer service representative, please provide a warm, friendly reply to the following content. The reply should be warm, approachable, and make the user feel comfortable."
        }
    };

    const basePrompt = basePrompts[language]?.[replyType] || basePrompts['zh_CN'][replyType];

    // 构建完整提示词
    let fullPrompt = `${basePrompt}\n\n`;

    // 添加上下文信息
    if (context.pageTitle) {
        fullPrompt += `页面标题: ${context.pageTitle}\n`;
    }
    if (context.pageUrl) {
        fullPrompt += `页面URL: ${context.pageUrl}\n`;
    }
    if (context.previousMessages && context.previousMessages.length > 0) {
        fullPrompt += `对话历史: ${context.previousMessages.slice(-3).join('\n')}\n`;
    }

    fullPrompt += `\n用户内容: ${inputContent}\n\n`;

    // 添加格式要求
    const formatRequirements = {
        zh_CN: `请用${language === 'zh_CN' ? '中文' : language}回复，长度控制在${options.maxLength || 200}字以内。`,
        en_US: `Please reply in English, keeping the length within ${options.maxLength || 200} characters.`
    };

    fullPrompt += formatRequirements[language] || formatRequirements['zh_CN'];

    // 添加特殊要求
    if (options.includeEmoji) {
        const emojiRequirement = {
            zh_CN: '适当使用表情符号让回复更友好。',
            en_US: 'Use appropriate emojis to make the reply more friendly.'
        };
        fullPrompt += '\n' + (emojiRequirement[language] || emojiRequirement['zh_CN']);
    }

    return fullPrompt;
}

/**
 * @function rg_postProcessReply - 后处理回复内容
 * @description 对生成的回复进行后处理，添加问候语和结束语
 * @param {string} replyContent - 原始回复内容
 * @param {Object} template - 语言模板
 * @param {Object} options - 选项
 * @returns {string} 处理后的回复内容
 */
function rg_postProcessReply(replyContent, template, options) {
    // 清理回复内容
    let processedReply = replyContent.trim();

    // 移除可能的引号
    if ((processedReply.startsWith('"') && processedReply.endsWith('"')) ||
        (processedReply.startsWith("'") && processedReply.endsWith("'"))) {
        processedReply = processedReply.slice(1, -1);
    }

    // 添加问候语（如果回复中没有包含）
    if (template.greeting && !processedReply.includes(template.greeting.slice(0, 5))) {
        processedReply = template.greeting + ' ' + processedReply;
    }

    // 添加结束语（如果回复中没有包含）
    if (template.closing && !processedReply.includes(template.closing.slice(0, 5))) {
        processedReply = processedReply + ' ' + template.closing;
    }

    // 长度控制
    const maxLength = options.maxLength || 200;
    if (processedReply.length > maxLength) {
        // 智能截断，保留完整句子
        const sentences = processedReply.split(/[。！？.!?]/);
        let truncated = '';
        for (const sentence of sentences) {
            if ((truncated + sentence).length <= maxLength - 10) {
                truncated += sentence + (sentence.match(/[。！？.!?]/) ? '' : '。');
            } else {
                break;
            }
        }
        processedReply = truncated || processedReply.substring(0, maxLength - 3) + '...';
    }

    return processedReply;
}

/**
 * @function rg_getTemperatureByType - 根据回复类型获取温度参数
 * @description 不同类型的回复使用不同的创造性参数
 * @param {string} replyType - 回复类型
 * @returns {number} 温度参数
 */
function rg_getTemperatureByType(replyType) {
    const temperatureMap = {
        [REPLY_TYPES.HELPFUL]: 0.3,      // 有帮助的回复需要准确性
        [REPLY_TYPES.PROFESSIONAL]: 0.2,  // 专业回复需要严谨性
        [REPLY_TYPES.FRIENDLY]: 0.7       // 友好回复可以更有创造性
    };

    return temperatureMap[replyType] || 0.5;
}

/**
 * @function rg_generateTemplateReply - 生成模板回复
 * @description 当API调用失败时使用的后备模板回复
 * @param {string} inputContent - 输入内容
 * @param {string} replyType - 回复类型
 * @param {string} language - 语言
 * @returns {Object} 模板回复对象
 */
function rg_generateTemplateReply(inputContent, replyType, language) {
    const template = CUSTOMER_SERVICE_TEMPLATES[language]?.[replyType] ||
                    CUSTOMER_SERVICE_TEMPLATES['zh_CN'][replyType];

    const templateReplies = {
        zh_CN: {
            helpful: "我已收到您的消息，正在为您查找相关信息。请稍等片刻，我会尽快为您提供详细的解答。",
            professional: "感谢您的咨询。我们已收到您的问题，将在第一时间为您处理并回复。",
            friendly: "收到您的消息啦！我正在努力为您寻找最佳解决方案，请稍等一下哦～"
        },
        en_US: {
            helpful: "I have received your message and am looking for relevant information for you. Please wait a moment, I will provide you with a detailed answer as soon as possible.",
            professional: "Thank you for your inquiry. We have received your question and will process and respond to you as soon as possible.",
            friendly: "Got your message! I'm working hard to find the best solution for you, please wait a moment~"
        }
    };

    const baseReply = templateReplies[language]?.[replyType] || templateReplies['zh_CN'][replyType];
    const fullReply = template.greeting + ' ' + baseReply + ' ' + template.closing;

    return {
        content: fullReply,
        confidence: 0.6,
        generationTime: 0,
        template: 'fallback_' + template.tone
    };
}

/**
 * @function rg_generateCacheKey - 生成缓存键
 * @description 基于输入内容和选项生成缓存键
 * @param {string} inputContent - 输入内容
 * @param {Object} context - 上下文
 * @param {Object} options - 选项
 * @returns {string} 缓存键
 */
function rg_generateCacheKey(inputContent, context, options) {
    const keyData = {
        input: inputContent,
        language: options.language || rg_currentLanguage,
        maxLength: options.maxLength || 200,
        includeEmoji: options.includeEmoji !== false,
        pageUrl: context.pageUrl || ''
    };

    // 使用支持UTF-8的base64编码方法
    return rg_utf8ToBase64(JSON.stringify(keyData)).substring(0, 32);
}

/**
 * @function rg_utf8ToBase64 - UTF-8字符串转Base64
 * @description 支持中文和特殊字符的base64编码
 * @param {string} str - 要编码的字符串
 * @returns {string} Base64编码结果
 */
function rg_utf8ToBase64(str) {
    try {
        // 使用TextEncoder将UTF-8字符串转换为字节数组
        const encoder = new TextEncoder();
        const bytes = encoder.encode(str);

        // 将字节数组转换为二进制字符串
        let binaryString = '';
        for (let i = 0; i < bytes.length; i++) {
            binaryString += String.fromCharCode(bytes[i]);
        }

        // 使用btoa编码二进制字符串
        return btoa(binaryString);
    } catch (error) {
        console.warn('UTF-8 Base64编码失败，使用简化方法:', error);
        // 后备方案：使用简单的哈希算法
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash).toString(36);
    }
}

/**
 * @function rg_getFallbackReplies - 获取后备回复
 * @description 当所有生成方法都失败时使用的后备回复
 * @param {string} inputContent - 输入内容
 * @param {string} language - 语言
 * @returns {Array} 后备回复数组
 */
function rg_getFallbackReplies(inputContent, language) {
    const fallbackMessages = {
        zh_CN: {
            helpful: "感谢您的咨询！我已收到您的消息，正在为您查找相关信息。请稍等片刻，我会尽快为您提供详细的解答。如有其他问题，请随时联系我们。",
            professional: "您好，感谢您的咨询。我们已收到您的问题，将在第一时间为您处理并回复。祝您工作顺利！",
            friendly: "您好！很高兴为您服务～收到您的消息啦！我正在努力为您寻找最佳解决方案，请稍等一下哦～希望能帮到您！😊"
        },
        en_US: {
            helpful: "Thank you for your inquiry! I have received your message and am looking for relevant information for you. Please wait a moment, I will provide you with a detailed answer as soon as possible. Please feel free to contact us if you have any other questions.",
            professional: "Dear valued customer, Thank you for your inquiry. We have received your question and will process and respond to you as soon as possible. Best regards,",
            friendly: "Hi there! Happy to help! 😊 Got your message! I'm working hard to find the best solution for you, please wait a moment~ Hope this helps!"
        }
    };

    const messages = fallbackMessages[language] || fallbackMessages['zh_CN'];

    return Object.values(REPLY_TYPES).map((type, index) => ({
        id: `fallback_reply_${Date.now()}_${index}`,
        type: type,
        content: messages[type],
        confidence: 0.5,
        language: language,
        timestamp: Date.now(),
        metadata: {
            inputLength: inputContent.length,
            generationTime: 0,
            template: 'fallback'
        }
    }));
}
// #endregion

// #region 一键复制功能
/**
 * @function rg_copyReplyToClipboard - 复制回复到剪贴板
 * @description 将选中的回复复制到剪贴板
 * @param {string} replyContent - 回复内容
 * @returns {Promise<boolean>} 是否复制成功
 */
async function rg_copyReplyToClipboard(replyContent) {
    try {
        if (!replyContent || typeof replyContent !== 'string') {
            throw new Error('回复内容不能为空');
        }

        // 使用现代剪贴板API
        if (navigator.clipboard && navigator.clipboard.writeText) {
            await navigator.clipboard.writeText(replyContent);
            console.log('✅ 回复已复制到剪贴板');
            return true;
        }

        // 后备方案：使用传统方法
        const textArea = document.createElement('textarea');
        textArea.value = replyContent;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
            console.log('✅ 回复已复制到剪贴板（后备方案）');
            return true;
        } else {
            throw new Error('复制操作失败');
        }

    } catch (error) {
        console.error('❌ 复制回复失败:', error);
        return false;
    }
}

/**
 * @function rg_createCopyButton - 创建复制按钮
 * @description 创建一个复制按钮元素
 * @param {string} replyContent - 要复制的回复内容
 * @param {Object} options - 按钮选项
 * @returns {HTMLElement} 复制按钮元素
 */
function rg_createCopyButton(replyContent, options = {}) {
    const {
        className = 'rg-copy-btn',
        text = '复制',
        successText = '已复制',
        errorText = '复制失败',
        showToast = true
    } = options;

    const button = document.createElement('button');
    button.className = className;
    button.textContent = text;
    button.type = 'button';

    button.addEventListener('click', async (event) => {
        event.preventDefault();
        event.stopPropagation();

        const originalText = button.textContent;
        button.disabled = true;

        try {
            const success = await rg_copyReplyToClipboard(replyContent);

            if (success) {
                button.textContent = successText;
                button.classList.add('success');

                if (showToast) {
                    rg_showCopyToast('回复已复制到剪贴板', 'success');
                }
            } else {
                throw new Error('复制失败');
            }

        } catch (error) {
            button.textContent = errorText;
            button.classList.add('error');

            if (showToast) {
                rg_showCopyToast('复制失败，请重试', 'error');
            }
        }

        // 恢复按钮状态
        setTimeout(() => {
            button.textContent = originalText;
            button.disabled = false;
            button.classList.remove('success', 'error');
        }, 2000);
    });

    return button;
}

/**
 * @function rg_showCopyToast - 显示复制提示
 * @description 显示复制操作的提示消息
 * @param {string} message - 提示消息
 * @param {string} type - 提示类型
 */
function rg_showCopyToast(message, type = 'info') {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = `rg-copy-toast rg-copy-toast-${type}`;
    toast.textContent = message;

    // 添加样式
    Object.assign(toast.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '12px 16px',
        borderRadius: '8px',
        color: 'white',
        fontSize: '14px',
        fontWeight: '500',
        zIndex: '10000',
        opacity: '0',
        transform: 'translateY(-20px)',
        transition: 'all 0.3s ease',
        backgroundColor: type === 'success' ? '#10B981' : type === 'error' ? '#EF4444' : '#6B7280'
    });

    // 添加到页面
    document.body.appendChild(toast);

    // 显示动画
    requestAnimationFrame(() => {
        toast.style.opacity = '1';
        toast.style.transform = 'translateY(0)';
    });

    // 自动移除
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transform = 'translateY(-20px)';

        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}
// #endregion

// #region 工具函数
/**
 * @function rg_getReplyTypeLabel - 获取回复类型标签
 * @description 获取回复类型的显示标签
 * @param {string} replyType - 回复类型
 * @param {string} language - 语言
 * @returns {string} 类型标签
 */
function rg_getReplyTypeLabel(replyType, language = rg_currentLanguage) {
    const labels = {
        zh_CN: {
            [REPLY_TYPES.HELPFUL]: '有帮助的',
            [REPLY_TYPES.PROFESSIONAL]: '专业的',
            [REPLY_TYPES.FRIENDLY]: '友好的'
        },
        en_US: {
            [REPLY_TYPES.HELPFUL]: 'Helpful',
            [REPLY_TYPES.PROFESSIONAL]: 'Professional',
            [REPLY_TYPES.FRIENDLY]: 'Friendly'
        }
    };

    return labels[language]?.[replyType] || labels['zh_CN'][replyType] || replyType;
}

/**
 * @function rg_clearCache - 清空回复缓存
 * @description 清空所有缓存的回复
 */
function rg_clearCache() {
    rg_replyCache.clear();
    console.log('🗑️ 回复缓存已清空');
}

/**
 * @function rg_getGenerationHistory - 获取生成历史
 * @description 获取回复生成历史记录
 * @param {number} limit - 限制数量
 * @returns {Array} 历史记录数组
 */
function rg_getGenerationHistory(limit = 10) {
    return rg_generationHistory.slice(-limit);
}

/**
 * @function rg_getStatistics - 获取统计信息
 * @description 获取回复生成器的统计信息
 * @returns {Object} 统计信息
 */
function rg_getStatistics() {
    const totalGenerations = rg_generationHistory.length;
    const cacheSize = rg_replyCache.size;

    // 计算平均生成时间
    let totalTime = 0;
    let validTimeCount = 0;

    rg_generationHistory.forEach(record => {
        record.replies.forEach(reply => {
            if (reply.metadata.generationTime > 0) {
                totalTime += reply.metadata.generationTime;
                validTimeCount++;
            }
        });
    });

    const averageTime = validTimeCount > 0 ? totalTime / validTimeCount : 0;

    // 统计语言使用情况
    const languageStats = {};
    rg_generationHistory.forEach(record => {
        record.replies.forEach(reply => {
            const lang = reply.language;
            languageStats[lang] = (languageStats[lang] || 0) + 1;
        });
    });

    return {
        totalGenerations,
        cacheSize,
        averageGenerationTime: Math.round(averageTime),
        languageStats,
        isReady: rg_isReady(),
        currentLanguage: rg_currentLanguage
    };
}

/**
 * @function getReplyGenerator - 获取回复生成器实例
 * @description 获取全局回复生成器实例
 * @returns {Object} 回复生成器API对象
 */
function getReplyGenerator() {
    return {
        // 核心功能
        initialize: rg_initialize,
        isReady: rg_isReady,
        generateReplies: rg_generateReplies,

        // 复制功能
        copyToClipboard: rg_copyReplyToClipboard,
        createCopyButton: rg_createCopyButton,

        // 工具函数
        getTypeLabel: rg_getReplyTypeLabel,
        clearCache: rg_clearCache,
        getHistory: rg_getGenerationHistory,
        getStatistics: rg_getStatistics,

        // 常量
        REPLY_TYPES,
        SUPPORTED_LANGUAGES
    };
}
// #endregion

console.log('🤖 AI Side Panel 智能回复生成器已加载');
