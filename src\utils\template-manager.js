/**
 * @file AI Side Panel 快捷回复模板管理器
 * @description 管理快捷回复模板的增删改查，支持分类、搜索、导入导出等功能
 */

// #region 全局变量
let tm_templates = [];
let tm_categories = [];
let tm_isInitialized = false;
let tm_currentLanguage = 'zh_CN';
let tm_storageKey = 'aisp_templates';
let tm_categoriesKey = 'aisp_template_categories';

// 默认模板数据
const DEFAULT_TEMPLATES = {
    zh_CN: [
        {
            id: 'greeting_1',
            title: '友好问候',
            content: '您好！很高兴为您服务，请问有什么可以帮助您的吗？',
            category: '问候',
            language: 'zh_CN',
            isDefault: true,
            createdAt: Date.now(),
            updatedAt: Date.now()
        },
        {
            id: 'greeting_2',
            title: '专业问候',
            content: '您好，感谢您的咨询。我是客服代表，很高兴为您提供帮助。',
            category: '问候',
            language: 'zh_CN',
            isDefault: true,
            createdAt: Date.now(),
            updatedAt: Date.now()
        },
        {
            id: 'thanks_1',
            title: '感谢回复',
            content: '非常感谢您的反馈！我们会认真对待您的建议。',
            category: '感谢',
            language: 'zh_CN',
            isDefault: true,
            createdAt: Date.now(),
            updatedAt: Date.now()
        },
        {
            id: 'apology_1',
            title: '道歉回复',
            content: '非常抱歉给您带来不便，我们会尽快为您解决这个问题。',
            category: '道歉',
            language: 'zh_CN',
            isDefault: true,
            createdAt: Date.now(),
            updatedAt: Date.now()
        },
        {
            id: 'inquiry_1',
            title: '信息询问',
            content: '为了更好地为您服务，请您提供一些详细信息：',
            category: '询问',
            language: 'zh_CN',
            isDefault: true,
            createdAt: Date.now(),
            updatedAt: Date.now()
        },
        {
            id: 'confirm_1',
            title: '确认回复',
            content: '好的，我已经记录了您的需求，我们会在24小时内给您回复。',
            category: '确认',
            language: 'zh_CN',
            isDefault: true,
            createdAt: Date.now(),
            updatedAt: Date.now()
        },
        {
            id: 'closing_1',
            title: '结束对话',
            content: '如果您还有其他问题，请随时联系我们。祝您生活愉快！',
            category: '结束',
            language: 'zh_CN',
            isDefault: true,
            createdAt: Date.now(),
            updatedAt: Date.now()
        }
    ],
    en_US: [
        {
            id: 'greeting_en_1',
            title: 'Friendly Greeting',
            content: 'Hello! I\'m happy to assist you. How can I help you today?',
            category: 'Greeting',
            language: 'en_US',
            isDefault: true,
            createdAt: Date.now(),
            updatedAt: Date.now()
        },
        {
            id: 'greeting_en_2',
            title: 'Professional Greeting',
            content: 'Good day! Thank you for contacting us. I\'m a customer service representative and I\'m here to help.',
            category: 'Greeting',
            language: 'en_US',
            isDefault: true,
            createdAt: Date.now(),
            updatedAt: Date.now()
        },
        {
            id: 'thanks_en_1',
            title: 'Thank You Reply',
            content: 'Thank you very much for your feedback! We take your suggestions seriously.',
            category: 'Thanks',
            language: 'en_US',
            isDefault: true,
            createdAt: Date.now(),
            updatedAt: Date.now()
        },
        {
            id: 'apology_en_1',
            title: 'Apology Reply',
            content: 'I sincerely apologize for any inconvenience caused. We will resolve this issue as soon as possible.',
            category: 'Apology',
            language: 'en_US',
            isDefault: true,
            createdAt: Date.now(),
            updatedAt: Date.now()
        },
        {
            id: 'inquiry_en_1',
            title: 'Information Inquiry',
            content: 'To better assist you, could you please provide some additional details:',
            category: 'Inquiry',
            language: 'en_US',
            isDefault: true,
            createdAt: Date.now(),
            updatedAt: Date.now()
        },
        {
            id: 'confirm_en_1',
            title: 'Confirmation Reply',
            content: 'Understood. I have recorded your request and we will get back to you within 24 hours.',
            category: 'Confirmation',
            language: 'en_US',
            isDefault: true,
            createdAt: Date.now(),
            updatedAt: Date.now()
        },
        {
            id: 'closing_en_1',
            title: 'Closing',
            content: 'If you have any other questions, please don\'t hesitate to contact us. Have a great day!',
            category: 'Closing',
            language: 'en_US',
            isDefault: true,
            createdAt: Date.now(),
            updatedAt: Date.now()
        }
    ]
};

// 默认分类
const DEFAULT_CATEGORIES = {
    zh_CN: ['问候', '感谢', '道歉', '询问', '确认', '结束'],
    en_US: ['Greeting', 'Thanks', 'Apology', 'Inquiry', 'Confirmation', 'Closing']
};
// #endregion

// #region 初始化
/**
 * @function tm_initialize - 初始化模板管理器
 * @description 初始化模板管理器，加载存储的模板数据
 * @returns {Promise<boolean>} 是否初始化成功
 */
async function tm_initialize() {
    if (tm_isInitialized) return true;
    
    try {
        console.log('📋 模板管理器初始化中...');
        
        // 获取当前语言
        if (typeof lang_getCurrentLanguage === 'function') {
            tm_currentLanguage = lang_getCurrentLanguage();
        }
        
        // 加载模板数据
        await tm_loadTemplates();
        
        // 加载分类数据
        await tm_loadCategories();
        
        // 添加语言变更监听器
        if (typeof lang_addChangeListener === 'function') {
            lang_addChangeListener((newLang, oldLang) => {
                tm_currentLanguage = newLang;
                console.log(`📋 模板管理器语言已切换: ${oldLang} -> ${newLang}`);
            });
        }
        
        tm_isInitialized = true;
        console.log('✅ 模板管理器初始化完成');
        return true;
        
    } catch (error) {
        console.error('❌ 模板管理器初始化失败:', error);
        return false;
    }
}

/**
 * @function tm_loadTemplates - 加载模板数据
 * @description 从Chrome存储加载模板数据
 */
async function tm_loadTemplates() {
    try {
        const result = await chrome.storage.local.get(tm_storageKey);
        let storedTemplates = result[tm_storageKey] || [];
        
        // 如果没有存储的模板，使用默认模板
        if (storedTemplates.length === 0) {
            storedTemplates = Object.values(DEFAULT_TEMPLATES).flat();
            await tm_saveTemplates(storedTemplates);
        }
        
        tm_templates = storedTemplates;
        console.log(`📋 已加载 ${tm_templates.length} 个模板`);
        
    } catch (error) {
        console.error('加载模板数据失败:', error);
        // 使用默认模板作为后备
        tm_templates = Object.values(DEFAULT_TEMPLATES).flat();
    }
}

/**
 * @function tm_loadCategories - 加载分类数据
 * @description 从Chrome存储加载分类数据
 */
async function tm_loadCategories() {
    try {
        const result = await chrome.storage.local.get(tm_categoriesKey);
        let storedCategories = result[tm_categoriesKey] || {};
        
        // 如果没有存储的分类，使用默认分类
        if (Object.keys(storedCategories).length === 0) {
            storedCategories = DEFAULT_CATEGORIES;
            await tm_saveCategories(storedCategories);
        }
        
        tm_categories = storedCategories;
        console.log('📋 已加载分类数据');
        
    } catch (error) {
        console.error('加载分类数据失败:', error);
        // 使用默认分类作为后备
        tm_categories = DEFAULT_CATEGORIES;
    }
}

/**
 * @function tm_saveTemplates - 保存模板数据
 * @description 将模板数据保存到Chrome存储
 * @param {Array} templates - 模板数组
 */
async function tm_saveTemplates(templates = tm_templates) {
    try {
        await chrome.storage.local.set({ [tm_storageKey]: templates });
        console.log('📋 模板数据已保存');
    } catch (error) {
        console.error('保存模板数据失败:', error);
        throw error;
    }
}

/**
 * @function tm_saveCategories - 保存分类数据
 * @description 将分类数据保存到Chrome存储
 * @param {Object} categories - 分类对象
 */
async function tm_saveCategories(categories = tm_categories) {
    try {
        await chrome.storage.local.set({ [tm_categoriesKey]: categories });
        console.log('📋 分类数据已保存');
    } catch (error) {
        console.error('保存分类数据失败:', error);
        throw error;
    }
}
// #endregion

// #region 模板CRUD操作
/**
 * @function tm_getTemplates - 获取模板列表
 * @description 获取模板列表，支持筛选和排序
 * @param {Object} options - 查询选项
 * @returns {Promise<Array>} 模板数组
 */
async function tm_getTemplates(options = {}) {
    const {
        language = tm_currentLanguage,
        category = null,
        active = null,
        limit = null,
        sortBy = 'updatedAt',
        sortOrder = 'desc'
    } = options;

    let filteredTemplates = [...tm_templates];

    // 按语言筛选
    if (language) {
        filteredTemplates = filteredTemplates.filter(t => t.language === language);
    }

    // 按分类筛选
    if (category) {
        filteredTemplates = filteredTemplates.filter(t => t.category === category);
    }

    // 按激活状态筛选
    if (active !== null) {
        filteredTemplates = filteredTemplates.filter(t => t.active !== false);
    }

    // 排序
    filteredTemplates.sort((a, b) => {
        const aValue = a[sortBy] || 0;
        const bValue = b[sortBy] || 0;

        if (sortOrder === 'desc') {
            return bValue - aValue;
        } else {
            return aValue - bValue;
        }
    });

    // 限制数量
    if (limit && limit > 0) {
        filteredTemplates = filteredTemplates.slice(0, limit);
    }

    return filteredTemplates;
}

/**
 * @function tm_getTemplateById - 根据ID获取模板
 * @description 根据模板ID获取单个模板
 * @param {string} templateId - 模板ID
 * @returns {Object|null} 模板对象或null
 */
function tm_getTemplateById(templateId) {
    return tm_templates.find(t => t.id === templateId) || null;
}

/**
 * @function tm_searchTemplates - 搜索模板
 * @description 根据关键词搜索模板
 * @param {string} query - 搜索关键词
 * @param {Object} options - 搜索选项
 * @returns {Promise<Array>} 匹配的模板数组
 */
async function tm_searchTemplates(query, options = {}) {
    if (!query || !query.trim()) {
        return await tm_getTemplates(options);
    }

    const searchQuery = query.toLowerCase().trim();
    const {
        language = tm_currentLanguage,
        limit = null
    } = options;

    let results = tm_templates.filter(template => {
        // 语言筛选
        if (language && template.language !== language) {
            return false;
        }

        // 搜索标题和内容
        const titleMatch = template.title.toLowerCase().includes(searchQuery);
        const contentMatch = template.content.toLowerCase().includes(searchQuery);
        const categoryMatch = template.category && template.category.toLowerCase().includes(searchQuery);

        return titleMatch || contentMatch || categoryMatch;
    });

    // 按相关性排序
    results.sort((a, b) => {
        const aScore = tm_calculateRelevanceScore(a, searchQuery);
        const bScore = tm_calculateRelevanceScore(b, searchQuery);
        return bScore - aScore;
    });

    // 限制数量
    if (limit && limit > 0) {
        results = results.slice(0, limit);
    }

    return results;
}

/**
 * @function tm_calculateRelevanceScore - 计算相关性分数
 * @description 计算模板与搜索关键词的相关性分数
 * @param {Object} template - 模板对象
 * @param {string} query - 搜索关键词
 * @returns {number} 相关性分数
 */
function tm_calculateRelevanceScore(template, query) {
    let score = 0;
    const queryLower = query.toLowerCase();

    // 标题匹配权重更高
    if (template.title.toLowerCase().includes(queryLower)) {
        score += 10;
        // 完全匹配加分
        if (template.title.toLowerCase() === queryLower) {
            score += 20;
        }
    }

    // 内容匹配
    if (template.content.toLowerCase().includes(queryLower)) {
        score += 5;
    }

    // 分类匹配
    if (template.category && template.category.toLowerCase().includes(queryLower)) {
        score += 8;
    }

    // 最近使用的模板加分
    const daysSinceUpdate = (Date.now() - (template.updatedAt || 0)) / (1000 * 60 * 60 * 24);
    if (daysSinceUpdate < 7) {
        score += 3;
    }

    return score;
}

/**
 * @function tm_createTemplate - 创建新模板
 * @description 创建一个新的模板
 * @param {Object} templateData - 模板数据
 * @returns {Promise<Object>} 创建的模板对象
 */
async function tm_createTemplate(templateData) {
    const {
        title,
        content,
        category = '',
        language = tm_currentLanguage
    } = templateData;

    // 验证必需字段
    if (!title || !title.trim()) {
        throw new Error('模板标题不能为空');
    }

    if (!content || !content.trim()) {
        throw new Error('模板内容不能为空');
    }

    // 创建新模板
    const newTemplate = {
        id: tm_generateTemplateId(),
        title: title.trim(),
        content: content.trim(),
        category: category.trim(),
        language: language,
        isDefault: false,
        active: true,
        createdAt: Date.now(),
        updatedAt: Date.now()
    };

    // 添加到模板列表
    tm_templates.push(newTemplate);

    // 保存到存储
    await tm_saveTemplates();

    console.log('📋 新模板已创建:', newTemplate.title);
    return newTemplate;
}

/**
 * @function tm_updateTemplate - 更新模板
 * @description 更新现有模板
 * @param {string} templateId - 模板ID
 * @param {Object} updateData - 更新数据
 * @returns {Promise<Object>} 更新后的模板对象
 */
async function tm_updateTemplate(templateId, updateData) {
    const templateIndex = tm_templates.findIndex(t => t.id === templateId);

    if (templateIndex === -1) {
        throw new Error('模板不存在');
    }

    const template = tm_templates[templateIndex];

    // 不允许修改默认模板的核心属性
    if (template.isDefault) {
        const allowedFields = ['active'];
        const hasDisallowedFields = Object.keys(updateData).some(key => !allowedFields.includes(key));

        if (hasDisallowedFields) {
            throw new Error('不能修改默认模板的内容');
        }
    }

    // 验证更新数据
    if (updateData.title !== undefined && !updateData.title.trim()) {
        throw new Error('模板标题不能为空');
    }

    if (updateData.content !== undefined && !updateData.content.trim()) {
        throw new Error('模板内容不能为空');
    }

    // 更新模板
    const updatedTemplate = {
        ...template,
        ...updateData,
        updatedAt: Date.now()
    };

    tm_templates[templateIndex] = updatedTemplate;

    // 保存到存储
    await tm_saveTemplates();

    console.log('📋 模板已更新:', updatedTemplate.title);
    return updatedTemplate;
}

/**
 * @function tm_deleteTemplate - 删除模板
 * @description 删除指定的模板
 * @param {string} templateId - 模板ID
 * @returns {Promise<boolean>} 是否删除成功
 */
async function tm_deleteTemplate(templateId) {
    const templateIndex = tm_templates.findIndex(t => t.id === templateId);

    if (templateIndex === -1) {
        throw new Error('模板不存在');
    }

    const template = tm_templates[templateIndex];

    // 不允许删除默认模板
    if (template.isDefault) {
        throw new Error('不能删除默认模板');
    }

    // 删除模板
    tm_templates.splice(templateIndex, 1);

    // 保存到存储
    await tm_saveTemplates();

    console.log('📋 模板已删除:', template.title);
    return true;
}

/**
 * @function tm_generateTemplateId - 生成模板ID
 * @description 生成唯一的模板ID
 * @returns {string} 模板ID
 */
function tm_generateTemplateId() {
    return 'template_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}
// #endregion

// #region 分类管理
/**
 * @function tm_getCategories - 获取分类列表
 * @description 获取指定语言的分类列表
 * @param {string} language - 语言代码
 * @returns {Array} 分类数组
 */
function tm_getCategories(language = tm_currentLanguage) {
    return tm_categories[language] || [];
}

/**
 * @function tm_addCategory - 添加分类
 * @description 为指定语言添加新分类
 * @param {string} categoryName - 分类名称
 * @param {string} language - 语言代码
 * @returns {Promise<boolean>} 是否添加成功
 */
async function tm_addCategory(categoryName, language = tm_currentLanguage) {
    if (!categoryName || !categoryName.trim()) {
        throw new Error('分类名称不能为空');
    }

    const trimmedName = categoryName.trim();

    if (!tm_categories[language]) {
        tm_categories[language] = [];
    }

    // 检查是否已存在
    if (tm_categories[language].includes(trimmedName)) {
        throw new Error('分类已存在');
    }

    // 添加分类
    tm_categories[language].push(trimmedName);

    // 保存到存储
    await tm_saveCategories();

    console.log('📋 新分类已添加:', trimmedName);
    return true;
}

/**
 * @function tm_removeCategory - 删除分类
 * @description 删除指定的分类
 * @param {string} categoryName - 分类名称
 * @param {string} language - 语言代码
 * @returns {Promise<boolean>} 是否删除成功
 */
async function tm_removeCategory(categoryName, language = tm_currentLanguage) {
    if (!tm_categories[language]) {
        throw new Error('分类不存在');
    }

    const categoryIndex = tm_categories[language].indexOf(categoryName);

    if (categoryIndex === -1) {
        throw new Error('分类不存在');
    }

    // 检查是否有模板使用此分类
    const templatesUsingCategory = tm_templates.filter(t =>
        t.language === language && t.category === categoryName
    );

    if (templatesUsingCategory.length > 0) {
        throw new Error(`无法删除分类，还有 ${templatesUsingCategory.length} 个模板在使用此分类`);
    }

    // 删除分类
    tm_categories[language].splice(categoryIndex, 1);

    // 保存到存储
    await tm_saveCategories();

    console.log('📋 分类已删除:', categoryName);
    return true;
}
// #endregion

// #region 导入导出功能
/**
 * @function tm_exportTemplates - 导出模板
 * @description 导出模板数据为JSON格式
 * @param {Object} options - 导出选项
 * @returns {string} JSON字符串
 */
function tm_exportTemplates(options = {}) {
    const {
        language = null,
        includeDefault = false
    } = options;

    let templatesToExport = [...tm_templates];

    // 按语言筛选
    if (language) {
        templatesToExport = templatesToExport.filter(t => t.language === language);
    }

    // 是否包含默认模板
    if (!includeDefault) {
        templatesToExport = templatesToExport.filter(t => !t.isDefault);
    }

    const exportData = {
        version: '1.0',
        exportTime: Date.now(),
        templates: templatesToExport,
        categories: tm_categories
    };

    return JSON.stringify(exportData, null, 2);
}

/**
 * @function tm_importTemplates - 导入模板
 * @description 从JSON数据导入模板
 * @param {string} jsonData - JSON数据字符串
 * @param {Object} options - 导入选项
 * @returns {Promise<Object>} 导入结果
 */
async function tm_importTemplates(jsonData, options = {}) {
    const {
        overwrite = false,
        mergeCategories = true
    } = options;

    try {
        const importData = JSON.parse(jsonData);

        if (!importData.templates || !Array.isArray(importData.templates)) {
            throw new Error('无效的模板数据格式');
        }

        let importedCount = 0;
        let skippedCount = 0;
        let updatedCount = 0;

        // 导入模板
        for (const template of importData.templates) {
            // 验证模板数据
            if (!template.id || !template.title || !template.content) {
                skippedCount++;
                continue;
            }

            const existingIndex = tm_templates.findIndex(t => t.id === template.id);

            if (existingIndex !== -1) {
                if (overwrite) {
                    // 更新现有模板
                    tm_templates[existingIndex] = {
                        ...template,
                        updatedAt: Date.now()
                    };
                    updatedCount++;
                } else {
                    skippedCount++;
                }
            } else {
                // 添加新模板
                tm_templates.push({
                    ...template,
                    createdAt: Date.now(),
                    updatedAt: Date.now()
                });
                importedCount++;
            }
        }

        // 导入分类
        if (importData.categories && mergeCategories) {
            for (const [language, categories] of Object.entries(importData.categories)) {
                if (!tm_categories[language]) {
                    tm_categories[language] = [];
                }

                for (const category of categories) {
                    if (!tm_categories[language].includes(category)) {
                        tm_categories[language].push(category);
                    }
                }
            }
        }

        // 保存数据
        await tm_saveTemplates();
        await tm_saveCategories();

        const result = {
            imported: importedCount,
            updated: updatedCount,
            skipped: skippedCount,
            total: importData.templates.length
        };

        console.log('📋 模板导入完成:', result);
        return result;

    } catch (error) {
        console.error('导入模板失败:', error);
        throw new Error('导入失败: ' + error.message);
    }
}
// #endregion

// #region 智能预测功能
/**
 * @function tm_predictTemplates - 智能预测模板
 * @description 根据输入内容智能预测可能需要的模板
 * @param {string} inputText - 输入文本
 * @param {Object} context - 上下文信息
 * @returns {Promise<Array>} 预测的模板数组
 */
async function tm_predictTemplates(inputText, context = {}) {
    if (!inputText || !inputText.trim()) {
        return [];
    }

    const input = inputText.toLowerCase().trim();
    const predictions = [];

    // 关键词匹配规则
    const keywordRules = {
        zh_CN: {
            '问候': ['你好', '您好', '早上好', '下午好', '晚上好', '欢迎'],
            '感谢': ['谢谢', '感谢', '多谢', '谢您'],
            '道歉': ['抱歉', '对不起', '不好意思', '很遗憾'],
            '询问': ['请问', '能否', '可以', '怎么', '如何', '什么'],
            '确认': ['好的', '明白', '收到', '了解', '确认'],
            '结束': ['再见', '拜拜', '结束', '完成', '谢谢您']
        },
        en_US: {
            'Greeting': ['hello', 'hi', 'good morning', 'good afternoon', 'welcome'],
            'Thanks': ['thank', 'thanks', 'appreciate'],
            'Apology': ['sorry', 'apologize', 'regret'],
            'Inquiry': ['can you', 'could you', 'how to', 'what is', 'please'],
            'Confirmation': ['ok', 'okay', 'understood', 'got it', 'confirm'],
            'Closing': ['goodbye', 'bye', 'see you', 'have a', 'thank you']
        }
    };

    const rules = keywordRules[tm_currentLanguage] || keywordRules['zh_CN'];

    // 计算每个分类的匹配分数
    const categoryScores = {};

    for (const [category, keywords] of Object.entries(rules)) {
        let score = 0;

        for (const keyword of keywords) {
            if (input.includes(keyword)) {
                score += keyword.length; // 长关键词权重更高
            }
        }

        if (score > 0) {
            categoryScores[category] = score;
        }
    }

    // 根据分数排序分类
    const sortedCategories = Object.entries(categoryScores)
        .sort(([,a], [,b]) => b - a)
        .map(([category]) => category);

    // 为每个匹配的分类获取模板
    for (const category of sortedCategories.slice(0, 3)) { // 最多3个分类
        const categoryTemplates = await tm_getTemplates({
            category: category,
            language: tm_currentLanguage,
            limit: 2 // 每个分类最多2个模板
        });

        predictions.push(...categoryTemplates);
    }

    // 如果没有匹配的分类，返回最近使用的模板
    if (predictions.length === 0) {
        const recentTemplates = await tm_getTemplates({
            language: tm_currentLanguage,
            limit: 5,
            sortBy: 'updatedAt',
            sortOrder: 'desc'
        });

        predictions.push(...recentTemplates);
    }

    // 去重并限制数量
    const uniquePredictions = predictions.filter((template, index, self) =>
        index === self.findIndex(t => t.id === template.id)
    ).slice(0, 5);

    return uniquePredictions;
}
// #endregion

// #region 工具函数
/**
 * @function tm_getStatistics - 获取统计信息
 * @description 获取模板管理器的统计信息
 * @returns {Object} 统计信息
 */
function tm_getStatistics() {
    const totalTemplates = tm_templates.length;
    const defaultTemplates = tm_templates.filter(t => t.isDefault).length;
    const customTemplates = totalTemplates - defaultTemplates;

    // 按语言统计
    const languageStats = {};
    tm_templates.forEach(template => {
        const lang = template.language;
        languageStats[lang] = (languageStats[lang] || 0) + 1;
    });

    // 按分类统计
    const categoryStats = {};
    tm_templates.forEach(template => {
        const category = template.category || '未分类';
        categoryStats[category] = (categoryStats[category] || 0) + 1;
    });

    return {
        totalTemplates,
        defaultTemplates,
        customTemplates,
        languageStats,
        categoryStats,
        totalCategories: Object.keys(tm_categories).reduce((sum, lang) =>
            sum + tm_categories[lang].length, 0
        ),
        isInitialized: tm_isInitialized,
        currentLanguage: tm_currentLanguage
    };
}

/**
 * @function tm_resetToDefaults - 重置为默认模板
 * @description 重置模板数据为默认状态
 * @returns {Promise<boolean>} 是否重置成功
 */
async function tm_resetToDefaults() {
    try {
        // 重置模板
        tm_templates = Object.values(DEFAULT_TEMPLATES).flat();

        // 重置分类
        tm_categories = { ...DEFAULT_CATEGORIES };

        // 保存到存储
        await tm_saveTemplates();
        await tm_saveCategories();

        console.log('📋 模板数据已重置为默认状态');
        return true;

    } catch (error) {
        console.error('重置模板数据失败:', error);
        throw error;
    }
}

/**
 * @function getTemplateManager - 获取模板管理器实例
 * @description 获取全局模板管理器实例
 * @returns {Object} 模板管理器API对象
 */
function getTemplateManager() {
    return {
        // 初始化
        initialize: tm_initialize,

        // 模板CRUD
        getTemplates: tm_getTemplates,
        getTemplateById: tm_getTemplateById,
        searchTemplates: tm_searchTemplates,
        createTemplate: tm_createTemplate,
        updateTemplate: tm_updateTemplate,
        deleteTemplate: tm_deleteTemplate,

        // 分类管理
        getCategories: tm_getCategories,
        addCategory: tm_addCategory,
        removeCategory: tm_removeCategory,

        // 导入导出
        exportTemplates: tm_exportTemplates,
        importTemplates: tm_importTemplates,

        // 智能预测
        predictTemplates: tm_predictTemplates,

        // 工具函数
        getStatistics: tm_getStatistics,
        resetToDefaults: tm_resetToDefaults
    };
}
// #endregion

console.log('📋 AI Side Panel 模板管理器已加载');
