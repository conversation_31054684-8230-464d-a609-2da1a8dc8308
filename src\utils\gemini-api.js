/**
 * @file Google Gemini API 集成模块
 * @description 提供AI内容分析功能，基于最新的Gemini 2.5 Flash Preview模型，包括内容总结、要点提取、回复生成等
 */

// #region 日志系统初始化
// 创建Gemini API专用日志记录器
let geminiLogger = null;

/**
 * @function aisp_initializeGeminiLogger - 初始化Gemini API日志系统
 * @description 创建适用于Gemini API的日志记录器
 */
function aisp_initializeGeminiLogger() {
    // 检查全局日志系统是否可用
    if (typeof aisp_logCreateLogger === 'function') {
        geminiLogger = aisp_logCreateLogger('gemini-api');
    } else {
        // 创建简化的日志记录器
        geminiLogger = {
            error: (message, data = null) => console.error(`[AISP-GEMINI] [ERROR] ${message}`, data),
            warn: (message, data = null) => console.warn(`[AISP-GEMINI] [WARN] ${message}`, data),
            info: (message, data = null) => console.info(`[AISP-GEMINI] [INFO] ${message}`, data),
            debug: (message, data = null) => console.log(`[AISP-GEMINI] [DEBUG] ${message}`, data),
            performance: (operation, duration, data = {}) => {
                console.log(`[AISP-GEMINI] [PERF] ${operation}: ${duration}ms`, data);
            },
            apiCall: (apiName, method, url, options = {}) => {
                console.log(`[AISP-GEMINI] [API] ${apiName} ${method} ${url}`, options);
            },
            functionEntry: (functionName, params = {}) => {
                console.log(`[AISP-GEMINI] [ENTRY] ${functionName}`, params);
            },
            functionExit: (functionName, result = {}) => {
                console.log(`[AISP-GEMINI] [EXIT] ${functionName}`, result);
            }
        };
    }
}

// 立即初始化日志系统
aisp_initializeGeminiLogger();
// #endregion

// #region 配置常量

/**
 * Gemini API 配置
 */
const GEMINI_CONFIG = {
    // API 端点配置
    BASE_URL: 'https://generativelanguage.googleapis.com/v1beta',
    MODEL_NAME: 'gemini-2.5-flash-preview-05-20',

    // 请求配置
    DEFAULT_TIMEOUT: 30000,
    MAX_RETRIES: 3,
    RETRY_DELAY_BASE: 1000,

    // 内容限制 - Gemini 2.5 Flash Preview 支持更大的输入和更快的处理
    MAX_INPUT_LENGTH: 200000,  // 增加到200K字符，2.5 Flash Preview 支持更大输入
    MAX_OUTPUT_LENGTH: 10000,  // 增加输出长度

    // 缓存配置
    CACHE_TTL: 300000, // 5分钟
    MAX_CACHE_SIZE: 100,

    // 速率限制 - Gemini 2.5 Flash Preview 有更高的限制和更快速度
    RATE_LIMIT_PER_MINUTE: 150,  // 增加到每分钟150次
    RATE_LIMIT_WINDOW: 60000
};

/**
 * 提示词模板
 */
const PROMPT_TEMPLATES = {
    // 内容分析模板 - 针对Gemini 2.5 Flash Preview优化
    ANALYZE_CONTENT: `作为一个专业的内容分析专家，请对以下内容进行深度分析：

内容：{content}

请提供详细的分析结果，用{language}回复，严格按照以下JSON格式：

{
    "summary": "内容的核心摘要（2-3句话，突出主要观点）",
    "keyPoints": [
        "关键要点1（具体且有价值的信息）",
        "关键要点2（按重要性排序）",
        "关键要点3（可操作或有启发性的内容）",
        "关键要点4（如果有的话）",
        "关键要点5（如果有的话）"
    ],
    "contentType": "具体的内容类型（如：技术文章、新闻报道、产品介绍、学术论文、博客文章等）",
    "topic": "主要主题和领域",
    "sentiment": "情感倾向（positive/neutral/negative）",
    "audience": "目标受众群体",
    "difficulty": "内容难度级别（beginner/intermediate/advanced）",
    "actionItems": ["可执行的行动建议1", "可执行的行动建议2"],
    "tags": ["相关标签1", "相关标签2", "相关标签3"],
    "confidence": 0.95,
    "readingTime": "预估阅读时间（分钟）"
}`,

    // 内容摘要模板 - 针对Gemini 2.5 Flash Preview优化
    SUMMARIZE_TEXT: `作为专业的内容摘要专家，请为以下内容生成{length}摘要：

内容：{content}

摘要要求：
- 准确提取核心信息和关键观点
- 保持原文的逻辑结构和重要细节
- 语言简洁明了，表达清晰
- 突出最有价值的信息点
- 用{language}回复
- 长度严格控制在{maxLength}字以内
- 如果是技术内容，保留重要的技术术语
- 如果是新闻内容，突出时效性和重要性

请直接提供摘要内容，不需要额外说明：`,

    // 要点提取模板
    EXTRACT_KEY_POINTS: `请从以下内容中提取{count}个最重要的要点：

内容：{content}

要求：
- 每个要点简洁明了
- 按重要性排序
- 用{language}回复
- 格式为编号列表

要点：`,

    // 回复生成模板 - 针对Gemini 2.5 Flash Preview优化
    GENERATE_REPLIES: `作为专业的客服回复专家，基于以下上下文生成3个高质量的{type}风格客服回复：

上下文：{context}

回复要求：
- 语气保持{tone}，符合品牌形象
- 用{language}回复，表达自然流畅
- 每个回复控制在100字以内，简洁有效
- 提供不同的回复角度和解决方案
- 体现专业性和同理心
- 如果涉及问题，提供具体的解决步骤
- 如果是咨询，提供有价值的信息
- 保持积极正面的态度

请按以下格式提供回复：

回复选项1：[具体回复内容]

回复选项2：[具体回复内容]

回复选项3：[具体回复内容]`,

    // 情感分析模板 - 针对Gemini 2.5 Flash Preview优化
    ANALYZE_SENTIMENT: `作为专业的情感分析专家，请对以下文本进行深度情感分析：

文本：{content}

请进行多维度的情感分析，用{language}回复，严格按照以下JSON格式：

{
    "sentiment": "主要情感倾向（positive/neutral/negative）",
    "intensity": 0.8,
    "confidence": 0.95,
    "emotions": {
        "joy": 0.2,
        "anger": 0.1,
        "sadness": 0.0,
        "fear": 0.0,
        "surprise": 0.1,
        "disgust": 0.0
    },
    "keywords": ["情感关键词1", "情感关键词2", "情感关键词3"],
    "phrases": ["重要情感短语1", "重要情感短语2"],
    "tone": "语调特征（formal/informal/urgent/calm等）",
    "subjectivity": 0.7,
    "analysis": "详细的情感分析说明，包括情感产生的原因和上下文",
    "recommendations": ["基于情感分析的回复建议1", "基于情感分析的回复建议2"]
}`
};

/**
 * 错误类型定义
 */
const ERROR_TYPES = {
    NETWORK_ERROR: 'network_error',
    AUTH_ERROR: 'auth_error',
    RATE_LIMIT_ERROR: 'rate_limit_error',
    CONTENT_ERROR: 'content_error',
    API_ERROR: 'api_error',
    TIMEOUT_ERROR: 'timeout_error',
    UNKNOWN_ERROR: 'unknown_error'
};

// #endregion

// #region 核心API类

/**
 * @class GeminiAPI - Google Gemini API 客户端
 * @description 提供完整的AI内容分析功能
 */
class GeminiAPI {
    /**
     * @constructor
     * @param {string} apiKey - API密钥
     * @param {Object} options - 配置选项
     */
    constructor(apiKey, options = {}) {
        geminiLogger.functionEntry('GeminiAPI.constructor', {
            hasApiKey: !!apiKey,
            options: Object.keys(options)
        });

        this.apiKey = apiKey;
        this.config = { ...GEMINI_CONFIG, ...options };

        // 初始化缓存
        this.cache = new Map();
        this.cacheTimestamps = new Map();

        // 初始化速率限制
        this.requestTimes = [];

        // 初始化统计
        this.stats = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0,
            cacheHits: 0
        };

        // 增强的API监控
        this.monitoring = {
            enabled: true,
            requestHistory: [],
            maxHistorySize: 100,
            currentRequests: new Map(),
            streamHistory: [],
            maxStreamHistorySize: 50
        };

        // 流式传输管理
        this.streamingRequests = new Map();
        this.maxConcurrentStreams = 3;

        // 内存管理
        this.memoryMonitor = {
            lastCleanup: Date.now(),
            cleanupInterval: 300000, // 5分钟
            maxCacheSize: 100,
            maxStreamAge: 600000 // 10分钟
        };

        // 启动内存清理定时器
        this._startMemoryCleanup();

        geminiLogger.info('Gemini API 客户端初始化完成', {
            modelName: this.config.MODEL_NAME,
            maxInputLength: this.config.MAX_INPUT_LENGTH,
            cacheEnabled: true,
            maxConcurrentStreams: this.maxConcurrentStreams
        });

        geminiLogger.functionExit('GeminiAPI.constructor', { success: true });
    }

    /**
     * @function analyzeContent - 综合内容分析
     * @description 对内容进行全面分析，包括摘要、要点、情感等
     * @param {Object} contentData - 内容数据
     * @param {Object} options - 分析选项
     * @returns {Promise<Object>} 分析结果
     */
    async analyzeContent(contentData, options = {}) {
        const startTime = Date.now();

        try {
            geminiLogger.functionEntry('analyzeContent', {
                hasContentData: !!contentData,
                contentLength: contentData?.text?.length || 0,
                options: Object.keys(options)
            });

            const {
                language = 'zh_CN',
                includeKeyPoints = true,
                includeSentiment = true,
                maxLength = 500
            } = options;

            // 验证输入
            if (!contentData || !contentData.text) {
                throw new Error('内容数据无效');
            }

            geminiLogger.debug('开始内容分析', {
                textLength: contentData.text.length,
                language,
                includeKeyPoints,
                includeSentiment
            });

            // 准备分析内容
            const content = this._prepareContent(contentData);

            // 检查缓存
            const cacheKey = this._generateCacheKey('analyze', content, options);
            const cachedResult = this._getFromCache(cacheKey);
            if (cachedResult) {
                this.stats.cacheHits++;
                geminiLogger.info('使用缓存结果', { cacheKey: cacheKey.substring(0, 20) + '...' });
                geminiLogger.functionExit('analyzeContent', {
                    success: true,
                    fromCache: true,
                    duration: Date.now() - startTime
                });
                return cachedResult;
            }

            // 构建提示词
            const prompt = this._buildPrompt('ANALYZE_CONTENT', {
                content: content,
                language: this._getLanguageName(language)
            });

            // 记录API请求开始
            const requestId = this._logAPIRequestStart('generateContent', {
                promptLength: prompt.length,
                model: this.config.MODEL_NAME,
                operation: 'analyzeContent'
            });

            // 发送请求 - Gemini 2.5 Flash Preview 优化参数
            const response = await this._makeRequest(prompt, {
                maxOutputTokens: 2500,  // 增加输出长度，利用2.5 Flash Preview更强性能
                temperature: 0.2,       // 降低温度提高准确性
                topP: 0.8,
                topK: 40
            }, requestId);

            // 解析结果
            const result = this._parseAnalysisResult(response);

            // 缓存结果
            this._setCache(cacheKey, result);

            const duration = Date.now() - startTime;
            geminiLogger.performance('内容分析', duration, {
                success: true,
                resultSize: JSON.stringify(result).length,
                fromCache: false
            });

            geminiLogger.functionExit('analyzeContent', {
                success: true,
                duration,
                resultKeys: Object.keys(result)
            });

            return result;

        } catch (error) {
            const duration = Date.now() - startTime;
            geminiLogger.error('内容分析失败', {
                error: error.message,
                stack: error.stack,
                duration,
                contentLength: contentData?.text?.length || 0
            });

            geminiLogger.functionExit('analyzeContent', {
                success: false,
                error: error.message,
                duration
            });

            throw this._handleError(error);
        }
    }

    /**
     * @function analyzeContentStream - 流式分析内容
     * @description 使用Gemini API流式分析网页内容，支持实时显示
     * @param {Object} contentData - 内容数据
     * @param {Function} onChunk - 接收数据块的回调函数
     * @param {Object} options - 分析选项
     * @returns {Promise<Object>} 完整的分析结果
     */
    async analyzeContentStream(contentData, onChunk = null, options = {}) {
        const startTime = Date.now();

        try {
            geminiLogger.functionEntry('analyzeContentStream', {
                hasContentData: !!contentData,
                contentLength: contentData?.text?.length || 0,
                hasCallback: typeof onChunk === 'function',
                options: Object.keys(options)
            });

            const {
                language = 'zh_CN',
                includeKeyPoints = true,
                includeSentiment = true,
                maxLength = 500
            } = options;

            // 验证输入
            if (!contentData || !contentData.text) {
                throw new Error('内容数据无效');
            }

            geminiLogger.debug('开始流式内容分析', {
                textLength: contentData.text.length,
                language,
                includeKeyPoints,
                includeSentiment
            });

            // 准备分析内容
            const content = this._prepareContent(contentData);

            // 构建提示词
            const prompt = this._buildPrompt('ANALYZE_CONTENT', {
                content: content,
                language: this._getLanguageName(language)
            });

            // 记录流式API请求开始
            const requestId = this._logAPIRequestStart('streamGenerateContent', {
                promptLength: prompt.length,
                model: this.config.MODEL_NAME,
                operation: 'analyzeContentStream'
            });

            // 创建流ID用于监控
            const streamId = `stream_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            // 包装onChunk回调以添加监控
            const monitoredOnChunk = (chunk, fullText) => {
                // 记录流式数据块
                this._logStreamChunk(streamId, {
                    content: chunk,
                    index: this.monitoring.streamHistory.filter(s => s.streamId === streamId).length,
                    isComplete: false
                });

                // 调用原始回调
                if (onChunk && typeof onChunk === 'function') {
                    onChunk(chunk, fullText);
                }
            };

            // 发送流式请求
            const response = await this._makeStreamRequest(prompt, {
                maxOutputTokens: 2500,
                temperature: 0.2,
                topP: 0.8,
                topK: 40
            }, monitoredOnChunk, requestId, streamId);

            // 解析结果
            const result = this._parseAnalysisResult(response);

            const duration = Date.now() - startTime;
            geminiLogger.performance('流式内容分析', duration, {
                success: true,
                resultSize: JSON.stringify(result).length,
                fromCache: false
            });

            geminiLogger.functionExit('analyzeContentStream', {
                success: true,
                duration,
                resultKeys: Object.keys(result)
            });

            return result;

        } catch (error) {
            const duration = Date.now() - startTime;
            geminiLogger.error('流式内容分析失败', {
                error: error.message,
                stack: error.stack,
                duration,
                contentLength: contentData?.text?.length || 0
            });

            geminiLogger.functionExit('analyzeContentStream', {
                success: false,
                error: error.message,
                duration
            });

            throw this._handleError(error);
        }
    }

    /**
     * @function summarizeText - 文本摘要
     * @description 生成文本摘要
     * @param {string} text - 输入文本
     * @param {Object} options - 摘要选项
     * @returns {Promise<string>} 摘要文本
     */
    async summarizeText(text, options = {}) {
        try {
            const {
                length = 'medium',
                language = 'zh_CN',
                maxLength = 200
            } = options;

            // 验证输入
            if (!text || text.trim().length === 0) {
                throw new Error('输入文本为空');
            }

            // 检查文本长度
            if (text.length > this.config.MAX_INPUT_LENGTH) {
                text = text.substring(0, this.config.MAX_INPUT_LENGTH);
            }

            // 检查缓存
            const cacheKey = this._generateCacheKey('summarize', text, options);
            const cachedResult = this._getFromCache(cacheKey);
            if (cachedResult) {
                this.stats.cacheHits++;
                return cachedResult;
            }

            // 构建提示词
            const prompt = this._buildPrompt('SUMMARIZE_TEXT', {
                content: text,
                length: length,
                language: this._getLanguageName(language),
                maxLength: maxLength
            });

            // 发送请求 - Gemini 2.5 Flash Preview 优化参数
            const response = await this._makeRequest(prompt, {
                maxOutputTokens: Math.min(maxLength * 4, 1500),  // 增加输出容量，利用2.5 Preview性能
                temperature: 0.3,  // 适中的创造性
                topP: 0.9,
                topK: 40
            });

            // 提取摘要
            const summary = response.trim();
            
            // 缓存结果
            this._setCache(cacheKey, summary);
            
            return summary;

        } catch (error) {
            console.error('文本摘要失败:', error);
            throw this._handleError(error);
        }
    }

    /**
     * @function extractKeyPoints - 提取关键要点
     * @description 从内容中提取关键要点
     * @param {string} content - 输入内容
     * @param {Object} options - 提取选项
     * @returns {Promise<Array>} 要点列表
     */
    async extractKeyPoints(content, options = {}) {
        try {
            const {
                count = 5,
                language = 'zh_CN'
            } = options;

            // 验证输入
            if (!content || content.trim().length === 0) {
                throw new Error('输入内容为空');
            }

            // 检查缓存
            const cacheKey = this._generateCacheKey('keypoints', content, options);
            const cachedResult = this._getFromCache(cacheKey);
            if (cachedResult) {
                this.stats.cacheHits++;
                return cachedResult;
            }

            // 构建提示词
            const prompt = this._buildPrompt('EXTRACT_KEY_POINTS', {
                content: content,
                count: count,
                language: this._getLanguageName(language)
            });

            // 发送请求 - Gemini 2.5 Flash Preview 优化参数
            const response = await this._makeRequest(prompt, {
                maxOutputTokens: 1000,   // 增加输出长度，利用2.5 Flash Preview性能
                temperature: 0.2,       // 降低温度提高准确性
                topP: 0.8,
                topK: 40
            });

            // 解析要点
            const keyPoints = this._parseKeyPoints(response);
            
            // 缓存结果
            this._setCache(cacheKey, keyPoints);
            
            return keyPoints;

        } catch (error) {
            console.error('要点提取失败:', error);
            throw this._handleError(error);
        }
    }

    /**
     * @function generateReplies - 生成回复建议
     * @description 基于上下文生成多个回复建议
     * @param {string} context - 上下文内容
     * @param {Object} options - 生成选项
     * @returns {Promise<Array>} 回复建议列表
     */
    async generateReplies(context, options = {}) {
        try {
            const {
                type = 'professional',
                tone = 'friendly',
                language = 'zh_CN',
                count = 3
            } = options;

            // 验证输入
            if (!context || context.trim().length === 0) {
                throw new Error('上下文内容为空');
            }

            // 检查缓存
            const cacheKey = this._generateCacheKey('replies', context, options);
            const cachedResult = this._getFromCache(cacheKey);
            if (cachedResult) {
                this.stats.cacheHits++;
                return cachedResult;
            }

            // 构建提示词
            const prompt = this._buildPrompt('GENERATE_REPLIES', {
                context: context,
                type: type,
                tone: tone,
                language: this._getLanguageName(language)
            });

            // 发送请求 - Gemini 2.5 Flash Preview 优化参数
            const response = await this._makeRequest(prompt, {
                maxOutputTokens: 1500,  // 增加输出长度支持更详细回复
                temperature: 0.6,       // 适中的创造性
                topP: 0.9,
                topK: 50
            });

            // 解析回复
            const replies = this._parseReplies(response);

            // 缓存结果
            this._setCache(cacheKey, replies);

            return replies;

        } catch (error) {
            console.error('回复生成失败:', error);
            throw this._handleError(error);
        }
    }

    /**
     * @function analyzeSentiment - 情感分析
     * @description 分析文本的情感倾向
     * @param {string} content - 输入内容
     * @param {Object} options - 分析选项
     * @returns {Promise<Object>} 情感分析结果
     */
    async analyzeSentiment(content, options = {}) {
        try {
            const {
                language = 'zh_CN'
            } = options;

            // 验证输入
            if (!content || content.trim().length === 0) {
                throw new Error('输入内容为空');
            }

            // 检查缓存
            const cacheKey = this._generateCacheKey('sentiment', content, options);
            const cachedResult = this._getFromCache(cacheKey);
            if (cachedResult) {
                this.stats.cacheHits++;
                return cachedResult;
            }

            // 构建提示词
            const prompt = this._buildPrompt('ANALYZE_SENTIMENT', {
                content: content,
                language: this._getLanguageName(language)
            });

            // 发送请求 - Gemini 2.5 Flash Preview 优化参数
            const response = await this._makeRequest(prompt, {
                maxOutputTokens: 1000,   // 增加输出长度支持详细分析
                temperature: 0.1,       // 最低温度确保分析准确性
                topP: 0.8,
                topK: 30
            });

            // 解析情感分析结果
            const sentiment = this._parseSentimentResult(response);

            // 缓存结果
            this._setCache(cacheKey, sentiment);

            return sentiment;

        } catch (error) {
            console.error('情感分析失败:', error);
            throw this._handleError(error);
        }
    }

    /**
     * @function translateContent - 内容翻译
     * @description 翻译内容到目标语言
     * @param {string} content - 输入内容
     * @param {string} targetLanguage - 目标语言
     * @param {Object} options - 翻译选项
     * @returns {Promise<string>} 翻译结果
     */
    async translateContent(content, targetLanguage, options = {}) {
        try {
            const {
                preserveFormatting = true
            } = options;

            // 验证输入
            if (!content || content.trim().length === 0) {
                throw new Error('输入内容为空');
            }

            // 检查缓存
            const cacheKey = this._generateCacheKey('translate', content, { targetLanguage, ...options });
            const cachedResult = this._getFromCache(cacheKey);
            if (cachedResult) {
                this.stats.cacheHits++;
                return cachedResult;
            }

            // 构建翻译提示词
            const prompt = `请将以下内容翻译成${this._getLanguageName(targetLanguage)}：

原文：${content}

要求：
- 保持原文的语气和风格
- ${preserveFormatting ? '保持原有格式' : '可以调整格式'}
- 确保翻译准确自然

译文：`;

            // 发送请求 - Gemini 2.5 Flash Preview 优化参数
            const response = await this._makeRequest(prompt, {
                maxOutputTokens: Math.min(content.length * 3, 2500),  // 增加输出容量
                temperature: 0.2,  // 降低温度提高翻译准确性
                topP: 0.8,
                topK: 40
            });

            // 提取翻译结果
            const translation = response.trim();

            // 缓存结果
            this._setCache(cacheKey, translation);

            return translation;

        } catch (error) {
            console.error('内容翻译失败:', error);
            throw this._handleError(error);
        }
    }

    // #endregion

    // #region 私有辅助方法

    /**
     * @function _makeRequest - 发送API请求
     * @description 发送请求到Gemini API
     * @param {string} prompt - 提示词
     * @param {Object} options - 请求选项
     * @returns {Promise<string>} API响应
     * @private
     */
    async _makeRequest(prompt, options = {}, requestId = null) {
        const startTime = Date.now();

        try {
            // 检查速率限制
            this._checkRateLimit();

            // 准备请求数据
            const requestData = {
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    maxOutputTokens: options.maxOutputTokens || 500,
                    temperature: options.temperature || 0.5,
                    topP: options.topP || 0.8,
                    topK: options.topK || 40
                }
            };

            // 发送请求
            const response = await this._sendHttpRequest(requestData);

            // 提取响应文本
            const responseText = this._extractTextFromResponse(response);

            // 记录API请求结束
            if (requestId) {
                this._logAPIRequestEnd(requestId, {
                    success: true,
                    status: 200,
                    content: responseText
                });
            }

            // 更新统计
            this.stats.totalRequests++;
            this.stats.successfulRequests++;
            this._updateAverageResponseTime(Date.now() - startTime);

            return responseText;

        } catch (error) {
            // 记录API请求失败
            if (requestId) {
                this._logAPIRequestEnd(requestId, {
                    success: false,
                    status: error.status || 500,
                    error: error.message
                });
            }

            this.stats.totalRequests++;
            this.stats.failedRequests++;
            throw error;
        }
    }

    /**
     * @function _makeStreamRequest - 发送流式请求到Gemini API
     * @description 发送流式HTTP请求到Gemini API，支持实时响应
     * @param {string} prompt - 提示词
     * @param {Object} options - 请求选项
     * @param {Function} onChunk - 接收数据块的回调函数
     * @param {string} requestId - 请求ID（用于监控）
     * @param {string} streamId - 流ID（用于监控）
     * @returns {Promise<string>} 完整的API响应文本
     * @private
     */
    async _makeStreamRequest(prompt, options = {}, onChunk = null, requestId = null, streamId = null) {
        const startTime = Date.now();

        try {
            // 检查速率限制
            this._checkRateLimit();

            // 准备请求数据
            const requestData = {
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    maxOutputTokens: options.maxOutputTokens || 500,
                    temperature: options.temperature || 0.5,
                    topP: options.topP || 0.8,
                    topK: options.topK || 40
                }
            };

            // 发送流式请求
            const fullResponse = await this._sendStreamHttpRequest(requestData, onChunk, 0, streamId);

            // 记录流式传输完成
            if (streamId) {
                this._logStreamChunk(streamId, {
                    content: '',
                    index: this.monitoring.streamHistory.filter(s => s.streamId === streamId).length,
                    isComplete: true
                });
            }

            // 记录API请求结束
            if (requestId) {
                this._logAPIRequestEnd(requestId, {
                    success: true,
                    status: 200,
                    content: fullResponse
                });
            }

            // 更新统计
            this.stats.totalRequests++;
            this.stats.successfulRequests++;
            this._updateAverageResponseTime(Date.now() - startTime);

            return fullResponse;

        } catch (error) {
            // 记录API请求失败
            if (requestId) {
                this._logAPIRequestEnd(requestId, {
                    success: false,
                    status: error.status || 500,
                    error: error.message
                });
            }

            this.stats.totalRequests++;
            this.stats.failedRequests++;
            throw error;
        }
    }

    /**
     * @function _sendHttpRequest - 发送HTTP请求
     * @description 发送HTTP请求到Gemini API，包含重试机制
     * @param {Object} requestData - 请求数据
     * @param {number} retryCount - 重试次数
     * @returns {Promise<Object>} API响应
     * @private
     */
    async _sendHttpRequest(requestData, retryCount = 0) {
        const url = `${this.config.BASE_URL}/models/${this.config.MODEL_NAME}:generateContent?key=${this.apiKey}`;

        const requestOptions = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'AI-Side-Panel/1.0'
            },
            body: JSON.stringify(requestData)
        };

        try {
            // 设置超时
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.config.DEFAULT_TIMEOUT);
            requestOptions.signal = controller.signal;

            const response = await fetch(url, requestOptions);
            clearTimeout(timeoutId);

            // 检查响应状态
            if (!response.ok) {
                throw await this._createApiError(response);
            }

            const data = await response.json();

            // 验证响应格式
            if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
                throw new Error('API响应格式无效');
            }

            return data;

        } catch (error) {
            // 处理超时错误
            if (error.name === 'AbortError') {
                error.type = ERROR_TYPES.TIMEOUT_ERROR;
                error.message = '请求超时';
            }

            // 判断是否需要重试
            if (this._shouldRetry(error, retryCount)) {
                const delay = this._calculateRetryDelay(retryCount);
                console.log(`请求失败，${delay}ms后重试 (${retryCount + 1}/${this.config.MAX_RETRIES})`);

                await this._sleep(delay);
                return this._sendHttpRequest(requestData, retryCount + 1);
            }

            throw error;
        }
    }

    /**
     * @function _sendStreamHttpRequest - 发送流式HTTP请求
     * @description 发送流式HTTP请求到Gemini API，支持实时数据流
     * @param {Object} requestData - 请求数据
     * @param {Function} onChunk - 接收数据块的回调函数
     * @param {number} retryCount - 重试次数
     * @param {string} streamId - 流ID（用于监控）
     * @returns {Promise<string>} 完整的响应文本
     * @private
     */
    async _sendStreamHttpRequest(requestData, onChunk = null, retryCount = 0, streamId = null) {
        const url = `${this.config.BASE_URL}/models/${this.config.MODEL_NAME}:streamGenerateContent?key=${this.apiKey}`;

        const requestOptions = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'AI-Side-Panel/1.0'
            },
            body: JSON.stringify(requestData)
        };

        let timeoutId = null;
        let reader = null;

        try {
            // 设置超时
            const controller = new AbortController();
            timeoutId = setTimeout(() => {
                geminiLogger.warn('流式请求超时，正在中止');
                controller.abort();
            }, this.config.DEFAULT_TIMEOUT);
            requestOptions.signal = controller.signal;

            const response = await fetch(url, requestOptions);
            clearTimeout(timeoutId);
            timeoutId = null;

            if (!response.ok) {
                throw await this._createApiError(response);
            }

            // 检查响应体是否存在
            if (!response.body) {
                throw new Error('响应体为空，无法进行流式读取');
            }

            // 处理流式响应 - 优化版本
            reader = response.body.getReader();
            const decoder = new TextDecoder();
            let fullText = '';
            let consecutiveErrors = 0;
            const MAX_CONSECUTIVE_ERRORS = 5;

            // 性能优化：数据块缓冲
            let chunkBuffer = [];
            let lastCallbackTime = 0;
            const CALLBACK_THROTTLE_MS = 100; // 100ms节流
            let pendingCallback = null;

            // 性能监控
            const perfMonitor = {
                startTime: Date.now(),
                charsProcessed: 0,
                chunksReceived: 0,
                callbacksExecuted: 0
            };

            try {
                while (true) {
                    const { done, value } = await reader.read();

                    if (done) {
                        // 处理最后的缓冲数据
                        if (chunkBuffer.length > 0) {
                            await this._processBufferedChunks(chunkBuffer, onChunk, fullText, perfMonitor);
                        }
                        break;
                    }

                    perfMonitor.chunksReceived++;

                    try {
                        const chunk = decoder.decode(value, { stream: true });
                        perfMonitor.charsProcessed += chunk.length;

                        // 将数据块添加到缓冲区
                        chunkBuffer.push(chunk);

                        // 检查是否需要处理缓冲区（节流机制）
                        const now = Date.now();
                        if (now - lastCallbackTime >= CALLBACK_THROTTLE_MS || chunkBuffer.length >= 10) {
                            // 使用requestIdleCallback进行非关键处理
                            if (typeof requestIdleCallback !== 'undefined') {
                                if (pendingCallback) {
                                    cancelIdleCallback(pendingCallback);
                                }

                                pendingCallback = requestIdleCallback(() => {
                                    this._processBufferedChunks(chunkBuffer, onChunk, fullText, perfMonitor);
                                    chunkBuffer = [];
                                    lastCallbackTime = Date.now();
                                    pendingCallback = null;
                                });
                            } else {
                                // 回退到直接处理
                                await this._processBufferedChunks(chunkBuffer, onChunk, fullText, perfMonitor);
                                chunkBuffer = [];
                                lastCallbackTime = now;
                            }
                        }

                        consecutiveErrors = 0; // 重置错误计数

                    } catch (chunkError) {
                        geminiLogger.warn('处理数据块时出错', { error: chunkError.message });
                        consecutiveErrors++;

                        if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
                            throw new Error('流式传输处理错误过多');
                        }
                    }
                }

                // 记录性能指标
                const duration = Date.now() - perfMonitor.startTime;
                geminiLogger.performance('流式传输性能统计', duration, {
                    charsProcessed: perfMonitor.charsProcessed,
                    chunksReceived: perfMonitor.chunksReceived,
                    callbacksExecuted: perfMonitor.callbacksExecuted,
                    charsPerSecond: Math.round(perfMonitor.charsProcessed / (duration / 1000)),
                    avgChunkSize: Math.round(perfMonitor.charsProcessed / perfMonitor.chunksReceived)
                });

            } finally {
                // 清理资源
                if (pendingCallback) {
                    cancelIdleCallback(pendingCallback);
                }

                if (reader) {
                    try {
                        reader.releaseLock();
                    } catch (releaseError) {
                        geminiLogger.warn('释放reader锁失败', { error: releaseError.message });
                    }
                }
            }

            return fullText;

        } catch (error) {
            // 清理资源
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
            if (reader) {
                try {
                    reader.releaseLock();
                } catch (releaseError) {
                    geminiLogger.warn('清理reader时出错', { error: releaseError.message });
                }
            }

            // 处理不同类型的错误
            if (error.name === 'AbortError') {
                error.type = ERROR_TYPES.TIMEOUT_ERROR;
                error.message = '流式请求超时';
                geminiLogger.warn('流式请求被中止', { reason: '超时' });
            } else if (error.name === 'TypeError' && error.message.includes('fetch')) {
                error.type = ERROR_TYPES.NETWORK_ERROR;
                error.message = '网络连接错误';
                geminiLogger.warn('流式请求网络错误', { originalError: error.message });
            }

            // 判断是否需要重试
            if (this._shouldRetry(error, retryCount)) {
                const delay = this._calculateRetryDelay(retryCount);
                geminiLogger.warn(`流式请求失败，${delay}ms后重试`, {
                    retryCount: retryCount + 1,
                    maxRetries: this.config.MAX_RETRIES,
                    errorType: error.type || 'unknown'
                });

                await this._sleep(delay);
                return this._sendStreamHttpRequest(requestData, onChunk, retryCount + 1);
            }

            geminiLogger.error('流式请求最终失败', {
                error: error.message,
                type: error.type,
                retryCount,
                maxRetries: this.config.MAX_RETRIES
            });

            throw error;
        }
    }

    /**
     * @function _processBufferedChunks - 处理缓冲的数据块
     * @description 批量处理缓冲的数据块，提高性能
     * @param {Array} chunkBuffer - 缓冲的数据块数组
     * @param {Function} onChunk - 回调函数
     * @param {string} fullText - 当前完整文本（引用传递）
     * @param {Object} perfMonitor - 性能监控对象
     * @private
     */
    async _processBufferedChunks(chunkBuffer, onChunk, fullText, perfMonitor) {
        if (!chunkBuffer || chunkBuffer.length === 0) return;

        try {
            // 合并所有缓冲的数据块
            const combinedChunk = chunkBuffer.join('');
            const lines = combinedChunk.split('\n');
            let newText = '';

            // 使用数组收集文本，避免重复字符串拼接
            const textParts = [];

            for (const line of lines) {
                if (line.trim()) {
                    try {
                        // Gemini API流式响应处理
                        let jsonStr = line.trim();
                        if (jsonStr.startsWith('data: ')) {
                            jsonStr = jsonStr.slice(6);
                        }

                        if (jsonStr === '[DONE]' || jsonStr === '') continue;

                        const data = JSON.parse(jsonStr);
                        const text = this._extractTextFromResponse(data);

                        if (text) {
                            textParts.push(text);
                        }
                    } catch (parseError) {
                        geminiLogger.warn('批量解析错误', {
                            line: line.substring(0, 50),
                            error: parseError.message
                        });
                    }
                }
            }

            // 合并新文本
            if (textParts.length > 0) {
                newText = textParts.join('');
                fullText += newText;

                // 调用回调函数
                if (onChunk && typeof onChunk === 'function') {
                    try {
                        onChunk(newText, fullText);
                        perfMonitor.callbacksExecuted++;
                    } catch (callbackError) {
                        geminiLogger.warn('批量回调执行错误', {
                            error: callbackError.message
                        });
                    }
                }
            }

        } catch (error) {
            geminiLogger.error('处理缓冲数据块失败', { error: error.message });
        }
    }

    /**
     * @function _startMemoryCleanup - 启动内存清理定时器
     * @description 定期清理过期的缓存和流式请求
     * @private
     */
    _startMemoryCleanup() {
        setInterval(() => {
            this._performMemoryCleanup();
        }, this.memoryMonitor.cleanupInterval);

        geminiLogger.debug('内存清理定时器已启动', {
            interval: this.memoryMonitor.cleanupInterval,
            maxCacheSize: this.memoryMonitor.maxCacheSize
        });
    }

    /**
     * @function _performMemoryCleanup - 执行内存清理
     * @description 清理过期缓存和流式请求
     * @private
     */
    _performMemoryCleanup() {
        const now = Date.now();
        let cleanedItems = 0;

        try {
            // 清理过期缓存
            for (const [key, timestamp] of this.cacheTimestamps.entries()) {
                if (now - timestamp > this.config.CACHE_TTL) {
                    this.cache.delete(key);
                    this.cacheTimestamps.delete(key);
                    cleanedItems++;
                }
            }

            // 如果缓存过大，清理最旧的项目
            if (this.cache.size > this.memoryMonitor.maxCacheSize) {
                const sortedEntries = Array.from(this.cacheTimestamps.entries())
                    .sort((a, b) => a[1] - b[1]);

                const itemsToRemove = this.cache.size - this.memoryMonitor.maxCacheSize;
                for (let i = 0; i < itemsToRemove; i++) {
                    const [key] = sortedEntries[i];
                    this.cache.delete(key);
                    this.cacheTimestamps.delete(key);
                    cleanedItems++;
                }
            }

            // 清理过期的流式请求
            for (const [requestId, requestInfo] of this.streamingRequests.entries()) {
                if (now - requestInfo.startTime > this.memoryMonitor.maxStreamAge) {
                    // 尝试清理流式请求资源
                    if (requestInfo.reader) {
                        try {
                            requestInfo.reader.releaseLock();
                        } catch (error) {
                            // 忽略清理错误
                        }
                    }
                    this.streamingRequests.delete(requestId);
                    cleanedItems++;
                }
            }

            // 记录清理结果
            if (cleanedItems > 0) {
                geminiLogger.debug('内存清理完成', {
                    cleanedItems,
                    cacheSize: this.cache.size,
                    activeStreams: this.streamingRequests.size,
                    memoryUsage: this._estimateMemoryUsage()
                });
            }

            this.memoryMonitor.lastCleanup = now;

        } catch (error) {
            geminiLogger.error('内存清理失败', { error: error.message });
        }
    }

    /**
     * @function _estimateMemoryUsage - 估算内存使用量
     * @description 估算当前API实例的内存使用量
     * @returns {Object} 内存使用统计
     * @private
     */
    _estimateMemoryUsage() {
        try {
            let cacheMemory = 0;
            let streamMemory = 0;

            // 估算缓存内存使用
            for (const value of this.cache.values()) {
                cacheMemory += JSON.stringify(value).length * 2; // 粗略估算
            }

            // 估算流式请求内存使用
            streamMemory = this.streamingRequests.size * 1024; // 每个流大约1KB

            return {
                cacheMemory: Math.round(cacheMemory / 1024), // KB
                streamMemory: Math.round(streamMemory / 1024), // KB
                totalMemory: Math.round((cacheMemory + streamMemory) / 1024), // KB
                cacheItems: this.cache.size,
                activeStreams: this.streamingRequests.size
            };

        } catch (error) {
            geminiLogger.warn('内存使用估算失败', { error: error.message });
            return { error: error.message };
        }
    }

    /**
     * @function _checkConcurrentStreamLimit - 检查并发流限制
     * @description 检查是否超过最大并发流数量
     * @returns {boolean} 是否可以创建新的流
     * @private
     */
    _checkConcurrentStreamLimit() {
        const activeStreams = this.streamingRequests.size;

        if (activeStreams >= this.maxConcurrentStreams) {
            geminiLogger.warn('达到最大并发流限制', {
                activeStreams,
                maxConcurrentStreams: this.maxConcurrentStreams
            });
            return false;
        }

        return true;
    }

    /**
     * @function _createApiError - 创建API错误对象
     * @description 根据HTTP响应创建详细的错误对象
     * @param {Response} response - HTTP响应
     * @returns {Promise<Error>} 错误对象
     * @private
     */
    async _createApiError(response) {
        let errorData;
        try {
            errorData = await response.json();
        } catch (e) {
            errorData = { message: '未知API错误' };
        }

        const error = new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);

        // 设置错误类型
        switch (response.status) {
            case 401:
                error.type = ERROR_TYPES.AUTH_ERROR;
                error.message = 'API密钥无效或已过期';
                break;
            case 429:
                error.type = ERROR_TYPES.RATE_LIMIT_ERROR;
                error.message = '请求频率超限，请稍后重试';
                break;
            case 400:
                error.type = ERROR_TYPES.CONTENT_ERROR;
                error.message = '请求内容格式错误';
                break;
            case 500:
            case 502:
            case 503:
                error.type = ERROR_TYPES.API_ERROR;
                error.message = 'API服务暂时不可用';
                break;
            default:
                error.type = ERROR_TYPES.UNKNOWN_ERROR;
        }

        error.status = response.status;
        error.details = errorData;

        return error;
    }

    /**
     * @function _shouldRetry - 判断是否应该重试
     * @description 根据错误类型和重试次数判断是否重试
     * @param {Error} error - 错误对象
     * @param {number} retryCount - 当前重试次数
     * @returns {boolean} 是否应该重试
     * @private
     */
    _shouldRetry(error, retryCount) {
        if (retryCount >= this.config.MAX_RETRIES) {
            return false;
        }

        // 可重试的错误类型
        const retryableErrors = [
            ERROR_TYPES.NETWORK_ERROR,
            ERROR_TYPES.TIMEOUT_ERROR,
            ERROR_TYPES.API_ERROR,
            ERROR_TYPES.RATE_LIMIT_ERROR
        ];

        return retryableErrors.includes(error.type) ||
               (error.status >= 500 && error.status < 600);
    }

    /**
     * @function _calculateRetryDelay - 计算重试延迟
     * @description 使用指数退避算法计算重试延迟
     * @param {number} retryCount - 重试次数
     * @returns {number} 延迟时间（毫秒）
     * @private
     */
    _calculateRetryDelay(retryCount) {
        const baseDelay = this.config.RETRY_DELAY_BASE;
        const exponentialDelay = baseDelay * Math.pow(2, retryCount);
        const jitter = Math.random() * 1000; // 添加随机抖动

        return Math.min(exponentialDelay + jitter, 30000); // 最大30秒
    }

    /**
     * @function _checkRateLimit - 检查速率限制
     * @description 检查是否超过速率限制
     * @throws {Error} 超过速率限制时抛出错误
     * @private
     */
    _checkRateLimit() {
        const now = Date.now();
        const windowStart = now - this.config.RATE_LIMIT_WINDOW;

        // 清理过期的请求记录
        this.requestTimes = this.requestTimes.filter(time => time > windowStart);

        // 检查是否超过限制
        if (this.requestTimes.length >= this.config.RATE_LIMIT_PER_MINUTE) {
            const error = new Error('请求频率超限，请稍后重试');
            error.type = ERROR_TYPES.RATE_LIMIT_ERROR;
            throw error;
        }

        // 记录当前请求时间
        this.requestTimes.push(now);
    }

    /**
     * @function _extractTextFromResponse - 从API响应中提取文本
     * @description 从Gemini API响应中提取生成的文本
     * @param {Object} response - API响应
     * @returns {string} 提取的文本
     * @private
     */
    _extractTextFromResponse(response) {
        try {
            const candidate = response.candidates[0];
            const content = candidate.content;
            const parts = content.parts;

            if (parts && parts.length > 0 && parts[0].text) {
                return parts[0].text.trim();
            }

            throw new Error('响应中没有找到文本内容');

        } catch (error) {
            console.error('提取响应文本失败:', error);
            throw new Error('解析API响应失败');
        }
    }

    /**
     * @function _prepareContent - 准备内容数据
     * @description 将结构化内容数据转换为适合AI分析的格式
     * @param {Object} contentData - 原始内容数据
     * @returns {string} 格式化的内容
     * @private
     */
    _prepareContent(contentData) {
        let content = '';

        // 添加基本信息
        if (contentData.title) {
            content += `标题：${contentData.title}\n\n`;
        }

        if (contentData.url) {
            content += `链接：${contentData.url}\n\n`;
        }

        // 添加主要文本内容
        if (contentData.text) {
            content += `正文：\n${contentData.text}\n\n`;
        }

        // 添加结构化内容
        if (contentData.structuredContent) {
            const structured = contentData.structuredContent;

            // 添加标题层次
            if (structured.headings && structured.headings.length > 0) {
                content += '标题结构：\n';
                structured.headings.forEach(heading => {
                    content += `${'  '.repeat(heading.level - 1)}- ${heading.text}\n`;
                });
                content += '\n';
            }

            // 添加关键链接
            if (structured.links && structured.links.length > 0) {
                content += '重要链接：\n';
                structured.links.slice(0, 5).forEach(link => {
                    content += `- ${link.text}: ${link.href}\n`;
                });
                content += '\n';
            }
        }

        // 添加元数据
        if (contentData.metadata) {
            const meta = contentData.metadata;
            if (meta.description) {
                content += `描述：${meta.description}\n\n`;
            }
            if (meta.keywords) {
                content += `关键词：${meta.keywords}\n\n`;
            }
        }

        // 限制内容长度
        if (content.length > this.config.MAX_INPUT_LENGTH) {
            content = content.substring(0, this.config.MAX_INPUT_LENGTH) + '...';
        }

        return content.trim();
    }

    /**
     * @function _buildPrompt - 构建提示词
     * @description 根据模板和参数构建提示词
     * @param {string} templateName - 模板名称
     * @param {Object} params - 参数对象
     * @returns {string} 构建的提示词
     * @private
     */
    _buildPrompt(templateName, params) {
        let template = PROMPT_TEMPLATES[templateName];

        if (!template) {
            throw new Error(`未找到提示词模板: ${templateName}`);
        }

        // 替换模板中的参数
        for (const [key, value] of Object.entries(params)) {
            const placeholder = `{${key}}`;
            template = template.replace(new RegExp(placeholder, 'g'), value);
        }

        return template;
    }

    /**
     * @function _parseAnalysisResult - 解析分析结果
     * @description 解析AI返回的内容分析结果
     * @param {string} response - AI响应
     * @returns {Object} 解析后的结果
     * @private
     */
    _parseAnalysisResult(response) {
        try {
            // 尝试解析JSON格式
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const result = JSON.parse(jsonMatch[0]);
                return {
                    summary: result.summary || '',
                    keyPoints: result.keyPoints || [],
                    contentType: result.contentType || 'unknown',
                    topic: result.topic || '',
                    sentiment: result.sentiment || 'neutral',
                    audience: result.audience || '',
                    difficulty: result.difficulty || 'intermediate',
                    actionItems: result.actionItems || [],
                    tags: result.tags || [],
                    confidence: result.confidence || 0.5,
                    readingTime: result.readingTime || '未知'
                };
            }

            // 如果不是JSON格式，尝试文本解析
            return this._parseTextAnalysis(response);

        } catch (error) {
            console.error('解析分析结果失败:', error);
            return {
                summary: response.substring(0, 200),
                keyPoints: [],
                contentType: 'unknown',
                topic: '',
                sentiment: 'neutral',
                audience: '',
                confidence: 0.3
            };
        }
    }

    /**
     * @function _parseKeyPoints - 解析关键要点
     * @description 从AI响应中解析关键要点列表
     * @param {string} response - AI响应
     * @returns {Array} 要点列表
     * @private
     */
    _parseKeyPoints(response) {
        const keyPoints = [];

        // 匹配编号列表格式
        const numberedMatches = response.match(/^\d+\.\s*(.+)$/gm);
        if (numberedMatches) {
            numberedMatches.forEach(match => {
                const point = match.replace(/^\d+\.\s*/, '').trim();
                if (point) keyPoints.push(point);
            });
        }

        // 匹配项目符号格式
        if (keyPoints.length === 0) {
            const bulletMatches = response.match(/^[-•*]\s*(.+)$/gm);
            if (bulletMatches) {
                bulletMatches.forEach(match => {
                    const point = match.replace(/^[-•*]\s*/, '').trim();
                    if (point) keyPoints.push(point);
                });
            }
        }

        // 如果没有找到格式化的要点，按行分割
        if (keyPoints.length === 0) {
            const lines = response.split('\n').filter(line => line.trim());
            lines.forEach(line => {
                const cleaned = line.trim();
                if (cleaned && cleaned.length > 10) {
                    keyPoints.push(cleaned);
                }
            });
        }

        return keyPoints.slice(0, 10); // 限制要点数量
    }

    /**
     * @function _parseReplies - 解析回复建议
     * @description 从AI响应中解析回复建议列表
     * @param {string} response - AI响应
     * @returns {Array} 回复建议列表
     * @private
     */
    _parseReplies(response) {
        const replies = [];

        // 尝试多种分割方式
        const patterns = [
            /^\d+\.\s*(.+?)(?=\n\d+\.|$)/gms,  // 编号格式
            /^[-•*]\s*(.+?)(?=\n[-•*]|$)/gms,  // 项目符号格式
            /回复\s*[一二三1-3][:：]\s*(.+?)(?=\n回复|$)/gms  // 中文格式
        ];

        for (const pattern of patterns) {
            const matches = response.match(pattern);
            if (matches && matches.length > 0) {
                matches.forEach(match => {
                    const reply = match.replace(/^\d+\.\s*|^[-•*]\s*|^回复\s*[一二三1-3][:：]\s*/g, '').trim();
                    if (reply && reply.length > 5) {
                        replies.push(reply);
                    }
                });
                break;
            }
        }

        // 如果没有找到格式化的回复，按段落分割
        if (replies.length === 0) {
            const paragraphs = response.split('\n\n').filter(p => p.trim());
            paragraphs.forEach(paragraph => {
                const cleaned = paragraph.trim();
                if (cleaned && cleaned.length > 10 && cleaned.length < 500) {
                    replies.push(cleaned);
                }
            });
        }

        return replies.slice(0, 5); // 限制回复数量
    }

    /**
     * @function _parseSentimentResult - 解析情感分析结果
     * @description 从AI响应中解析情感分析结果
     * @param {string} response - AI响应
     * @returns {Object} 情感分析结果
     * @private
     */
    _parseSentimentResult(response) {
        try {
            // 尝试解析JSON格式
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const result = JSON.parse(jsonMatch[0]);
                return {
                    sentiment: result.sentiment || 'neutral',
                    intensity: result.intensity || 0.5,
                    confidence: result.confidence || 0.5,
                    emotions: result.emotions || {},
                    keywords: result.keywords || [],
                    phrases: result.phrases || [],
                    tone: result.tone || 'neutral',
                    subjectivity: result.subjectivity || 0.5,
                    analysis: result.analysis || '',
                    recommendations: result.recommendations || []
                };
            }

            // 文本格式解析
            const sentiment = this._extractSentimentFromText(response);
            return {
                sentiment: sentiment,
                intensity: 0.5,
                keywords: [],
                analysis: response.substring(0, 200)
            };

        } catch (error) {
            console.error('解析情感分析结果失败:', error);
            return {
                sentiment: 'neutral',
                intensity: 0.5,
                keywords: [],
                analysis: ''
            };
        }
    }

    /**
     * @function _extractSentimentFromText - 从文本中提取情感
     * @description 从非结构化文本中提取情感倾向
     * @param {string} text - 输入文本
     * @returns {string} 情感倾向
     * @private
     */
    _extractSentimentFromText(text) {
        const lowerText = text.toLowerCase();

        if (lowerText.includes('positive') || lowerText.includes('积极') ||
            lowerText.includes('正面') || lowerText.includes('乐观')) {
            return 'positive';
        }

        if (lowerText.includes('negative') || lowerText.includes('消极') ||
            lowerText.includes('负面') || lowerText.includes('悲观')) {
            return 'negative';
        }

        return 'neutral';
    }

    /**
     * @function _parseTextAnalysis - 解析文本分析结果
     * @description 从非JSON格式的响应中解析分析结果
     * @param {string} response - AI响应
     * @returns {Object} 分析结果
     * @private
     */
    _parseTextAnalysis(response) {
        const result = {
            summary: '',
            keyPoints: [],
            contentType: 'unknown',
            topic: '',
            sentiment: 'neutral',
            audience: '',
            confidence: 0.3
        };

        // 提取摘要
        const summaryMatch = response.match(/摘要[:：]\s*(.+?)(?=\n|$)/i);
        if (summaryMatch) {
            result.summary = summaryMatch[1].trim();
        }

        // 提取要点
        const keyPointsSection = response.match(/要点[:：]\s*([\s\S]*?)(?=\n\n|\n[^-•*\d]|$)/i);
        if (keyPointsSection) {
            result.keyPoints = this._parseKeyPoints(keyPointsSection[1]);
        }

        // 提取内容类型
        const typeMatch = response.match(/类型[:：]\s*(.+?)(?=\n|$)/i);
        if (typeMatch) {
            result.contentType = typeMatch[1].trim();
        }

        return result;
    }

    // #region 缓存管理

    /**
     * @function _generateCacheKey - 生成缓存键
     * @description 根据操作类型、内容和选项生成缓存键
     * @param {string} operation - 操作类型
     * @param {string} content - 内容
     * @param {Object} options - 选项
     * @returns {string} 缓存键
     * @private
     */
    _generateCacheKey(operation, content, options) {
        const contentHash = this._hashString(content);
        const optionsHash = this._hashString(JSON.stringify(options));
        return `${operation}_${contentHash}_${optionsHash}`;
    }

    /**
     * @function _hashString - 字符串哈希
     * @description 生成字符串的简单哈希值
     * @param {string} str - 输入字符串
     * @returns {string} 哈希值
     * @private
     */
    _hashString(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash).toString(36);
    }

    /**
     * @function _getFromCache - 从缓存获取数据
     * @description 从缓存中获取数据，检查过期时间
     * @param {string} key - 缓存键
     * @returns {*} 缓存的数据或null
     * @private
     */
    _getFromCache(key) {
        if (!this.cache.has(key)) {
            return null;
        }

        const timestamp = this.cacheTimestamps.get(key);
        const now = Date.now();

        // 检查是否过期
        if (now - timestamp > this.config.CACHE_TTL) {
            this.cache.delete(key);
            this.cacheTimestamps.delete(key);
            return null;
        }

        return this.cache.get(key);
    }

    /**
     * @function _setCache - 设置缓存数据
     * @description 将数据存储到缓存中
     * @param {string} key - 缓存键
     * @param {*} value - 要缓存的数据
     * @private
     */
    _setCache(key, value) {
        // 检查缓存大小限制
        if (this.cache.size >= this.config.MAX_CACHE_SIZE) {
            this._cleanupCache();
        }

        this.cache.set(key, value);
        this.cacheTimestamps.set(key, Date.now());
    }

    /**
     * @function _cleanupCache - 清理缓存
     * @description 清理过期的缓存项
     * @private
     */
    _cleanupCache() {
        const now = Date.now();
        const expiredKeys = [];

        for (const [key, timestamp] of this.cacheTimestamps.entries()) {
            if (now - timestamp > this.config.CACHE_TTL) {
                expiredKeys.push(key);
            }
        }

        // 删除过期项
        expiredKeys.forEach(key => {
            this.cache.delete(key);
            this.cacheTimestamps.delete(key);
        });

        // 如果还是太多，删除最旧的项
        if (this.cache.size >= this.config.MAX_CACHE_SIZE) {
            const sortedEntries = Array.from(this.cacheTimestamps.entries())
                .sort((a, b) => a[1] - b[1]);

            const toDelete = sortedEntries.slice(0, Math.floor(this.config.MAX_CACHE_SIZE * 0.3));
            toDelete.forEach(([key]) => {
                this.cache.delete(key);
                this.cacheTimestamps.delete(key);
            });
        }
    }

    // #endregion

    // #region API监控方法

    /**
     * @function _logAPIRequestStart - 记录API请求开始
     * @description 记录API请求的开始信息，用于监控
     * @param {string} endpoint - API端点
     * @param {Object} requestData - 请求数据
     * @returns {string} 请求ID
     * @private
     */
    _logAPIRequestStart(endpoint, requestData) {
        if (!this.monitoring.enabled) return null;

        const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        const requestInfo = {
            id: requestId,
            endpoint,
            method: 'POST',
            url: `${this.config.BASE_URL}/models/${this.config.MODEL_NAME}:${endpoint}`,
            startTime: Date.now(),
            requestData: this._sanitizeRequestData(requestData),
            status: 'pending'
        };

        // 添加到当前请求映射
        this.monitoring.currentRequests.set(requestId, requestInfo);

        // 记录到历史
        this.monitoring.requestHistory.push(requestInfo);

        // 限制历史记录大小
        if (this.monitoring.requestHistory.length > this.monitoring.maxHistorySize) {
            this.monitoring.requestHistory = this.monitoring.requestHistory.slice(-this.monitoring.maxHistorySize);
        }

        // 使用增强的日志系统记录
        if (typeof aisp_logAPIRequest === 'function') {
            aisp_logAPIRequest('Gemini', requestInfo);
        }

        geminiLogger.debug('API请求开始', {
            requestId,
            endpoint,
            url: requestInfo.url
        });

        return requestId;
    }

    /**
     * @function _logAPIRequestEnd - 记录API请求结束
     * @description 记录API请求的结束信息
     * @param {string} requestId - 请求ID
     * @param {Object} responseData - 响应数据
     * @private
     */
    _logAPIRequestEnd(requestId, responseData) {
        if (!this.monitoring.enabled || !requestId) return;

        const requestInfo = this.monitoring.currentRequests.get(requestId);
        if (!requestInfo) return;

        const endTime = Date.now();
        const duration = endTime - requestInfo.startTime;

        // 更新请求信息
        requestInfo.endTime = endTime;
        requestInfo.duration = duration;
        requestInfo.status = responseData.success ? 'success' : 'error';
        requestInfo.responseData = this._sanitizeResponseData(responseData);

        // 从当前请求中移除
        this.monitoring.currentRequests.delete(requestId);

        // 更新历史记录中的对应项
        const historyIndex = this.monitoring.requestHistory.findIndex(req => req.id === requestId);
        if (historyIndex !== -1) {
            this.monitoring.requestHistory[historyIndex] = { ...requestInfo };
        }

        // 使用增强的日志系统记录
        if (typeof aisp_logAPIResponse === 'function') {
            aisp_logAPIResponse(requestId, {
                status: responseData.status || (responseData.success ? 200 : 500),
                statusText: responseData.success ? 'OK' : 'Error',
                responseTime: duration,
                body: responseData,
                success: responseData.success
            });
        }

        geminiLogger.debug('API请求结束', {
            requestId,
            duration,
            status: requestInfo.status,
            success: responseData.success
        });
    }

    /**
     * @function _logStreamChunk - 记录流式数据块
     * @description 记录流式传输的数据块信息
     * @param {string} streamId - 流ID
     * @param {Object} chunkData - 数据块信息
     * @private
     */
    _logStreamChunk(streamId, chunkData) {
        if (!this.monitoring.enabled) return;

        const chunkInfo = {
            streamId,
            chunkIndex: chunkData.index || 0,
            chunkSize: chunkData.content ? chunkData.content.length : 0,
            content: chunkData.content ? chunkData.content.substring(0, 200) : '',
            timestamp: Date.now(),
            isComplete: chunkData.isComplete || false
        };

        // 添加到流式历史
        this.monitoring.streamHistory.push(chunkInfo);

        // 限制历史记录大小
        if (this.monitoring.streamHistory.length > this.monitoring.maxStreamHistorySize) {
            this.monitoring.streamHistory = this.monitoring.streamHistory.slice(-this.monitoring.maxStreamHistorySize);
        }

        // 使用增强的日志系统记录
        if (typeof aisp_logStreamChunk === 'function') {
            aisp_logStreamChunk(streamId, chunkInfo);
        }

        geminiLogger.debug('流式数据块', {
            streamId,
            chunkIndex: chunkInfo.chunkIndex,
            chunkSize: chunkInfo.chunkSize,
            isComplete: chunkInfo.isComplete
        });
    }

    /**
     * @function _sanitizeRequestData - 清理请求数据
     * @description 移除敏感信息，限制数据大小
     * @param {Object} data - 原始请求数据
     * @returns {Object} 清理后的数据
     * @private
     */
    _sanitizeRequestData(data) {
        if (!data) return null;

        const sanitized = { ...data };

        // 移除或截断大型数据
        if (sanitized.promptLength > 1000) {
            sanitized.promptPreview = `[${sanitized.promptLength} characters]`;
            delete sanitized.prompt;
        }

        return sanitized;
    }

    /**
     * @function _sanitizeResponseData - 清理响应数据
     * @description 限制响应数据大小
     * @param {Object} data - 原始响应数据
     * @returns {Object} 清理后的数据
     * @private
     */
    _sanitizeResponseData(data) {
        if (!data) return null;

        const sanitized = { ...data };

        // 限制响应内容大小
        if (sanitized.content && sanitized.content.length > 500) {
            sanitized.contentPreview = sanitized.content.substring(0, 500) + '...';
            sanitized.contentLength = sanitized.content.length;
            delete sanitized.content;
        }

        return sanitized;
    }

    /**
     * @function getMonitoringData - 获取监控数据
     * @description 获取API监控数据
     * @returns {Object} 监控数据
     */
    getMonitoringData() {
        return {
            enabled: this.monitoring.enabled,
            currentRequests: Array.from(this.monitoring.currentRequests.values()),
            requestHistory: this.monitoring.requestHistory.slice(-20), // 最近20个请求
            streamHistory: this.monitoring.streamHistory.slice(-10),   // 最近10个流
            stats: this.getStats()
        };
    }

    /**
     * @function clearMonitoringData - 清理监控数据
     * @description 清理API监控历史数据
     * @param {string} dataType - 要清理的数据类型
     */
    clearMonitoringData(dataType = 'all') {
        switch (dataType) {
            case 'requests':
                this.monitoring.requestHistory = [];
                this.monitoring.currentRequests.clear();
                break;
            case 'streams':
                this.monitoring.streamHistory = [];
                break;
            case 'all':
            default:
                this.monitoring.requestHistory = [];
                this.monitoring.currentRequests.clear();
                this.monitoring.streamHistory = [];
                break;
        }

        geminiLogger.info('监控数据已清理', { dataType });
    }

    /**
     * @function toggleMonitoring - 切换监控状态
     * @description 启用或禁用API监控
     * @param {boolean} enabled - 是否启用监控
     */
    toggleMonitoring(enabled) {
        this.monitoring.enabled = enabled;
        geminiLogger.info(`API监控已${enabled ? '启用' : '禁用'}`);
    }

    // #endregion

    // #region 工具函数

    /**
     * @function _getLanguageName - 获取语言名称
     * @description 将语言代码转换为语言名称
     * @param {string} languageCode - 语言代码
     * @returns {string} 语言名称
     * @private
     */
    _getLanguageName(languageCode) {
        const languageMap = {
            'zh_CN': '中文',
            'en_US': 'English',
            'ja_JP': '日本語',
            'ko_KR': '한국어'
        };

        return languageMap[languageCode] || '中文';
    }

    /**
     * @function _handleError - 处理错误
     * @description 统一处理和格式化错误
     * @param {Error} error - 原始错误
     * @returns {Error} 格式化后的错误
     * @private
     */
    _handleError(error) {
        // 如果已经是格式化的错误，直接返回
        if (error.type) {
            return error;
        }

        // 网络错误
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            error.type = ERROR_TYPES.NETWORK_ERROR;
            error.message = '网络连接失败，请检查网络设置';
        }
        // 超时错误
        else if (error.name === 'AbortError') {
            error.type = ERROR_TYPES.TIMEOUT_ERROR;
            error.message = '请求超时，请稍后重试';
        }
        // 其他错误
        else {
            error.type = ERROR_TYPES.UNKNOWN_ERROR;
        }

        return error;
    }

    /**
     * @function _updateAverageResponseTime - 更新平均响应时间
     * @description 更新API请求的平均响应时间统计
     * @param {number} responseTime - 本次响应时间
     * @private
     */
    _updateAverageResponseTime(responseTime) {
        const totalRequests = this.stats.successfulRequests;
        const currentAverage = this.stats.averageResponseTime;

        this.stats.averageResponseTime =
            (currentAverage * (totalRequests - 1) + responseTime) / totalRequests;
    }

    /**
     * @function _sleep - 延迟函数
     * @description 异步延迟指定时间
     * @param {number} ms - 延迟时间（毫秒）
     * @returns {Promise} Promise对象
     * @private
     */
    _sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // #endregion

    // #region 公共方法

    /**
     * @function getStats - 获取统计信息
     * @description 获取API使用统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this.stats,
            cacheSize: this.cache.size,
            successRate: this.stats.totalRequests > 0 ?
                (this.stats.successfulRequests / this.stats.totalRequests * 100).toFixed(2) + '%' : '0%'
        };
    }

    /**
     * @function clearCache - 清空缓存
     * @description 清空所有缓存数据
     */
    clearCache() {
        this.cache.clear();
        this.cacheTimestamps.clear();
        console.log('缓存已清空');
    }

    /**
     * @function updateConfig - 更新配置
     * @description 更新API配置
     * @param {Object} newConfig - 新配置
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        console.log('配置已更新:', newConfig);
    }

    /**
     * @function testConnection - 测试连接
     * @description 测试API连接是否正常
     * @returns {Promise<boolean>} 连接测试结果
     */
    async testConnection() {
        try {
            const testPrompt = '请回复"连接正常"以确认API工作状态';
            const response = await this._makeRequest(testPrompt, {
                maxOutputTokens: 100,
                temperature: 0,
                topP: 0.8,
                topK: 10
            });

            return response.includes('连接正常') || response.includes('正常');

        } catch (error) {
            console.error('连接测试失败:', error);
            return false;
        }
    }

    // #endregion
}

// #endregion

// #region 导出和工厂函数

/**
 * @function createGeminiAPI - 创建Gemini API实例
 * @description 工厂函数，创建配置好的Gemini API实例
 * @param {string} apiKey - API密钥
 * @param {Object} options - 配置选项
 * @returns {GeminiAPI} API实例
 */
function createGeminiAPI(apiKey, options = {}) {
    if (!apiKey) {
        throw new Error('API密钥不能为空');
    }

    return new GeminiAPI(apiKey, options);
}

/**
 * @function validateApiKey - 验证API密钥格式
 * @description 验证API密钥是否符合格式要求
 * @param {string} apiKey - API密钥
 * @returns {boolean} 验证结果
 */
function validateApiKey(apiKey) {
    if (!apiKey || typeof apiKey !== 'string') {
        return false;
    }

    // Google API密钥通常以AIza开头，长度为39字符
    // Gemini 2.5 Flash Preview API密钥保持相同格式
    return apiKey.startsWith('AIza') && apiKey.length === 39;
}

// 导出
// 导出
if (typeof self !== 'undefined' && typeof importScripts === 'function') {
    // Service Worker环境
    self.GeminiAPI = GeminiAPI;
    self.createGeminiAPI = createGeminiAPI;
    self.validateApiKey = validateApiKey;
    self.GEMINI_CONFIG = GEMINI_CONFIG;
    self.ERROR_TYPES = ERROR_TYPES;
} else if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        GeminiAPI,
        createGeminiAPI,
        validateApiKey,
        GEMINI_CONFIG,
        ERROR_TYPES
    };
} else if (typeof window !== 'undefined') {
    // 浏览器环境
    window.GeminiAPI = GeminiAPI;
    window.createGeminiAPI = createGeminiAPI;
    window.validateApiKey = validateApiKey;
    window.GEMINI_CONFIG = GEMINI_CONFIG;
    window.ERROR_TYPES = ERROR_TYPES;
}

// #endregion

console.log('Gemini API 模块已加载');
