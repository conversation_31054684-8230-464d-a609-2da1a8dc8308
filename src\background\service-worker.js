/**
 * @file AI Side Panel Chrome扩展后台服务工作器
 * @description 处理扩展的后台逻辑，包括API调用、数据同步、事件监听等
 */

// #region 导入依赖脚本
try {
    // 导入日志系统（优先加载）
    importScripts('../utils/logger.js');

    // 导入API配置
    importScripts('../config/api-keys.js');

    // 导入工具脚本
    importScripts('../utils/config-manager.js');
    importScripts('../utils/gemini-api.js');
    importScripts('../utils/api-manager.js');

    // 创建后台服务专用日志记录器
    const backgroundLogger = aisp_logCreateLogger('background');
    backgroundLogger.info('Service Worker 依赖脚本加载成功');
} catch (error) {
    console.error('❌ Service Worker 依赖脚本加载失败:', error);
    // 如果日志系统加载失败，使用console作为备用
    if (typeof aisp_logError === 'function') {
        aisp_logError('Service Worker 依赖脚本加载失败', { error: error.message });
    }
}
// #endregion

// #region 扩展初始化
/**
 * @function aisp_initialize - 扩展初始化函数
 * @description 扩展安装或启动时的初始化操作
 */
async function aisp_initialize() {
    try {
        await aisp_logFunctionEntry('aisp_initialize');
        await aisp_logInfo('AI Side Panel 扩展开始初始化');

        // 检查API密钥状态
        const keyStatus = getApiKeyStatus();
        if (keyStatus.gemini.available) {
            await aisp_logInfo('Gemini API密钥配置正确');
            
            // 自动检测API连接状态
            await aisp_testAPIConnection();
        } else {
            await aisp_logWarn('Gemini API密钥需要配置', {
                status: keyStatus.gemini.status
            });
        }

        // 设置默认配置
        const defaultConfig = {
            currentLanguage: 'zh_CN',
            googleDriveEnabled: false,
            autoAnalysis: true,
            templateEnabled: true
        };

        await chrome.storage.local.set({ 'aisp_config': defaultConfig });
        await aisp_logInfo('默认配置已设置', { config: defaultConfig });

        await aisp_logFunctionExit('aisp_initialize', { success: true });
    } catch (error) {
        await aisp_logError('扩展初始化失败', {
            error: error.message,
            stack: error.stack
        });
        throw error;
    }
}

/**
 * @function aisp_testAPIConnection - 测试API连接状态
 * @description 在扩展启动时自动测试Gemini API连接状态
 */
async function aisp_testAPIConnection() {
    try {
        await aisp_logFunctionEntry('aisp_testAPIConnection');
        await aisp_logInfo('开始检测 Gemini API 连接状态...');

        // 创建API管理器实例进行连接测试
        const apiManager = getAPIManager();
        await apiManager.initialize();

        if (apiManager.isReady()) {
            // 执行连接测试
            const connectionTest = await apiManager.testConnection();
            
            if (connectionTest.success) {
                await aisp_logInfo('✅ Gemini API 连接测试成功', {
                    responseTime: connectionTest.responseTime,
                    model: connectionTest.model
                });
                
                // 存储连接状态
                await chrome.storage.local.set({
                    'aisp_api_status': {
                        connected: true,
                        lastChecked: Date.now(),
                        responseTime: connectionTest.responseTime,
                        model: connectionTest.model || 'gemini-2.5-flash-preview-05-20'
                    }
                });
                
                // 发送连接成功通知给所有标签页
                aisp_broadcastConnectionStatus(true, connectionTest);
                
            } else {
                await aisp_logWarn('⚠️ Gemini API 连接测试失败', {
                    error: connectionTest.error
                });
                
                // 存储连接失败状态
                await chrome.storage.local.set({
                    'aisp_api_status': {
                        connected: false,
                        lastChecked: Date.now(),
                        error: connectionTest.error
                    }
                });
                
                // 发送连接失败通知
                aisp_broadcastConnectionStatus(false, connectionTest);
            }
        } else {
            await aisp_logError('API管理器未就绪，无法进行连接测试');
            
            // 存储API管理器未就绪状态
            await chrome.storage.local.set({
                'aisp_api_status': {
                    connected: false,
                    lastChecked: Date.now(),
                    error: 'API管理器未就绪'
                }
            });
        }

        await aisp_logFunctionExit('aisp_testAPIConnection', { success: true });

    } catch (error) {
        await aisp_logError('API连接测试过程中发生错误', {
            error: error.message,
            stack: error.stack
        });
        
        // 存储错误状态
        await chrome.storage.local.set({
            'aisp_api_status': {
                connected: false,
                lastChecked: Date.now(),
                error: error.message
            }
        });
        
        await aisp_logFunctionExit('aisp_testAPIConnection', { 
            success: false, 
            error: error.message 
        });
    }
}

/**
 * @function aisp_broadcastConnectionStatus - 广播API连接状态
 * @description 向所有标签页广播API连接状态变化
 * @param {boolean} connected - 是否连接成功
 * @param {Object} testResult - 测试结果详情
 */
async function aisp_broadcastConnectionStatus(connected, testResult) {
    try {
        // 获取所有标签页
        const tabs = await chrome.tabs.query({});

        const statusMessage = {
            action: 'api_connection_status',
            connected: connected,
            timestamp: Date.now(),
            details: testResult
        };

        // 向每个标签页发送状态消息
        for (const tab of tabs) {
            try {
                await chrome.tabs.sendMessage(tab.id, statusMessage);
            } catch (error) {
                // 忽略无法发送消息的标签页（如chrome://页面）
                if (!error.message.includes('Could not establish connection')) {
                    await aisp_logDebug('向标签页发送状态消息失败', {
                        tabId: tab.id,
                        error: error.message
                    });
                }
            }
        }

        await aisp_logInfo('API连接状态已广播', {
            connected,
            tabCount: tabs.length
        });

    } catch (error) {
        await aisp_logError('广播API连接状态失败', {
            error: error.message
        });
    }
}

/**
 * @function aisp_broadcastRuntimeData - 广播运行时数据
 * @description 向所有侧边栏和调试面板广播运行时数据更新
 * @param {Object} runtimeData - 运行时数据
 */
async function aisp_broadcastRuntimeData(runtimeData) {
    try {
        // 获取所有标签页
        const tabs = await chrome.tabs.query({});

        const dataMessage = {
            action: 'aisp_runtime_data_update',
            data: runtimeData,
            timestamp: Date.now()
        };

        // 向每个标签页的侧边栏发送数据
        for (const tab of tabs) {
            try {
                // 发送到content script
                await chrome.tabs.sendMessage(tab.id, dataMessage);

                // 发送到侧边栏（如果存在）
                try {
                    await chrome.runtime.sendMessage(dataMessage);
                } catch (error) {
                    // 侧边栏可能未打开，忽略错误
                }
            } catch (error) {
                // 忽略无法发送消息的标签页
                if (!error.message.includes('Could not establish connection')) {
                    await aisp_logDebug('向标签页发送运行时数据失败', {
                        tabId: tab.id,
                        error: error.message
                    });
                }
            }
        }

        await aisp_logDebug('运行时数据已广播', {
            dataType: runtimeData.dataType,
            tabCount: tabs.length
        });

    } catch (error) {
        await aisp_logError('广播运行时数据失败', {
            error: error.message
        });
    }
}

// 扩展安装事件监听
chrome.runtime.onInstalled.addListener((details) => {
    if (details.reason === 'install') {
        aisp_initialize();
        
        // 打开欢迎页面
        chrome.tabs.create({
            url: chrome.runtime.getURL('src/welcome/welcome.html')
        });
    }
});

// 扩展启动事件监听
chrome.runtime.onStartup.addListener(() => {
    aisp_initialize();
});
// #endregion

// #region 侧边栏管理
/**
 * @function aisp_toggleSidePanel - 切换侧边栏显示状态
 * @param {number} tabId - 标签页ID
 * @returns {Promise<void>}
 */
async function aisp_toggleSidePanel(tabId) {
    try {
        await aisp_logFunctionEntry('aisp_toggleSidePanel', { tabId });
        await aisp_logUserAction('toggle_sidepanel', { tabId });

        await chrome.sidePanel.setOptions({
            tabId,
            enabled: true,
            path: 'src/sidepanel/sidepanel.html'
        });

        await aisp_logInfo('侧边栏已启用', { tabId });
        await aisp_logFunctionExit('aisp_toggleSidePanel', { success: true });
    } catch (error) {
        await aisp_logError('切换侧边栏失败', {
            tabId,
            error: error.message,
            stack: error.stack
        });
        throw error;
    }
}

// 扩展图标点击事件
chrome.action.onClicked.addListener(async (tab) => {
    await aisp_toggleSidePanel(tab.id);
});
// #endregion

// #region 消息通信处理
/**
 * @function aisp_handleMessage - 处理来自其他脚本的消息
 * @param {Object} request - 请求对象
 * @param {Object} sender - 发送者信息
 * @param {Function} sendResponse - 响应函数
 * @returns {boolean} 是否异步响应
 */
async function aisp_handleMessage(request, sender, sendResponse) {
    try {
        await aisp_logFunctionEntry('aisp_handleMessage', {
            action: request.action,
            senderTab: sender.tab?.id,
            senderUrl: sender.url
        });

        switch (request.action) {
            case 'content_captured':
                // 处理内容捕获消息
                await aisp_logInfo('收到内容捕获消息', {
                    dataSize: JSON.stringify(request.data).length
                });
                aisp_processContentCapture(request.data)
                    .then(result => {
                        aisp_logInfo('内容捕获处理完成');
                        sendResponse({ success: true, data: result });
                    })
                    .catch(error => {
                        aisp_logError('内容捕获处理失败', { error: error.message });
                        sendResponse({ success: false, error: error.message });
                    });
                return true;

            case 'analyze_content':
                // 处理内容分析请求
                await aisp_logInfo('收到内容分析请求', {
                    contentLength: request.content?.length
                });
                aisp_analyzeContent(request.content)
                    .then(result => {
                        aisp_logInfo('内容分析完成');
                        sendResponse({ success: true, data: result });
                    })
                    .catch(error => {
                        aisp_logError('内容分析失败', { error: error.message });
                        sendResponse({ success: false, error: error.message });
                    });
                return true;

            case 'get_templates':
                // 获取模板数据
                await aisp_logInfo('收到获取模板请求');
                aisp_getTemplates()
                    .then(templates => {
                        aisp_logInfo('模板数据获取完成', { count: templates.length });
                        sendResponse({ success: true, data: templates });
                    })
                    .catch(error => {
                        aisp_logError('获取模板失败', { error: error.message });
                        sendResponse({ success: false, error: error.message });
                    });
                return true;

            case 'save_template':
                // 保存模板
                await aisp_logInfo('收到保存模板请求', {
                    templateId: request.template?.id
                });
                aisp_saveTemplate(request.template)
                    .then(() => {
                        aisp_logInfo('模板保存完成');
                        sendResponse({ success: true });
                    })
                    .catch(error => {
                        aisp_logError('保存模板失败', { error: error.message });
                        sendResponse({ success: false, error: error.message });
                    });
                return true;

            case 'log_message':
                // 处理来自content script的日志消息
                try {
                    const { level, message, data, context, timestamp, url } = request;
                    const logData = {
                        ...data,
                        originalContext: context,
                        sourceUrl: url,
                        receivedAt: new Date().toISOString()
                    };

                    // 根据级别调用相应的日志函数
                    switch (level) {
                        case 'ERROR':
                            await aisp_logError(`[${context.toUpperCase()}] ${message}`, logData);
                            break;
                        case 'WARN':
                            await aisp_logWarn(`[${context.toUpperCase()}] ${message}`, logData);
                            break;
                        case 'INFO':
                            await aisp_logInfo(`[${context.toUpperCase()}] ${message}`, logData);
                            break;
                        case 'DEBUG':
                            await aisp_logDebug(`[${context.toUpperCase()}] ${message}`, logData);
                            break;
                        default:
                            await aisp_logInfo(`[${context.toUpperCase()}] ${message}`, logData);
                    }

                    sendResponse({ success: true });
                } catch (error) {
                    console.error('处理日志消息失败:', error);
                    sendResponse({ success: false, error: error.message });
                }
                return true;

            case 'get_api_status':
                // 获取API连接状态
                await aisp_logInfo('收到API状态查询请求');
                try {
                    const apiStatus = await chrome.storage.local.get('aisp_api_status');
                    await aisp_logInfo('API状态查询完成', { status: apiStatus.aisp_api_status });
                    sendResponse({ 
                        success: true, 
                        data: apiStatus.aisp_api_status || { 
                            connected: false, 
                            error: '状态未知' 
                        } 
                    });
                } catch (error) {
                    await aisp_logError('获取API状态失败', { error: error.message });
                    sendResponse({ success: false, error: error.message });
                }
                return true;

            case 'test_api_connection':
                // 手动测试API连接
                await aisp_logInfo('收到API连接测试请求');
                (async () => {
                    try {
                        await aisp_testAPIConnection();
                        const result = await chrome.storage.local.get('aisp_api_status');
                        await aisp_logInfo('API连接测试完成');
                        sendResponse({
                            success: true,
                            data: result.aisp_api_status
                        });
                    } catch (error) {
                        await aisp_logError('API连接测试失败', { error: error.message });
                        sendResponse({ success: false, error: error.message });
                    }
                })();
                return true;

            case 'aisp_runtime_data_sync':
                // 处理运行时数据同步
                try {
                    await aisp_logDebug('收到运行时数据同步', {
                        dataType: request.data?.dataType,
                        timestamp: request.data?.timestamp
                    });

                    // 转发到所有侧边栏和调试面板
                    aisp_broadcastRuntimeData(request.data);
                    sendResponse({ success: true });
                } catch (error) {
                    await aisp_logError('运行时数据同步失败', { error: error.message });
                    sendResponse({ success: false, error: error.message });
                }
                return true;

            case 'get_runtime_data':
                // 获取运行时监控数据
                try {
                    await aisp_logDebug('收到运行时数据请求', {
                        filter: request.filter
                    });

                    if (typeof aisp_getRuntimeData === 'function') {
                        const runtimeData = aisp_getRuntimeData(request.filter);
                        sendResponse({ success: true, data: runtimeData });
                    } else {
                        sendResponse({ success: false, error: '运行时数据功能未可用' });
                    }
                } catch (error) {
                    await aisp_logError('获取运行时数据失败', { error: error.message });
                    sendResponse({ success: false, error: error.message });
                }
                return true;

            case 'clear_runtime_data':
                // 清理运行时数据
                try {
                    await aisp_logInfo('收到清理运行时数据请求', {
                        dataType: request.dataType
                    });

                    if (typeof aisp_clearRuntimeData === 'function') {
                        aisp_clearRuntimeData(request.dataType);
                        sendResponse({ success: true });
                    } else {
                        sendResponse({ success: false, error: '运行时数据功能未可用' });
                    }
                } catch (error) {
                    await aisp_logError('清理运行时数据失败', { error: error.message });
                    sendResponse({ success: false, error: error.message });
                }
                return true;

            case 'toggle_dev_mode':
                // 切换开发模式
                try {
                    await aisp_logInfo('收到开发模式切换请求', {
                        enabled: request.enabled
                    });

                    if (typeof aisp_logSetConfig === 'function') {
                        aisp_logSetConfig({
                            enableDevMode: request.enabled,
                            enableRuntimeMonitoring: request.enabled,
                            enableRealtimeSync: request.enabled
                        });
                        sendResponse({ success: true });
                    } else {
                        sendResponse({ success: false, error: '日志配置功能未可用' });
                    }
                } catch (error) {
                    await aisp_logError('切换开发模式失败', { error: error.message });
                    sendResponse({ success: false, error: error.message });
                }
                return true;

            default:
                await aisp_logWarn('收到未知操作类型', { action: request.action });
                sendResponse({ success: false, error: '未知的操作类型' });
                return false;
        }
    } catch (error) {
        await aisp_logError('消息处理异常', {
            error: error.message,
            stack: error.stack,
            request: request
        });
        sendResponse({ success: false, error: '消息处理异常' });
        return false;
    }
}

// 消息监听器
chrome.runtime.onMessage.addListener(aisp_handleMessage);
// #endregion

// #region 内容处理函数
/**
 * @function aisp_processContentCapture - 处理捕获的内容
 * @param {Object} contentData - 捕获的内容数据
 * @returns {Promise<Object>} 处理结果
 */
async function aisp_processContentCapture(contentData) {
    try {
        // 存储捕获的内容
        await chrome.storage.local.set({
            'latest_content': {
                url: contentData.url,
                title: contentData.title,
                text: contentData.text,
                images: contentData.images,
                timestamp: Date.now()
            }
        });
        
        // 如果启用了自动分析，则触发AI分析
        const config = await chrome.storage.local.get('aisp_config');
        if (config.aisp_config?.autoAnalysis) {
            return await aisp_analyzeContent(contentData.text);
        }
        
        return { message: '内容已捕获' };
    } catch (error) {
        console.error('处理内容捕获失败:', error);
        throw error;
    }
}

/**
 * @function aisp_analyzeContent - 分析内容
 * @param {string} content - 要分析的内容
 * @returns {Promise<Object>} 分析结果
 */
async function aisp_analyzeContent(content) {
    const startTime = Date.now();

    try {
        await aisp_logFunctionEntry('aisp_analyzeContent', {
            contentLength: content?.length
        });

        // 检查API密钥状态
        const keyStatus = getApiKeyStatus();
        if (!keyStatus.gemini.available) {
            await aisp_logWarn('Gemini API密钥未配置，返回默认分析结果', {
                status: keyStatus.gemini.status
            });

            const defaultResult = {
                summary: '请在 src/config/api-keys.js 中配置 Gemini API 密钥以启用AI分析功能',
                keyPoints: ['配置API密钥', '重新加载扩展', '开始使用AI分析'],
                suggestions: ['请联系管理员获取API密钥'],
                error: 'API密钥未配置'
            };

            await aisp_logFunctionExit('aisp_analyzeContent', {
                result: 'default_response',
                duration: Date.now() - startTime
            });
            return defaultResult;
        }

        await aisp_logInfo('开始AI内容分析', { apiStatus: 'available' });

        // 获取API管理器实例
        const apiManager = getAPIManager();
        await apiManager.initialize();

        if (!apiManager.isReady()) {
            throw new Error('API管理器未就绪');
        }

        await aisp_logAPICall('Gemini', 'POST', 'content-analysis', {
            contentLength: content.length
        });

        // 使用Gemini API分析内容
        const analysisResult = await apiManager.analyzeContent({
            text: content,
            url: '',
            title: ''
        });

        const result = {
            summary: analysisResult.summary || '分析完成',
            keyPoints: analysisResult.keyPoints || [],
            suggestions: analysisResult.actionItems || [],
            contentType: analysisResult.contentType || 'unknown',
            sentiment: analysisResult.sentiment || 'neutral',
            confidence: analysisResult.confidence || 0.5
        };

        const duration = Date.now() - startTime;
        await aisp_logPerformance('content_analysis', duration, {
            success: true,
            resultSize: JSON.stringify(result).length
        });

        await aisp_logFunctionExit('aisp_analyzeContent', {
            success: true,
            duration
        });

        return result;

    } catch (error) {
        const duration = Date.now() - startTime;

        await aisp_logError('内容分析失败', {
            error: error.message,
            stack: error.stack,
            duration,
            contentLength: content?.length
        });

        // 返回友好的错误信息
        const errorResult = {
            summary: '内容分析暂时不可用',
            keyPoints: ['请检查网络连接', '确认API密钥配置正确', '稍后重试'],
            suggestions: ['联系技术支持获取帮助'],
            error: error.userMessage || error.message || '未知错误'
        };

        await aisp_logFunctionExit('aisp_analyzeContent', {
            success: false,
            error: error.message,
            duration
        });

        return errorResult;
    }
}

/**
 * @function aisp_getTemplates - 获取模板数据
 * @returns {Promise<Array>} 模板列表
 */
async function aisp_getTemplates() {
    try {
        const result = await chrome.storage.local.get('aisp_templates');
        return result.aisp_templates || [];
    } catch (error) {
        console.error('获取模板失败:', error);
        throw error;
    }
}

/**
 * @function aisp_saveTemplate - 保存模板
 * @param {Object} template - 模板对象
 * @returns {Promise<void>}
 */
async function aisp_saveTemplate(template) {
    try {
        const templates = await aisp_getTemplates();
        templates.push({
            id: Date.now().toString(),
            ...template,
            createdAt: new Date().toISOString()
        });
        
        await chrome.storage.local.set({ 'aisp_templates': templates });
    } catch (error) {
        console.error('保存模板失败:', error);
        throw error;
    }
}
// #endregion

// #region 标签页事件处理
// 标签页更新事件监听
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url) {
        // 页面加载完成后，可以进行一些初始化操作
        console.log('页面加载完成:', tab.url);
    }
});

// 标签页激活事件监听
chrome.tabs.onActivated.addListener((activeInfo) => {
    console.log('标签页切换:', activeInfo.tabId);
});
// #endregion

console.log('AI Side Panel Service Worker 已加载');
