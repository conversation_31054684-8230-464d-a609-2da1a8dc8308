/**
 * @file AI Side Panel 统一错误处理中间件
 * @description 提供统一的错误处理机制，包括错误分类、日志记录、用户提示和恢复策略
 */

// #region 全局变量和配置
let aisp_errorHandlerInitialized = false;
let aisp_errorStats = {
    totalErrors: 0,
    errorsByType: new Map(),
    errorsByComponent: new Map(),
    recentErrors: [],
    startTime: Date.now()
};

// 错误处理配置
const AISP_ERROR_CONFIG = {
    // 错误级别
    LEVELS: {
        CRITICAL: 'critical',    // 严重错误，影响核心功能
        ERROR: 'error',          // 一般错误，影响部分功能
        WARNING: 'warning',      // 警告，不影响功能但需注意
        INFO: 'info'             // 信息，仅记录
    },
    
    // 错误类型
    TYPES: {
        INITIALIZATION: 'initialization',  // 初始化错误
        API: 'api',                       // API调用错误
        NETWORK: 'network',               // 网络错误
        VALIDATION: 'validation',         // 数据验证错误
        PERMISSION: 'permission',         // 权限错误
        TIMEOUT: 'timeout',               // 超时错误
        UNKNOWN: 'unknown'                // 未知错误
    },
    
    // 处理策略
    STRATEGIES: {
        RETRY: 'retry',           // 重试
        FALLBACK: 'fallback',     // 降级处理
        IGNORE: 'ignore',         // 忽略
        NOTIFY: 'notify',         // 通知用户
        RELOAD: 'reload'          // 重新加载
    },
    
    // 配置选项
    MAX_RECENT_ERRORS: 50,        // 最大保存的最近错误数量
    ERROR_REPORT_INTERVAL: 300000, // 错误报告间隔（5分钟）
    AUTO_RECOVERY_ENABLED: true,   // 是否启用自动恢复
    USER_NOTIFICATION_ENABLED: true // 是否启用用户通知
};

// 错误处理规则
const AISP_ERROR_RULES = {
    // API错误处理规则
    'api': {
        level: AISP_ERROR_CONFIG.LEVELS.ERROR,
        strategy: AISP_ERROR_CONFIG.STRATEGIES.RETRY,
        maxRetries: 3,
        retryDelay: 1000,
        userMessage: 'API调用失败，正在重试...',
        recovery: 'aisp_recoverApiError'
    },
    
    // 网络错误处理规则
    'network': {
        level: AISP_ERROR_CONFIG.LEVELS.ERROR,
        strategy: AISP_ERROR_CONFIG.STRATEGIES.FALLBACK,
        userMessage: '网络连接异常，已切换到离线模式',
        recovery: 'aisp_recoverNetworkError'
    },
    
    // 初始化错误处理规则
    'initialization': {
        level: AISP_ERROR_CONFIG.LEVELS.CRITICAL,
        strategy: AISP_ERROR_CONFIG.STRATEGIES.RELOAD,
        userMessage: '组件初始化失败，正在重新加载...',
        recovery: 'aisp_recoverInitializationError'
    },
    
    // 权限错误处理规则
    'permission': {
        level: AISP_ERROR_CONFIG.LEVELS.WARNING,
        strategy: AISP_ERROR_CONFIG.STRATEGIES.NOTIFY,
        userMessage: '权限不足，请检查扩展程序权限设置',
        recovery: null
    },
    
    // 超时错误处理规则
    'timeout': {
        level: AISP_ERROR_CONFIG.LEVELS.WARNING,
        strategy: AISP_ERROR_CONFIG.STRATEGIES.RETRY,
        maxRetries: 2,
        retryDelay: 2000,
        userMessage: '操作超时，正在重试...',
        recovery: 'aisp_recoverTimeoutError'
    },
    
    // 默认错误处理规则
    'default': {
        level: AISP_ERROR_CONFIG.LEVELS.ERROR,
        strategy: AISP_ERROR_CONFIG.STRATEGIES.NOTIFY,
        userMessage: '发生未知错误，请稍后重试',
        recovery: null
    }
};
// #endregion

// #region 主要错误处理函数
/**
 * @function aisp_handleError - 统一错误处理函数
 * @description 统一处理各种类型的错误，包括分类、记录、通知和恢复
 * @param {Error|string} error - 错误对象或错误消息
 * @param {Object} context - 错误上下文信息
 * @returns {Promise<Object>} 错误处理结果
 */
async function aisp_handleError(error, context = {}) {
    try {
        // 标准化错误对象
        const errorInfo = aisp_normalizeError(error, context);
        
        // 记录错误统计
        aisp_recordErrorStats(errorInfo);
        
        // 记录到日志系统
        aisp_logError(errorInfo);
        
        // 获取处理规则
        const rule = aisp_getErrorRule(errorInfo.type);
        
        // 执行错误处理策略
        const result = await aisp_executeErrorStrategy(errorInfo, rule);
        
        // 通知用户（如果需要）
        if (AISP_ERROR_CONFIG.USER_NOTIFICATION_ENABLED && rule.userMessage) {
            aisp_notifyUser(errorInfo, rule);
        }
        
        return {
            success: true,
            errorInfo,
            strategy: rule.strategy,
            result
        };
        
    } catch (handlingError) {
        console.error('❌ 错误处理器本身发生错误:', handlingError);
        
        // 降级处理：直接记录原始错误
        console.error('❌ 原始错误:', error);
        
        return {
            success: false,
            error: handlingError.message,
            originalError: error
        };
    }
}

/**
 * @function aisp_normalizeError - 标准化错误对象
 * @description 将各种形式的错误转换为标准化的错误信息对象
 * @param {Error|string} error - 原始错误
 * @param {Object} context - 错误上下文
 * @returns {Object} 标准化的错误信息对象
 */
function aisp_normalizeError(error, context) {
    const timestamp = Date.now();
    const errorId = aisp_generateErrorId();
    
    let message, stack, type, component;
    
    if (error instanceof Error) {
        message = error.message;
        stack = error.stack;
        
        // 根据错误消息推断错误类型
        type = aisp_inferErrorType(error);
    } else if (typeof error === 'string') {
        message = error;
        stack = new Error().stack;
        type = AISP_ERROR_CONFIG.TYPES.UNKNOWN;
    } else {
        message = '未知错误';
        stack = new Error().stack;
        type = AISP_ERROR_CONFIG.TYPES.UNKNOWN;
    }
    
    // 从上下文中提取组件信息
    component = context.component || aisp_inferComponent(stack);
    
    return {
        id: errorId,
        message,
        stack,
        type,
        component,
        timestamp,
        context: {
            url: window.location.href,
            userAgent: navigator.userAgent,
            ...context
        }
    };
}

/**
 * @function aisp_inferErrorType - 推断错误类型
 * @description 根据错误对象和消息推断错误类型
 * @param {Error} error - 错误对象
 * @returns {string} 错误类型
 */
function aisp_inferErrorType(error) {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
        return AISP_ERROR_CONFIG.TYPES.NETWORK;
    }
    
    if (message.includes('timeout') || message.includes('超时')) {
        return AISP_ERROR_CONFIG.TYPES.TIMEOUT;
    }
    
    if (message.includes('permission') || message.includes('权限')) {
        return AISP_ERROR_CONFIG.TYPES.PERMISSION;
    }
    
    if (message.includes('api') || message.includes('response')) {
        return AISP_ERROR_CONFIG.TYPES.API;
    }
    
    if (message.includes('initialize') || message.includes('初始化')) {
        return AISP_ERROR_CONFIG.TYPES.INITIALIZATION;
    }
    
    if (message.includes('validation') || message.includes('验证')) {
        return AISP_ERROR_CONFIG.TYPES.VALIDATION;
    }
    
    return AISP_ERROR_CONFIG.TYPES.UNKNOWN;
}

/**
 * @function aisp_inferComponent - 推断错误来源组件
 * @description 根据错误堆栈推断错误来源的组件
 * @param {string} stack - 错误堆栈
 * @returns {string} 组件名称
 */
function aisp_inferComponent(stack) {
    if (!stack) return 'unknown';
    
    const componentPatterns = [
        { pattern: /gemini-api\.js/, component: 'gemini-api' },
        { pattern: /chat-interface\.js/, component: 'chat-interface' },
        { pattern: /template-popup\.js/, component: 'template-popup' },
        { pattern: /mindmap-renderer\.js/, component: 'mindmap-renderer' },
        { pattern: /debug-panel\.js/, component: 'debug-panel' },
        { pattern: /logger\.js/, component: 'logger' },
        { pattern: /cache-manager\.js/, component: 'cache-manager' },
        { pattern: /performance-optimizer\.js/, component: 'performance-optimizer' },
        { pattern: /content-script\.js/, component: 'content-script' },
        { pattern: /service-worker\.js/, component: 'service-worker' },
        { pattern: /sidepanel\.js/, component: 'sidepanel' }
    ];
    
    for (const { pattern, component } of componentPatterns) {
        if (pattern.test(stack)) {
            return component;
        }
    }
    
    return 'unknown';
}

/**
 * @function aisp_generateErrorId - 生成错误ID
 * @description 生成唯一的错误标识符
 * @returns {string} 错误ID
 */
function aisp_generateErrorId() {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);
    return `err_${timestamp}_${random}`;
}
// #endregion

// #region 错误处理策略执行
/**
 * @function aisp_getErrorRule - 获取错误处理规则
 * @description 根据错误类型获取对应的处理规则
 * @param {string} errorType - 错误类型
 * @returns {Object} 错误处理规则
 */
function aisp_getErrorRule(errorType) {
    return AISP_ERROR_RULES[errorType] || AISP_ERROR_RULES['default'];
}

/**
 * @function aisp_executeErrorStrategy - 执行错误处理策略
 * @description 根据错误处理规则执行相应的处理策略
 * @param {Object} errorInfo - 错误信息对象
 * @param {Object} rule - 错误处理规则
 * @returns {Promise<Object>} 处理结果
 */
async function aisp_executeErrorStrategy(errorInfo, rule) {
    switch (rule.strategy) {
        case AISP_ERROR_CONFIG.STRATEGIES.RETRY:
            return await aisp_executeRetryStrategy(errorInfo, rule);

        case AISP_ERROR_CONFIG.STRATEGIES.FALLBACK:
            return await aisp_executeFallbackStrategy(errorInfo, rule);

        case AISP_ERROR_CONFIG.STRATEGIES.IGNORE:
            return { strategy: 'ignore', message: '错误已忽略' };

        case AISP_ERROR_CONFIG.STRATEGIES.NOTIFY:
            return { strategy: 'notify', message: '已通知用户' };

        case AISP_ERROR_CONFIG.STRATEGIES.RELOAD:
            return await aisp_executeReloadStrategy(errorInfo, rule);

        default:
            return { strategy: 'default', message: '使用默认处理' };
    }
}

/**
 * @function aisp_executeRetryStrategy - 执行重试策略
 * @description 执行错误重试策略
 * @param {Object} errorInfo - 错误信息
 * @param {Object} rule - 处理规则
 * @returns {Promise<Object>} 重试结果
 */
async function aisp_executeRetryStrategy(errorInfo, rule) {
    const maxRetries = rule.maxRetries || 3;
    const retryDelay = rule.retryDelay || 1000;

    console.log(`🔄 执行重试策略，最大重试次数: ${maxRetries}`);

    // 如果有恢复函数，尝试执行
    if (rule.recovery && typeof window[rule.recovery] === 'function') {
        try {
            await window[rule.recovery](errorInfo);
            return { strategy: 'retry', success: true, message: '重试成功' };
        } catch (retryError) {
            console.warn('⚠️ 重试失败:', retryError.message);
            return { strategy: 'retry', success: false, message: '重试失败', error: retryError.message };
        }
    }

    return { strategy: 'retry', success: false, message: '无可用的重试方法' };
}

/**
 * @function aisp_executeFallbackStrategy - 执行降级策略
 * @description 执行错误降级策略
 * @param {Object} errorInfo - 错误信息
 * @param {Object} rule - 处理规则
 * @returns {Promise<Object>} 降级结果
 */
async function aisp_executeFallbackStrategy(errorInfo, rule) {
    console.log('📉 执行降级策略');

    // 如果有恢复函数，尝试执行
    if (rule.recovery && typeof window[rule.recovery] === 'function') {
        try {
            await window[rule.recovery](errorInfo);
            return { strategy: 'fallback', success: true, message: '降级处理成功' };
        } catch (fallbackError) {
            console.warn('⚠️ 降级处理失败:', fallbackError.message);
            return { strategy: 'fallback', success: false, message: '降级处理失败', error: fallbackError.message };
        }
    }

    return { strategy: 'fallback', success: false, message: '无可用的降级方法' };
}

/**
 * @function aisp_executeReloadStrategy - 执行重新加载策略
 * @description 执行重新加载策略
 * @param {Object} errorInfo - 错误信息
 * @param {Object} rule - 处理规则
 * @returns {Promise<Object>} 重新加载结果
 */
async function aisp_executeReloadStrategy(errorInfo, rule) {
    console.log('🔄 执行重新加载策略');

    try {
        // 延迟重新加载，给用户看到提示的时间
        setTimeout(() => {
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.reload();
            } else {
                window.location.reload();
            }
        }, 2000);

        return { strategy: 'reload', success: true, message: '正在重新加载...' };

    } catch (reloadError) {
        console.error('❌ 重新加载失败:', reloadError);
        return { strategy: 'reload', success: false, message: '重新加载失败', error: reloadError.message };
    }
}
// #endregion

// #region 错误记录和统计
/**
 * @function aisp_recordErrorStats - 记录错误统计
 * @description 记录错误统计信息，用于分析和监控
 * @param {Object} errorInfo - 错误信息对象
 * @returns {void}
 */
function aisp_recordErrorStats(errorInfo) {
    // 增加总错误数
    aisp_errorStats.totalErrors++;

    // 按类型统计
    const typeCount = aisp_errorStats.errorsByType.get(errorInfo.type) || 0;
    aisp_errorStats.errorsByType.set(errorInfo.type, typeCount + 1);

    // 按组件统计
    const componentCount = aisp_errorStats.errorsByComponent.get(errorInfo.component) || 0;
    aisp_errorStats.errorsByComponent.set(errorInfo.component, componentCount + 1);

    // 添加到最近错误列表
    aisp_errorStats.recentErrors.unshift(errorInfo);

    // 保持最近错误列表大小
    if (aisp_errorStats.recentErrors.length > AISP_ERROR_CONFIG.MAX_RECENT_ERRORS) {
        aisp_errorStats.recentErrors = aisp_errorStats.recentErrors.slice(0, AISP_ERROR_CONFIG.MAX_RECENT_ERRORS);
    }
}

/**
 * @function aisp_logError - 记录错误到日志系统
 * @description 将错误信息记录到统一日志系统
 * @param {Object} errorInfo - 错误信息对象
 * @returns {void}
 */
function aisp_logError(errorInfo) {
    const logMessage = `[${errorInfo.component}] ${errorInfo.message}`;
    const logContext = {
        errorId: errorInfo.id,
        type: errorInfo.type,
        timestamp: errorInfo.timestamp,
        stack: errorInfo.stack,
        context: errorInfo.context
    };

    // 根据错误级别选择日志方法
    const rule = aisp_getErrorRule(errorInfo.type);

    if (typeof aisp_logError === 'function') {
        switch (rule.level) {
            case AISP_ERROR_CONFIG.LEVELS.CRITICAL:
                aisp_logError(logMessage, logContext);
                break;
            case AISP_ERROR_CONFIG.LEVELS.ERROR:
                aisp_logError(logMessage, logContext);
                break;
            case AISP_ERROR_CONFIG.LEVELS.WARNING:
                aisp_logWarn(logMessage, logContext);
                break;
            case AISP_ERROR_CONFIG.LEVELS.INFO:
                aisp_logInfo(logMessage, logContext);
                break;
        }
    } else {
        // 降级到控制台输出
        console.error(`❌ ${logMessage}`, logContext);
    }
}

/**
 * @function aisp_notifyUser - 通知用户错误信息
 * @description 向用户显示错误通知
 * @param {Object} errorInfo - 错误信息对象
 * @param {Object} rule - 错误处理规则
 * @returns {void}
 */
function aisp_notifyUser(errorInfo, rule) {
    const message = rule.userMessage || '发生了一个错误';
    const errorCode = `错误代码: ${errorInfo.id}`;

    // 尝试使用Chrome通知API
    if (typeof chrome !== 'undefined' && chrome.notifications) {
        chrome.notifications.create(errorInfo.id, {
            type: 'basic',
            iconUrl: 'icons/icon-48.png',
            title: 'AI Side Panel',
            message: `${message}\n${errorCode}`
        });
    } else {
        // 降级到浏览器通知
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification('AI Side Panel', {
                body: `${message}\n${errorCode}`,
                icon: 'icons/icon-48.png'
            });
        } else {
            // 最后降级到控制台
            console.warn(`⚠️ 用户通知: ${message} (${errorCode})`);
        }
    }
}

/**
 * @function aisp_getErrorStats - 获取错误统计信息
 * @description 获取当前的错误统计信息
 * @returns {Object} 错误统计对象
 */
function aisp_getErrorStats() {
    const runtime = Date.now() - aisp_errorStats.startTime;

    return {
        totalErrors: aisp_errorStats.totalErrors,
        errorsByType: Object.fromEntries(aisp_errorStats.errorsByType),
        errorsByComponent: Object.fromEntries(aisp_errorStats.errorsByComponent),
        recentErrors: aisp_errorStats.recentErrors.slice(0, 10), // 只返回最近10个
        runtime,
        errorRate: aisp_errorStats.totalErrors / (runtime / 1000 / 60) // 每分钟错误数
    };
}
// #endregion

console.log('🛡️ AI Side Panel 错误处理中间件已加载');
