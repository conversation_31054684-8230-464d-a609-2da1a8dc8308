/**
 * @file AI侧边栏扩展 - 统一日志管理系统
 * @description 提供全局的日志记录、错误处理和调试功能
 * <AUTHOR> Side Panel Team
 */

/**
 * 日志级别枚举
 */
const AISP_LOG_LEVELS = {
    ERROR: 0,   // 错误级别
    WARN: 1,    // 警告级别
    INFO: 2,    // 信息级别
    DEBUG: 3    // 调试级别
};

/**
 * 日志级别名称映射
 */
const AISP_LOG_LEVEL_NAMES = {
    0: 'ERROR',
    1: 'WARN',
    2: 'INFO',
    3: 'DEBUG'
};

/**
 * 日志级别颜色映射（用于控制台输出）
 */
const AISP_LOG_COLORS = {
    ERROR: '#FF4444',   // 红色
    WARN: '#FF8800',    // 橙色
    INFO: '#0088FF',    // 蓝色
    DEBUG: '#888888'    // 灰色
};

/**
 * 全局日志配置
 */
let aispLogConfig = {
    level: AISP_LOG_LEVELS.INFO,           // 默认日志级别
    enableConsole: true,                   // 启用控制台输出
    enableStorage: true,                   // 启用本地存储
    maxStorageEntries: 1000,              // 最大存储条目数
    enableTimestamp: true,                 // 启用时间戳
    enableStackTrace: true,                // 启用堆栈跟踪
    contextPrefix: 'AISP',                 // 上下文前缀
    // 新增：运行时监控配置
    enableRuntimeMonitoring: true,         // 启用运行时监控
    enableAPIMonitoring: true,             // 启用API监控
    enableStreamMonitoring: true,          // 启用流式传输监控
    enablePerformanceMonitoring: true,     // 启用性能监控
    enableMemoryMonitoring: true,          // 启用内存监控
    enableDevMode: false,                  // 开发模式（详细日志）
    monitoringInterval: 5000,              // 监控间隔（毫秒）
    maxAPICallHistory: 100,                // 最大API调用历史记录数
    maxStreamChunkHistory: 50,             // 最大流式数据块历史记录数
    enableRealtimeSync: true,              // 启用实时同步到DevTools
    enableDataFiltering: true              // 启用数据过滤功能
};

/**
 * 运行时监控数据存储
 */
let aispRuntimeData = {
    apiCalls: [],                          // API调用历史
    streamChunks: [],                      // 流式数据块历史
    performanceMetrics: [],                // 性能指标
    memoryUsage: [],                       // 内存使用记录
    userInteractions: [],                  // 用户交互记录
    contentExtractions: [],                // 内容提取记录
    errors: [],                            // 错误记录
    systemStats: {                         // 系统统计
        totalAPIRequests: 0,
        totalStreamChunks: 0,
        totalErrors: 0,
        totalUserActions: 0,
        averageResponseTime: 0,
        lastUpdateTime: Date.now()
    }
};

/**
 * @function aisp_logSetConfig - 设置日志配置
 * @param {Object} config - 日志配置对象
 * @description 更新全局日志配置
 */
function aisp_logSetConfig(config) {
    aispLogConfig = { ...aispLogConfig, ...config };

    // 如果启用了开发模式，设置更详细的日志级别
    if (config.enableDevMode) {
        aispLogConfig.level = AISP_LOG_LEVELS.DEBUG;
        aispLogConfig.enableRuntimeMonitoring = true;
        aispLogConfig.enableRealtimeSync = true;
    }

    aisp_logDebug('日志配置已更新', { config: aispLogConfig });

    // 启动或停止运行时监控
    if (aispLogConfig.enableRuntimeMonitoring) {
        aisp_startRuntimeMonitoring();
    } else {
        aisp_stopRuntimeMonitoring();
    }
}

/**
 * @function aisp_startRuntimeMonitoring - 启动运行时监控
 * @description 开始监控系统运行时数据
 */
function aisp_startRuntimeMonitoring() {
    if (typeof window === 'undefined') return; // 只在浏览器环境中运行

    // 启动性能监控
    if (aispLogConfig.enablePerformanceMonitoring) {
        aisp_startPerformanceMonitoring();
    }

    // 启动内存监控
    if (aispLogConfig.enableMemoryMonitoring) {
        aisp_startMemoryMonitoring();
    }

    aisp_logInfo('运行时监控已启动', {
        performanceMonitoring: aispLogConfig.enablePerformanceMonitoring,
        memoryMonitoring: aispLogConfig.enableMemoryMonitoring,
        interval: aispLogConfig.monitoringInterval
    });
}

/**
 * @function aisp_stopRuntimeMonitoring - 停止运行时监控
 * @description 停止所有运行时监控
 */
function aisp_stopRuntimeMonitoring() {
    if (window.aispPerformanceMonitorInterval) {
        clearInterval(window.aispPerformanceMonitorInterval);
        window.aispPerformanceMonitorInterval = null;
    }

    if (window.aispMemoryMonitorInterval) {
        clearInterval(window.aispMemoryMonitorInterval);
        window.aispMemoryMonitorInterval = null;
    }

    aisp_logInfo('运行时监控已停止');
}

/**
 * @function aisp_logGetConfig - 获取当前日志配置
 * @returns {Object} 当前日志配置
 * @description 返回当前的日志配置
 */
function aisp_logGetConfig() {
    return { ...aispLogConfig };
}

/**
 * @function aisp_startPerformanceMonitoring - 启动性能监控
 * @description 定期收集性能指标数据
 */
function aisp_startPerformanceMonitoring() {
    if (typeof window === 'undefined' || typeof performance === 'undefined') return;

    window.aispPerformanceMonitorInterval = setInterval(() => {
        try {
            const perfData = {
                timestamp: Date.now(),
                navigation: performance.getEntriesByType('navigation')[0] || {},
                memory: performance.memory ? {
                    usedJSHeapSize: performance.memory.usedJSHeapSize,
                    totalJSHeapSize: performance.memory.totalJSHeapSize,
                    jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
                } : null,
                timing: performance.timing ? {
                    domContentLoaded: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart,
                    loadComplete: performance.timing.loadEventEnd - performance.timing.navigationStart
                } : null
            };

            // 添加到性能指标历史
            aispRuntimeData.performanceMetrics.push(perfData);

            // 限制历史记录数量
            if (aispRuntimeData.performanceMetrics.length > 50) {
                aispRuntimeData.performanceMetrics = aispRuntimeData.performanceMetrics.slice(-50);
            }

            // 实时同步到DevTools（如果启用）
            if (aispLogConfig.enableRealtimeSync) {
                aisp_syncToDevTools('performance', perfData);
            }

        } catch (error) {
            aisp_logError('性能监控数据收集失败', { error: error.message });
        }
    }, aispLogConfig.monitoringInterval);
}

/**
 * @function aisp_startMemoryMonitoring - 启动内存监控
 * @description 定期收集内存使用数据
 */
function aisp_startMemoryMonitoring() {
    if (typeof window === 'undefined') return;

    window.aispMemoryMonitorInterval = setInterval(() => {
        try {
            const memoryData = {
                timestamp: Date.now(),
                heap: performance.memory ? {
                    used: performance.memory.usedJSHeapSize,
                    total: performance.memory.totalJSHeapSize,
                    limit: performance.memory.jsHeapSizeLimit,
                    usagePercent: Math.round((performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit) * 100)
                } : null,
                // 估算DOM节点数量
                domNodes: document.querySelectorAll('*').length,
                // 估算事件监听器数量（近似值）
                eventListeners: window.getEventListeners ?
                    Object.keys(window.getEventListeners(document)).length : 0
            };

            // 添加到内存使用历史
            aispRuntimeData.memoryUsage.push(memoryData);

            // 限制历史记录数量
            if (aispRuntimeData.memoryUsage.length > 50) {
                aispRuntimeData.memoryUsage = aispRuntimeData.memoryUsage.slice(-50);
            }

            // 内存使用警告
            if (memoryData.heap && memoryData.heap.usagePercent > 80) {
                aisp_logWarn('内存使用率过高', {
                    usagePercent: memoryData.heap.usagePercent,
                    usedMB: Math.round(memoryData.heap.used / 1024 / 1024),
                    totalMB: Math.round(memoryData.heap.total / 1024 / 1024)
                });
            }

            // 实时同步到DevTools（如果启用）
            if (aispLogConfig.enableRealtimeSync) {
                aisp_syncToDevTools('memory', memoryData);
            }

        } catch (error) {
            aisp_logError('内存监控数据收集失败', { error: error.message });
        }
    }, aispLogConfig.monitoringInterval);
}

/**
 * @function aisp_syncToDevTools - 实时同步数据到DevTools控制台
 * @param {string} dataType - 数据类型
 * @param {Object} data - 要同步的数据
 * @description 将运行时数据实时推送到Chrome DevTools控制台
 */
function aisp_syncToDevTools(dataType, data) {
    if (!aispLogConfig.enableRealtimeSync) return;

    try {
        const syncData = {
            type: 'AISP_RUNTIME_DATA',
            dataType,
            timestamp: Date.now(),
            data,
            context: aisp_logGetContext().context
        };

        // 使用特殊的控制台标记，便于DevTools过滤
        console.group(`%c[AISP-MONITOR] ${dataType.toUpperCase()}`, 'color: #00ff00; font-weight: bold;');
        console.log('数据类型:', dataType);
        console.log('时间戳:', new Date(syncData.timestamp).toLocaleTimeString());
        console.log('上下文:', syncData.context);
        console.log('数据:', data);
        console.groupEnd();

        // 发送到background script进行跨上下文同步
        if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
            chrome.runtime.sendMessage({
                action: 'aisp_runtime_data_sync',
                data: syncData
            }).catch(() => {
                // 忽略发送失败的错误
            });
        }

    } catch (error) {
        // 避免循环日志，直接使用console.error
        console.error('[AISP-SYNC] 实时同步失败:', error);
    }
}

/**
 * @function aisp_logGetContext - 获取当前执行上下文信息
 * @returns {Object} 上下文信息对象
 * @description 检测当前代码运行的Chrome扩展上下文
 */
function aisp_logGetContext() {
    let context = 'unknown';
    let contextDetails = {};

    try {
        // 检测是否在Service Worker中
        if (typeof importScripts === 'function' && typeof chrome !== 'undefined' && chrome.runtime) {
            context = 'background';
            contextDetails.type = 'service-worker';
        }
        // 检测是否在Content Script中
        else if (typeof window !== 'undefined' && window.location && window.location.href !== 'chrome-extension://') {
            context = 'content';
            contextDetails.url = window.location.href;
            contextDetails.domain = window.location.hostname;
        }
        // 检测是否在扩展页面中（sidepanel、popup等）
        else if (typeof window !== 'undefined' && window.location && window.location.href.startsWith('chrome-extension://')) {
            context = 'extension';
            contextDetails.page = window.location.pathname;
        }
        // 检测是否在DevTools中
        else if (typeof chrome !== 'undefined' && chrome.devtools) {
            context = 'devtools';
        }
    } catch (error) {
        context = 'error';
        contextDetails.error = error.message;
    }

    return {
        context,
        timestamp: new Date().toISOString(),
        ...contextDetails
    };
}

/**
 * @function aisp_logFormatMessage - 格式化日志消息
 * @param {string} level - 日志级别
 * @param {string} message - 日志消息
 * @param {Object} data - 附加数据
 * @param {Object} metadata - 元数据
 * @returns {Object} 格式化后的日志对象
 * @description 创建结构化的日志消息对象
 */
function aisp_logFormatMessage(level, message, data = null, metadata = {}) {
    const contextInfo = aisp_logGetContext();
    const timestamp = new Date().toISOString();
    
    // 获取调用堆栈信息
    let stackTrace = null;
    if (aispLogConfig.enableStackTrace && (level === 'ERROR' || level === 'WARN')) {
        try {
            throw new Error();
        } catch (e) {
            stackTrace = e.stack;
        }
    }

    // 构建日志对象
    const logEntry = {
        timestamp,
        level,
        context: contextInfo.context,
        prefix: aispLogConfig.contextPrefix,
        message,
        data,
        metadata: {
            ...metadata,
            contextDetails: contextInfo,
            stackTrace
        }
    };

    return logEntry;
}

/**
 * @function aisp_logToConsole - 输出日志到控制台
 * @param {Object} logEntry - 日志条目对象
 * @description 将格式化的日志输出到浏览器控制台
 */
function aisp_logToConsole(logEntry) {
    if (!aispLogConfig.enableConsole) return;

    const { timestamp, level, context, prefix, message, data, metadata } = logEntry;
    const color = AISP_LOG_COLORS[level];
    
    // 构建控制台输出格式
    const timeStr = aispLogConfig.enableTimestamp ? `[${timestamp.split('T')[1].split('.')[0]}]` : '';
    const contextStr = `[${prefix}-${context.toUpperCase()}]`;
    const levelStr = `[${level}]`;
    
    const fullMessage = `${timeStr} ${contextStr} ${levelStr} ${message}`;

    // 根据级别选择控制台方法
    const consoleMethod = {
        'ERROR': 'error',
        'WARN': 'warn',
        'INFO': 'info',
        'DEBUG': 'log'
    }[level] || 'log';

    // 输出到控制台 - 使用安全的方法调用
    const logArgs = [`%c${fullMessage}`, `color: ${color}; font-weight: bold;`];
    if (data) logArgs.push({ data });
    if (metadata.stackTrace) logArgs.push({ stack: metadata.stackTrace });

    // 根据方法名安全调用console方法
    switch (consoleMethod) {
        case 'error':
            console.error(...logArgs);
            break;
        case 'warn':
            console.warn(...logArgs);
            break;
        case 'info':
            console.info(...logArgs);
            break;
        case 'log':
        default:
            console.log(...logArgs);
            break;
    }
}

/**
 * @function aisp_logToStorage - 保存日志到本地存储
 * @param {Object} logEntry - 日志条目对象
 * @description 将日志保存到Chrome扩展的本地存储中
 */
async function aisp_logToStorage(logEntry) {
    if (!aispLogConfig.enableStorage) return;

    try {
        // 获取现有日志
        const result = await chrome.storage.local.get(['aisp_logs']);
        let logs = result.aisp_logs || [];

        // 添加新日志
        logs.push(logEntry);

        // 限制日志数量
        if (logs.length > aispLogConfig.maxStorageEntries) {
            logs = logs.slice(-aispLogConfig.maxStorageEntries);
        }

        // 保存到存储
        await chrome.storage.local.set({ aisp_logs: logs });
    } catch (error) {
        console.error('保存日志到存储失败:', error);
    }
}

/**
 * @function aisp_logWrite - 核心日志写入函数
 * @param {string} level - 日志级别
 * @param {string} message - 日志消息
 * @param {Object} data - 附加数据
 * @param {Object} metadata - 元数据
 * @description 执行实际的日志写入操作
 */
async function aisp_logWrite(level, message, data = null, metadata = {}) {
    // 检查日志级别
    const levelValue = AISP_LOG_LEVELS[level];
    if (levelValue > aispLogConfig.level) {
        return; // 跳过低优先级日志
    }

    // 格式化日志消息
    const logEntry = aisp_logFormatMessage(level, message, data, metadata);

    // 输出到控制台
    aisp_logToConsole(logEntry);

    // 保存到存储
    await aisp_logToStorage(logEntry);
}

/**
 * @function aisp_logError - 记录错误级别日志
 * @param {string} message - 错误消息
 * @param {Object} data - 错误数据
 * @param {Object} metadata - 元数据
 * @description 记录错误级别的日志信息
 */
async function aisp_logError(message, data = null, metadata = {}) {
    await aisp_logWrite('ERROR', message, data, metadata);
}

/**
 * @function aisp_logWarn - 记录警告级别日志
 * @param {string} message - 警告消息
 * @param {Object} data - 警告数据
 * @param {Object} metadata - 元数据
 * @description 记录警告级别的日志信息
 */
async function aisp_logWarn(message, data = null, metadata = {}) {
    await aisp_logWrite('WARN', message, data, metadata);
}

/**
 * @function aisp_logInfo - 记录信息级别日志
 * @param {string} message - 信息消息
 * @param {Object} data - 信息数据
 * @param {Object} metadata - 元数据
 * @description 记录信息级别的日志信息
 */
async function aisp_logInfo(message, data = null, metadata = {}) {
    await aisp_logWrite('INFO', message, data, metadata);
}

/**
 * @function aisp_logDebug - 记录调试级别日志
 * @param {string} message - 调试消息
 * @param {Object} data - 调试数据
 * @param {Object} metadata - 元数据
 * @description 记录调试级别的日志信息
 */
async function aisp_logDebug(message, data = null, metadata = {}) {
    await aisp_logWrite('DEBUG', message, data, metadata);
}

/**
 * @function aisp_logGetStoredLogs - 获取存储的日志
 * @param {Object} filter - 过滤条件
 * @returns {Array} 日志数组
 * @description 从本地存储中获取日志记录
 */
async function aisp_logGetStoredLogs(filter = {}) {
    try {
        const result = await chrome.storage.local.get(['aisp_logs']);
        let logs = result.aisp_logs || [];

        // 应用过滤条件
        if (filter.level) {
            logs = logs.filter(log => log.level === filter.level);
        }
        if (filter.context) {
            logs = logs.filter(log => log.context === filter.context);
        }
        if (filter.since) {
            const sinceDate = new Date(filter.since);
            logs = logs.filter(log => new Date(log.timestamp) >= sinceDate);
        }

        return logs;
    } catch (error) {
        console.error('获取存储日志失败:', error);
        return [];
    }
}

/**
 * @function aisp_logClearStoredLogs - 清理存储的日志
 * @description 清空本地存储中的所有日志
 */
async function aisp_logClearStoredLogs() {
    try {
        await chrome.storage.local.remove(['aisp_logs']);
        aisp_logInfo('已清理存储的日志');
    } catch (error) {
        aisp_logError('清理存储日志失败', { error: error.message });
    }
}

/**
 * @function aisp_logExportLogs - 导出日志
 * @param {Object} options - 导出选项
 * @returns {string} 导出的日志字符串
 * @description 将日志导出为JSON或文本格式
 */
async function aisp_logExportLogs(options = {}) {
    const { format = 'json', filter = {} } = options;

    try {
        const logs = await aisp_logGetStoredLogs(filter);

        if (format === 'json') {
            return JSON.stringify(logs, null, 2);
        } else if (format === 'text') {
            return logs.map(log => {
                const timeStr = log.timestamp.split('T')[1].split('.')[0];
                return `[${timeStr}] [${log.context.toUpperCase()}] [${log.level}] ${log.message}`;
            }).join('\n');
        }

        return JSON.stringify(logs);
    } catch (error) {
        aisp_logError('导出日志失败', { error: error.message });
        return '';
    }
}

/**
 * @function aisp_logPerformance - 记录性能日志
 * @param {string} operation - 操作名称
 * @param {number} duration - 执行时间（毫秒）
 * @param {Object} data - 附加数据
 * @description 记录性能相关的日志信息
 */
async function aisp_logPerformance(operation, duration, data = {}) {
    const performanceData = {
        operation,
        duration,
        timestamp: Date.now(),
        ...data
    };

    const level = duration > 1000 ? 'WARN' : 'INFO';
    const message = `性能监控: ${operation} 耗时 ${duration}ms`;

    await aisp_logWrite(level, message, performanceData, { type: 'performance' });
}

/**
 * @function aisp_logAPICall - 记录API调用日志
 * @param {string} apiName - API名称
 * @param {string} method - HTTP方法
 * @param {string} url - 请求URL
 * @param {Object} options - 调用选项
 * @description 记录API调用的详细信息
 */
async function aisp_logAPICall(apiName, method, url, options = {}) {
    const apiData = {
        apiName,
        method,
        url,
        timestamp: Date.now(),
        ...options
    };

    await aisp_logInfo(`API调用: ${apiName} ${method} ${url}`, apiData, { type: 'api' });
}

/**
 * @function aisp_logAPIRequest - 记录详细的API请求信息
 * @param {string} apiName - API名称
 * @param {Object} requestData - 请求数据
 * @description 记录API请求的详细信息，包括请求体、头部等
 */
async function aisp_logAPIRequest(apiName, requestData) {
    if (!aispLogConfig.enableAPIMonitoring) return;

    const requestInfo = {
        id: aisp_generateRequestId(),
        apiName,
        method: requestData.method || 'GET',
        url: requestData.url,
        headers: requestData.headers || {},
        body: requestData.body ? aisp_sanitizeRequestBody(requestData.body) : null,
        timestamp: Date.now(),
        context: aisp_logGetContext().context
    };

    // 添加到API调用历史
    aispRuntimeData.apiCalls.push(requestInfo);

    // 限制历史记录数量
    if (aispRuntimeData.apiCalls.length > aispLogConfig.maxAPICallHistory) {
        aispRuntimeData.apiCalls = aispRuntimeData.apiCalls.slice(-aispLogConfig.maxAPICallHistory);
    }

    // 更新统计
    aispRuntimeData.systemStats.totalAPIRequests++;

    await aisp_logInfo(`API请求开始: ${apiName}`, {
        requestId: requestInfo.id,
        method: requestInfo.method,
        url: requestInfo.url,
        bodySize: requestInfo.body ? JSON.stringify(requestInfo.body).length : 0
    }, { type: 'api_request' });

    // 实时同步到DevTools
    if (aispLogConfig.enableRealtimeSync) {
        aisp_syncToDevTools('api_request', requestInfo);
    }

    return requestInfo.id;
}

/**
 * @function aisp_logAPIResponse - 记录API响应信息
 * @param {string} requestId - 请求ID
 * @param {Object} responseData - 响应数据
 * @description 记录API响应的详细信息
 */
async function aisp_logAPIResponse(requestId, responseData) {
    if (!aispLogConfig.enableAPIMonitoring) return;

    const responseInfo = {
        requestId,
        status: responseData.status,
        statusText: responseData.statusText,
        headers: responseData.headers || {},
        body: responseData.body ? aisp_sanitizeResponseBody(responseData.body) : null,
        responseTime: responseData.responseTime || 0,
        timestamp: Date.now(),
        success: responseData.status >= 200 && responseData.status < 300
    };

    // 查找对应的请求记录并更新
    const requestIndex = aispRuntimeData.apiCalls.findIndex(call => call.id === requestId);
    if (requestIndex !== -1) {
        aispRuntimeData.apiCalls[requestIndex].response = responseInfo;
        aispRuntimeData.apiCalls[requestIndex].completed = true;
        aispRuntimeData.apiCalls[requestIndex].duration = responseInfo.responseTime;
    }

    // 更新平均响应时间
    const completedCalls = aispRuntimeData.apiCalls.filter(call => call.completed);
    if (completedCalls.length > 0) {
        const totalTime = completedCalls.reduce((sum, call) => sum + (call.duration || 0), 0);
        aispRuntimeData.systemStats.averageResponseTime = Math.round(totalTime / completedCalls.length);
    }

    const logLevel = responseInfo.success ? 'INFO' : 'ERROR';
    const message = `API响应${responseInfo.success ? '成功' : '失败'}: ${responseInfo.status}`;

    await aisp_logWrite(logLevel, message, {
        requestId,
        status: responseInfo.status,
        responseTime: responseInfo.responseTime,
        bodySize: responseInfo.body ? JSON.stringify(responseInfo.body).length : 0
    }, { type: 'api_response' });

    // 实时同步到DevTools
    if (aispLogConfig.enableRealtimeSync) {
        aisp_syncToDevTools('api_response', responseInfo);
    }
}

/**
 * @function aisp_logStreamChunk - 记录流式传输数据块
 * @param {string} streamId - 流ID
 * @param {Object} chunkData - 数据块信息
 * @description 记录流式传输的数据块信息
 */
async function aisp_logStreamChunk(streamId, chunkData) {
    if (!aispLogConfig.enableStreamMonitoring) return;

    const chunkInfo = {
        streamId,
        chunkIndex: chunkData.index || 0,
        chunkSize: chunkData.size || 0,
        content: chunkData.content ? aisp_sanitizeStreamContent(chunkData.content) : null,
        timestamp: Date.now(),
        isComplete: chunkData.isComplete || false,
        context: aisp_logGetContext().context
    };

    // 添加到流式数据块历史
    aispRuntimeData.streamChunks.push(chunkInfo);

    // 限制历史记录数量
    if (aispRuntimeData.streamChunks.length > aispLogConfig.maxStreamChunkHistory) {
        aispRuntimeData.streamChunks = aispRuntimeData.streamChunks.slice(-aispLogConfig.maxStreamChunkHistory);
    }

    // 更新统计
    aispRuntimeData.systemStats.totalStreamChunks++;

    await aisp_logDebug(`流式数据块: ${streamId}`, {
        chunkIndex: chunkInfo.chunkIndex,
        chunkSize: chunkInfo.chunkSize,
        isComplete: chunkInfo.isComplete
    }, { type: 'stream_chunk' });

    // 实时同步到DevTools
    if (aispLogConfig.enableRealtimeSync) {
        aisp_syncToDevTools('stream_chunk', chunkInfo);
    }
}

/**
 * @function aisp_logContentExtraction - 记录内容提取信息
 * @param {string} extractionType - 提取类型
 * @param {Object} extractionData - 提取数据
 * @description 记录内容提取的详细信息
 */
async function aisp_logContentExtraction(extractionType, extractionData) {
    const extractionInfo = {
        type: extractionType,
        url: extractionData.url || window.location?.href,
        contentLength: extractionData.contentLength || 0,
        elementsCount: extractionData.elementsCount || 0,
        extractionTime: extractionData.extractionTime || 0,
        success: extractionData.success !== false,
        timestamp: Date.now(),
        context: aisp_logGetContext().context
    };

    // 添加到内容提取历史
    aispRuntimeData.contentExtractions.push(extractionInfo);

    // 限制历史记录数量
    if (aispRuntimeData.contentExtractions.length > 50) {
        aispRuntimeData.contentExtractions = aispRuntimeData.contentExtractions.slice(-50);
    }

    const logLevel = extractionInfo.success ? 'INFO' : 'WARN';
    const message = `内容提取${extractionInfo.success ? '成功' : '失败'}: ${extractionType}`;

    await aisp_logWrite(logLevel, message, {
        type: extractionType,
        contentLength: extractionInfo.contentLength,
        elementsCount: extractionInfo.elementsCount,
        extractionTime: extractionInfo.extractionTime
    }, { type: 'content_extraction' });

    // 实时同步到DevTools
    if (aispLogConfig.enableRealtimeSync) {
        aisp_syncToDevTools('content_extraction', extractionInfo);
    }
}

/**
 * @function aisp_logUserAction - 记录用户操作日志
 * @param {string} action - 操作类型
 * @param {Object} details - 操作详情
 * @description 记录用户交互操作
 */
async function aisp_logUserAction(action, details = {}) {
    const actionData = {
        action,
        timestamp: Date.now(),
        context: aisp_logGetContext().context,
        ...details
    };

    // 添加到用户交互历史
    aispRuntimeData.userInteractions.push(actionData);

    // 限制历史记录数量
    if (aispRuntimeData.userInteractions.length > 100) {
        aispRuntimeData.userInteractions = aispRuntimeData.userInteractions.slice(-100);
    }

    // 更新统计
    aispRuntimeData.systemStats.totalUserActions++;

    await aisp_logInfo(`用户操作: ${action}`, actionData, { type: 'user_action' });

    // 实时同步到DevTools
    if (aispLogConfig.enableRealtimeSync) {
        aisp_syncToDevTools('user_action', actionData);
    }
}

/**
 * @function aisp_logFunctionEntry - 记录函数进入日志
 * @param {string} functionName - 函数名称
 * @param {Object} params - 函数参数
 * @description 记录函数调用的入口点
 */
async function aisp_logFunctionEntry(functionName, params = {}) {
    await aisp_logDebug(`进入函数: ${functionName}`, { params }, { type: 'function_entry' });
}

/**
 * @function aisp_logFunctionExit - 记录函数退出日志
 * @param {string} functionName - 函数名称
 * @param {Object} result - 函数返回值
 * @description 记录函数调用的退出点
 */
async function aisp_logFunctionExit(functionName, result = {}) {
    await aisp_logDebug(`退出函数: ${functionName}`, { result }, { type: 'function_exit' });
}

/**
 * @function aisp_logWithTimer - 带计时器的日志记录
 * @param {string} operation - 操作名称
 * @param {Function} fn - 要执行的函数
 * @returns {*} 函数执行结果
 * @description 自动记录函数执行时间
 */
async function aisp_logWithTimer(operation, fn) {
    const startTime = Date.now();
    aisp_logDebug(`开始执行: ${operation}`);

    try {
        const result = await fn();
        const duration = Date.now() - startTime;
        await aisp_logPerformance(operation, duration, { success: true });
        return result;
    } catch (error) {
        const duration = Date.now() - startTime;
        await aisp_logError(`执行失败: ${operation}`, {
            error: error.message,
            duration
        });
        throw error;
    }
}

/**
 * @function aisp_generateRequestId - 生成请求ID
 * @returns {string} 唯一的请求ID
 * @description 生成用于跟踪API请求的唯一标识符
 */
function aisp_generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * @function aisp_sanitizeRequestBody - 清理请求体数据
 * @param {*} body - 原始请求体
 * @returns {*} 清理后的请求体
 * @description 移除敏感信息，限制数据大小
 */
function aisp_sanitizeRequestBody(body) {
    if (!body) return null;

    try {
        let sanitized = typeof body === 'string' ? JSON.parse(body) : body;

        // 移除敏感字段
        if (typeof sanitized === 'object') {
            const sensitiveFields = ['password', 'token', 'key', 'secret', 'auth'];
            sensitiveFields.forEach(field => {
                if (sanitized[field]) {
                    sanitized[field] = '[REDACTED]';
                }
            });
        }

        // 限制大小
        const jsonStr = JSON.stringify(sanitized);
        if (jsonStr.length > 1000) {
            return jsonStr.substring(0, 1000) + '...[TRUNCATED]';
        }

        return sanitized;
    } catch (error) {
        return '[PARSE_ERROR]';
    }
}

/**
 * @function aisp_sanitizeResponseBody - 清理响应体数据
 * @param {*} body - 原始响应体
 * @returns {*} 清理后的响应体
 * @description 限制响应数据大小，保护敏感信息
 */
function aisp_sanitizeResponseBody(body) {
    if (!body) return null;

    try {
        const jsonStr = typeof body === 'string' ? body : JSON.stringify(body);
        if (jsonStr.length > 2000) {
            return jsonStr.substring(0, 2000) + '...[TRUNCATED]';
        }
        return typeof body === 'string' ? JSON.parse(body) : body;
    } catch (error) {
        return '[PARSE_ERROR]';
    }
}

/**
 * @function aisp_sanitizeStreamContent - 清理流式内容
 * @param {string} content - 原始内容
 * @returns {string} 清理后的内容
 * @description 限制流式内容的大小
 */
function aisp_sanitizeStreamContent(content) {
    if (!content || typeof content !== 'string') return content;

    if (content.length > 500) {
        return content.substring(0, 500) + '...[TRUNCATED]';
    }

    return content;
}

/**
 * @function aisp_getRuntimeData - 获取运行时监控数据
 * @param {Object} filter - 过滤条件
 * @returns {Object} 运行时数据
 * @description 获取当前的运行时监控数据
 */
function aisp_getRuntimeData(filter = {}) {
    const data = { ...aispRuntimeData };

    // 应用时间过滤
    if (filter.since) {
        const sinceTime = new Date(filter.since).getTime();
        data.apiCalls = data.apiCalls.filter(call => call.timestamp >= sinceTime);
        data.streamChunks = data.streamChunks.filter(chunk => chunk.timestamp >= sinceTime);
        data.performanceMetrics = data.performanceMetrics.filter(metric => metric.timestamp >= sinceTime);
        data.memoryUsage = data.memoryUsage.filter(usage => usage.timestamp >= sinceTime);
        data.userInteractions = data.userInteractions.filter(action => action.timestamp >= sinceTime);
        data.contentExtractions = data.contentExtractions.filter(extraction => extraction.timestamp >= sinceTime);
    }

    // 应用类型过滤
    if (filter.type) {
        const filteredData = {};
        if (filter.type === 'api') filteredData.apiCalls = data.apiCalls;
        if (filter.type === 'stream') filteredData.streamChunks = data.streamChunks;
        if (filter.type === 'performance') filteredData.performanceMetrics = data.performanceMetrics;
        if (filter.type === 'memory') filteredData.memoryUsage = data.memoryUsage;
        if (filter.type === 'user') filteredData.userInteractions = data.userInteractions;
        if (filter.type === 'content') filteredData.contentExtractions = data.contentExtractions;
        return { ...filteredData, systemStats: data.systemStats };
    }

    return data;
}

/**
 * @function aisp_clearRuntimeData - 清理运行时数据
 * @param {string} dataType - 要清理的数据类型（可选）
 * @description 清理指定类型或所有运行时监控数据
 */
function aisp_clearRuntimeData(dataType = null) {
    if (dataType) {
        switch (dataType) {
            case 'api':
                aispRuntimeData.apiCalls = [];
                break;
            case 'stream':
                aispRuntimeData.streamChunks = [];
                break;
            case 'performance':
                aispRuntimeData.performanceMetrics = [];
                break;
            case 'memory':
                aispRuntimeData.memoryUsage = [];
                break;
            case 'user':
                aispRuntimeData.userInteractions = [];
                break;
            case 'content':
                aispRuntimeData.contentExtractions = [];
                break;
            case 'errors':
                aispRuntimeData.errors = [];
                break;
        }
    } else {
        // 清理所有数据
        aispRuntimeData.apiCalls = [];
        aispRuntimeData.streamChunks = [];
        aispRuntimeData.performanceMetrics = [];
        aispRuntimeData.memoryUsage = [];
        aispRuntimeData.userInteractions = [];
        aispRuntimeData.contentExtractions = [];
        aispRuntimeData.errors = [];

        // 重置统计
        aispRuntimeData.systemStats = {
            totalAPIRequests: 0,
            totalStreamChunks: 0,
            totalErrors: 0,
            totalUserActions: 0,
            averageResponseTime: 0,
            lastUpdateTime: Date.now()
        };
    }

    aisp_logInfo(`运行时数据已清理: ${dataType || '全部'}`);
}

/**
 * @function aisp_logCreateLogger - 创建带上下文的日志记录器
 * @param {string} context - 上下文名称
 * @returns {Object} 日志记录器对象
 * @description 创建一个带有特定上下文的日志记录器
 */
function aisp_logCreateLogger(context) {
    return {
        error: (message, data, metadata = {}) =>
            aisp_logError(message, data, { ...metadata, context }),
        warn: (message, data, metadata = {}) =>
            aisp_logWarn(message, data, { ...metadata, context }),
        info: (message, data, metadata = {}) =>
            aisp_logInfo(message, data, { ...metadata, context }),
        debug: (message, data, metadata = {}) =>
            aisp_logDebug(message, data, { ...metadata, context }),
        performance: (operation, duration, data = {}) =>
            aisp_logPerformance(operation, duration, { ...data, context }),
        apiCall: (apiName, method, url, options = {}) =>
            aisp_logAPICall(apiName, method, url, { ...options, context }),
        apiRequest: (apiName, requestData) =>
            aisp_logAPIRequest(apiName, requestData),
        apiResponse: (requestId, responseData) =>
            aisp_logAPIResponse(requestId, responseData),
        streamChunk: (streamId, chunkData) =>
            aisp_logStreamChunk(streamId, chunkData),
        contentExtraction: (extractionType, extractionData) =>
            aisp_logContentExtraction(extractionType, extractionData),
        userAction: (action, details = {}) =>
            aisp_logUserAction(action, { ...details, context }),
        functionEntry: (functionName, params = {}) =>
            aisp_logFunctionEntry(functionName, { ...params, context }),
        functionExit: (functionName, result = {}) =>
            aisp_logFunctionExit(functionName, { ...result, context }),
        withTimer: (operation, fn) =>
            aisp_logWithTimer(`${context}: ${operation}`, fn)
    };
}

// 导出全局日志函数（用于其他模块导入）
if (typeof self !== 'undefined' && typeof importScripts === 'function') {
    // Service Worker环境
    self.aisp_logError = aisp_logError;
    self.aisp_logWarn = aisp_logWarn;
    self.aisp_logInfo = aisp_logInfo;
    self.aisp_logDebug = aisp_logDebug;
    self.aisp_logPerformance = aisp_logPerformance;
    self.aisp_logAPICall = aisp_logAPICall;
    self.aisp_logAPIRequest = aisp_logAPIRequest;
    self.aisp_logAPIResponse = aisp_logAPIResponse;
    self.aisp_logStreamChunk = aisp_logStreamChunk;
    self.aisp_logContentExtraction = aisp_logContentExtraction;
    self.aisp_logUserAction = aisp_logUserAction;
    self.aisp_logFunctionEntry = aisp_logFunctionEntry;
    self.aisp_logFunctionExit = aisp_logFunctionExit;
    self.aisp_logWithTimer = aisp_logWithTimer;
    self.aisp_logCreateLogger = aisp_logCreateLogger;
    self.aisp_logSetConfig = aisp_logSetConfig;
    self.aisp_logGetConfig = aisp_logGetConfig;
    self.aisp_logGetStoredLogs = aisp_logGetStoredLogs;
    self.aisp_logClearStoredLogs = aisp_logClearStoredLogs;
    self.aisp_logExportLogs = aisp_logExportLogs;
    self.aisp_getRuntimeData = aisp_getRuntimeData;
    self.aisp_clearRuntimeData = aisp_clearRuntimeData;
    self.aisp_startRuntimeMonitoring = aisp_startRuntimeMonitoring;
    self.aisp_stopRuntimeMonitoring = aisp_stopRuntimeMonitoring;
} else if (typeof window !== 'undefined') {
    // 在扩展页面中，将函数添加到window对象
    window.aisp_logError = aisp_logError;
    window.aisp_logWarn = aisp_logWarn;
    window.aisp_logInfo = aisp_logInfo;
    window.aisp_logDebug = aisp_logDebug;
    window.aisp_logPerformance = aisp_logPerformance;
    window.aisp_logAPICall = aisp_logAPICall;
    window.aisp_logAPIRequest = aisp_logAPIRequest;
    window.aisp_logAPIResponse = aisp_logAPIResponse;
    window.aisp_logStreamChunk = aisp_logStreamChunk;
    window.aisp_logContentExtraction = aisp_logContentExtraction;
    window.aisp_logUserAction = aisp_logUserAction;
    window.aisp_logFunctionEntry = aisp_logFunctionEntry;
    window.aisp_logFunctionExit = aisp_logFunctionExit;
    window.aisp_logWithTimer = aisp_logWithTimer;
    window.aisp_logCreateLogger = aisp_logCreateLogger;
    window.aisp_logSetConfig = aisp_logSetConfig;
    window.aisp_logGetConfig = aisp_logGetConfig;
    window.aisp_logGetStoredLogs = aisp_logGetStoredLogs;
    window.aisp_logClearStoredLogs = aisp_logClearStoredLogs;
    window.aisp_logExportLogs = aisp_logExportLogs;
    window.aisp_getRuntimeData = aisp_getRuntimeData;
    window.aisp_clearRuntimeData = aisp_clearRuntimeData;
    window.aisp_startRuntimeMonitoring = aisp_startRuntimeMonitoring;
    window.aisp_stopRuntimeMonitoring = aisp_stopRuntimeMonitoring;
}

// 初始化日志系统
(async () => {
    try {
        await aisp_logInfo('AI侧边栏增强日志系统已初始化', {
            config: aispLogConfig,
            features: {
                runtimeMonitoring: aispLogConfig.enableRuntimeMonitoring,
                apiMonitoring: aispLogConfig.enableAPIMonitoring,
                streamMonitoring: aispLogConfig.enableStreamMonitoring,
                performanceMonitoring: aispLogConfig.enablePerformanceMonitoring,
                memoryMonitoring: aispLogConfig.enableMemoryMonitoring,
                realtimeSync: aispLogConfig.enableRealtimeSync
            }
        });

        // 自动启动运行时监控（如果启用）
        if (aispLogConfig.enableRuntimeMonitoring) {
            aisp_startRuntimeMonitoring();
        }

    } catch (error) {
        console.error('[AISP-LOG] 日志系统初始化失败:', error);
    }
})();
