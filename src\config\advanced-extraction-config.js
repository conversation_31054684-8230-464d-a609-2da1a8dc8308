/**
 * @file 高级内容提取配置文件
 * @description 管理高级内容提取功能的配置选项和网站特定规则
 */

// #region 高级提取配置

/**
 * 高级内容提取全局配置
 */
const ADVANCED_EXTRACTION_CONFIG = {
    // 功能开关
    FEATURES: {
        SHADOW_DOM: true,           // Shadow DOM内容提取
        IFRAME_EXTRACTION: true,    // iframe内容提取
        CANVAS_OCR: true,          // Canvas文字识别
        SVG_TEXT: true,            // SVG文字提取
        DYNAMIC_CONTENT: true,     // 动态内容检测
        SPA_ROUTING: true,         // SPA路由检测
        LAZY_LOADING: true,        // 懒加载内容检测
        INFINITE_SCROLL: true      // 无限滚动检测
    },

    // 反爬虫策略
    ANTI_DETECTION: {
        ENABLE: true,
        RANDOMIZE_TIMING: true,     // 随机化时间间隔
        SIMULATE_HUMAN: true,       // 模拟人类行为
        RESPECT_ROBOTS: true,       // 遵守robots.txt
        RATE_LIMITING: true,        // 频率限制
        MIN_INTERVAL: 1000,         // 最小间隔（毫秒）
        MAX_INTERVAL: 5000,         // 最大间隔（毫秒）
        RETRY_ATTEMPTS: 3,          // 重试次数
        BACKOFF_MULTIPLIER: 2       // 退避倍数
    },

    // 性能优化
    PERFORMANCE: {
        TIMEOUT: 30000,             // 超时时间（毫秒）
        BATCH_SIZE: 10,             // 批处理大小
        CACHE_TTL: 300000,          // 缓存生存时间（5分钟）
        QUALITY_THRESHOLD: 0.7,     // 内容质量阈值
        MAX_RETRIES: 3,             // 最大重试次数
        CONCURRENT_LIMIT: 5         // 并发限制
    },

    // 内容质量评估
    QUALITY_METRICS: {
        MIN_TEXT_LENGTH: 50,        // 最小文本长度
        MIN_WORD_COUNT: 10,         // 最小单词数
        CONTENT_DENSITY: 0.1,       // 内容密度阈值
        STRUCTURE_WEIGHT: 0.3,      // 结构化内容权重
        UNIQUENESS_WEIGHT: 0.4,     // 唯一性权重
        RELEVANCE_WEIGHT: 0.3       // 相关性权重
    }
};

/**
 * 网站特定配置规则
 */
const SITE_SPECIFIC_RULES = {
    // 社交媒体平台
    'twitter.com': {
        selectors: {
            posts: '[data-testid="tweet"]',
            content: '[data-testid="tweetText"]',
            author: '[data-testid="User-Name"]',
            timestamp: 'time'
        },
        features: {
            DYNAMIC_CONTENT: true,
            INFINITE_SCROLL: true,
            SPA_ROUTING: true
        },
        antiDetection: {
            MIN_INTERVAL: 2000,
            SIMULATE_HUMAN: true
        }
    },

    'facebook.com': {
        selectors: {
            posts: '[role="article"]',
            content: '[data-ad-preview="message"]',
            author: 'strong',
            timestamp: 'abbr'
        },
        features: {
            DYNAMIC_CONTENT: true,
            INFINITE_SCROLL: true
        }
    },

    'linkedin.com': {
        selectors: {
            posts: '.feed-shared-update-v2',
            content: '.feed-shared-text',
            author: '.feed-shared-actor__name'
        },
        features: {
            DYNAMIC_CONTENT: true,
            SPA_ROUTING: true
        }
    },

    // 新闻网站
    'news.ycombinator.com': {
        selectors: {
            articles: '.athing',
            title: '.titleline > a',
            comments: '.comment'
        },
        features: {
            DYNAMIC_CONTENT: false,
            INFINITE_SCROLL: false
        }
    },

    'reddit.com': {
        selectors: {
            posts: '[data-testid="post-container"]',
            content: '[data-testid="post-content"]',
            comments: '[data-testid="comment"]'
        },
        features: {
            DYNAMIC_CONTENT: true,
            INFINITE_SCROLL: true,
            SPA_ROUTING: true
        }
    },

    // 电商网站
    'amazon.com': {
        selectors: {
            product: '#dp-container',
            title: '#productTitle',
            price: '.a-price-whole',
            description: '#feature-bullets',
            reviews: '[data-hook="review-body"]'
        },
        features: {
            LAZY_LOADING: true,
            DYNAMIC_CONTENT: true
        },
        antiDetection: {
            MIN_INTERVAL: 3000,
            SIMULATE_HUMAN: true,
            RATE_LIMITING: true
        }
    },

    'ebay.com': {
        selectors: {
            product: '.x-item-title-label',
            price: '.notranslate',
            description: '.u-flL.condText'
        },
        features: {
            LAZY_LOADING: true
        }
    },

    // 技术文档网站
    'stackoverflow.com': {
        selectors: {
            question: '.question',
            answers: '.answer',
            code: 'pre code'
        },
        features: {
            DYNAMIC_CONTENT: true,
            SPA_ROUTING: true
        }
    },

    'github.com': {
        selectors: {
            readme: '.markdown-body',
            code: '.highlight',
            issues: '.js-issue-row'
        },
        features: {
            DYNAMIC_CONTENT: true,
            SPA_ROUTING: true,
            LAZY_LOADING: true
        }
    },

    // 默认规则
    'default': {
        selectors: {
            main: 'main, article, .content, .post, .article',
            title: 'h1, h2, .title, .headline',
            content: 'p, .text, .content',
            images: 'img',
            links: 'a'
        },
        features: {
            SHADOW_DOM: true,
            IFRAME_EXTRACTION: true,
            CANVAS_OCR: true,
            SVG_TEXT: true,
            DYNAMIC_CONTENT: true,
            LAZY_LOADING: true
        },
        antiDetection: {
            ENABLE: true,
            MIN_INTERVAL: 1000,
            SIMULATE_HUMAN: true
        }
    }
};

/**
 * 内容类型检测规则
 */
const CONTENT_TYPE_DETECTION = {
    'article': {
        indicators: ['article', '.post', '.blog-post', '.news-article'],
        textSelectors: ['p', '.content', '.text'],
        metaSelectors: ['.author', '.date', '.publish-date'],
        minTextLength: 500,
        weight: 1.0
    },
    'product': {
        indicators: ['.product', '.item', '.listing'],
        textSelectors: ['.description', '.details'],
        metaSelectors: ['.price', '.rating', '.reviews'],
        minTextLength: 100,
        weight: 0.9
    },
    'social': {
        indicators: ['.post', '.tweet', '.status', '.update'],
        textSelectors: ['.message', '.text', '.content'],
        metaSelectors: ['.author', '.timestamp', '.likes'],
        minTextLength: 10,
        weight: 0.8
    },
    'forum': {
        indicators: ['.thread', '.topic', '.discussion'],
        textSelectors: ['.message', '.post-content'],
        metaSelectors: ['.author', '.post-date', '.replies'],
        minTextLength: 50,
        weight: 0.7
    },
    'documentation': {
        indicators: ['.docs', '.documentation', '.manual'],
        textSelectors: ['.content', '.section'],
        metaSelectors: ['.toc', '.navigation'],
        minTextLength: 200,
        weight: 0.9
    }
};

/**
 * 反爬虫检测规避策略
 */
const ANTI_DETECTION_STRATEGIES = {
    // 用户代理字符串（Chrome扩展中通常不需要修改）
    USER_AGENTS: [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    ],

    // 请求头配置
    HEADERS: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    },

    // 行为模拟参数
    BEHAVIOR_SIMULATION: {
        MOUSE_MOVEMENT: {
            enabled: true,
            frequency: 0.1,     // 10%的概率触发
            maxDistance: 100    // 最大移动距离（像素）
        },
        SCROLL_SIMULATION: {
            enabled: true,
            frequency: 0.05,    // 5%的概率触发
            maxScroll: 50       // 最大滚动距离（像素）
        },
        RANDOM_DELAYS: {
            enabled: true,
            minDelay: 500,      // 最小延迟（毫秒）
            maxDelay: 2000      // 最大延迟（毫秒）
        }
    }
};

// #endregion

// #region 导出配置

// 导出配置对象
if (typeof self !== 'undefined' && typeof importScripts === 'function') {
    // Service Worker环境
    self.ADVANCED_EXTRACTION_CONFIG = ADVANCED_EXTRACTION_CONFIG;
    self.SITE_SPECIFIC_RULES = SITE_SPECIFIC_RULES;
    self.CONTENT_TYPE_DETECTION = CONTENT_TYPE_DETECTION;
    self.ANTI_DETECTION_STRATEGIES = ANTI_DETECTION_STRATEGIES;
} else if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        ADVANCED_EXTRACTION_CONFIG,
        SITE_SPECIFIC_RULES,
        CONTENT_TYPE_DETECTION,
        ANTI_DETECTION_STRATEGIES
    };
} else if (typeof window !== 'undefined') {
    // 浏览器环境
    window.ADVANCED_EXTRACTION_CONFIG = ADVANCED_EXTRACTION_CONFIG;
    window.SITE_SPECIFIC_RULES = SITE_SPECIFIC_RULES;
    window.CONTENT_TYPE_DETECTION = CONTENT_TYPE_DETECTION;
    window.ANTI_DETECTION_STRATEGIES = ANTI_DETECTION_STRATEGIES;
}

// #endregion

console.log('高级内容提取配置文件已加载');
