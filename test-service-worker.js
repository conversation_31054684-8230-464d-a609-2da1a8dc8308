/**
 * @file 测试Service Worker
 * @description 用于测试修复后的Service Worker是否能正常加载
 */

// #region 依赖导入测试
try {
    console.log('开始测试Service Worker依赖导入...');
    
    // 导入API配置
    importScripts('src/config/api-keys.js');
    console.log('✅ API配置导入成功');

    // 导入日志系统
    importScripts('src/utils/logger.js');
    console.log('✅ 日志系统导入成功');

    // 导入工具脚本
    importScripts('src/utils/config-manager.js');
    console.log('✅ 配置管理器导入成功');
    
    importScripts('src/utils/gemini-api.js');
    console.log('✅ Gemini API导入成功');
    
    importScripts('src/utils/api-manager.js');
    console.log('✅ API管理器导入成功');

    // 测试日志系统
    if (typeof aisp_logCreateLogger === 'function') {
        const testLogger = aisp_logCreateLogger('test');
        testLogger.info('测试日志系统正常工作');
        console.log('✅ 日志系统功能正常');
    } else {
        console.warn('⚠️ 日志系统函数不可用');
    }

    // 测试API密钥状态
    if (typeof getApiKeyStatus === 'function') {
        const keyStatus = getApiKeyStatus();
        console.log('✅ API密钥状态检查正常:', keyStatus);
    } else {
        console.warn('⚠️ API密钥状态函数不可用');
    }

    console.log('🎉 所有依赖导入测试通过！');

} catch (error) {
    console.error('❌ Service Worker依赖导入失败:', error);
    console.error('错误详情:', {
        message: error.message,
        stack: error.stack,
        name: error.name
    });
}
// #endregion

// #region 基础功能测试
console.log('开始基础功能测试...');

// 测试Chrome API可用性
if (typeof chrome !== 'undefined') {
    console.log('✅ Chrome API可用');
    
    if (chrome.runtime) {
        console.log('✅ chrome.runtime可用');
    }
    
    if (chrome.storage) {
        console.log('✅ chrome.storage可用');
    }
    
    if (chrome.tabs) {
        console.log('✅ chrome.tabs可用');
    }
} else {
    console.error('❌ Chrome API不可用');
}

// 测试全局对象
console.log('测试全局对象可用性:');
console.log('- typeof window:', typeof window);
console.log('- typeof document:', typeof document);
console.log('- typeof navigator:', typeof navigator);
console.log('- typeof console:', typeof console);
console.log('- typeof importScripts:', typeof importScripts);

console.log('🎉 基础功能测试完成！');
// #endregion

console.log('🚀 测试Service Worker加载完成');
