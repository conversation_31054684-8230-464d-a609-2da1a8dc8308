/**
 * @file AI Side Panel 缓存管理系统
 * @description 智能缓存管理，提升API调用性能，减少重复请求，优化内存使用
 */

// #region 全局变量
let cm_isInitialized = false;
let cm_caches = new Map();
let cm_cacheStats = {
    hits: 0,
    misses: 0,
    evictions: 0,
    totalRequests: 0
};

// 缓存配置
const CACHE_CONFIG = {
    // API响应缓存
    API_RESPONSES: {
        maxSize: 100,           // 最大缓存条目数
        ttl: 300000,           // 5分钟TTL
        cleanupInterval: 60000  // 1分钟清理间隔
    },
    // 文件列表缓存
    FILE_LISTS: {
        maxSize: 50,
        ttl: 180000,           // 3分钟TTL
        cleanupInterval: 60000
    },
    // 模板数据缓存
    TEMPLATES: {
        maxSize: 20,
        ttl: 600000,           // 10分钟TTL
        cleanupInterval: 120000
    },
    // 用户配置缓存
    USER_CONFIG: {
        maxSize: 10,
        ttl: 1800000,          // 30分钟TTL
        cleanupInterval: 300000
    },
    // 页面内容缓存
    PAGE_CONTENT: {
        maxSize: 30,
        ttl: 120000,           // 2分钟TTL
        cleanupInterval: 60000
    }
};

// 缓存类型
const CACHE_TYPES = {
    API_RESPONSES: 'api_responses',
    FILE_LISTS: 'file_lists',
    TEMPLATES: 'templates',
    USER_CONFIG: 'user_config',
    PAGE_CONTENT: 'page_content'
};
// #endregion

// #region 缓存项类
/**
 * @class CacheItem - 缓存项
 * @description 单个缓存项的数据结构
 */
class CacheItem {
    /**
     * @function constructor - 构造函数
     * @param {string} key - 缓存键
     * @param {*} value - 缓存值
     * @param {number} ttl - 生存时间（毫秒）
     */
    constructor(key, value, ttl) {
        this.key = key;
        this.value = value;
        this.createdAt = Date.now();
        this.expiresAt = Date.now() + ttl;
        this.accessCount = 0;
        this.lastAccessed = Date.now();
    }
    
    /**
     * @function isExpired - 检查是否过期
     * @returns {boolean} 是否过期
     */
    isExpired() {
        return Date.now() > this.expiresAt;
    }
    
    /**
     * @function access - 访问缓存项
     * @returns {*} 缓存值
     */
    access() {
        this.accessCount++;
        this.lastAccessed = Date.now();
        return this.value;
    }
    
    /**
     * @function getAge - 获取缓存项年龄
     * @returns {number} 年龄（毫秒）
     */
    getAge() {
        return Date.now() - this.createdAt;
    }
    
    /**
     * @function getRemainingTTL - 获取剩余生存时间
     * @returns {number} 剩余时间（毫秒）
     */
    getRemainingTTL() {
        return Math.max(0, this.expiresAt - Date.now());
    }
}
// #endregion

// #region 缓存管理器类
/**
 * @class Cache - 缓存管理器
 * @description 单个缓存类型的管理器
 */
class Cache {
    /**
     * @function constructor - 构造函数
     * @param {string} type - 缓存类型
     * @param {Object} config - 缓存配置
     */
    constructor(type, config) {
        this.type = type;
        this.config = config;
        this.items = new Map();
        this.cleanupTimer = null;
        
        this.startCleanup();
    }
    
    /**
     * @function set - 设置缓存
     * @param {string} key - 缓存键
     * @param {*} value - 缓存值
     * @param {number} customTTL - 自定义TTL
     */
    set(key, value, customTTL = null) {
        const ttl = customTTL || this.config.ttl;
        const item = new CacheItem(key, value, ttl);
        
        // 检查缓存大小限制
        if (this.items.size >= this.config.maxSize) {
            this.evictLRU();
        }
        
        this.items.set(key, item);
        console.log(`📦 缓存已设置: ${this.type}/${key}`);
    }
    
    /**
     * @function get - 获取缓存
     * @param {string} key - 缓存键
     * @returns {*} 缓存值或null
     */
    get(key) {
        const item = this.items.get(key);
        
        if (!item) {
            cm_cacheStats.misses++;
            cm_cacheStats.totalRequests++;
            return null;
        }
        
        if (item.isExpired()) {
            this.items.delete(key);
            cm_cacheStats.misses++;
            cm_cacheStats.totalRequests++;
            console.log(`⏰ 缓存已过期: ${this.type}/${key}`);
            return null;
        }
        
        cm_cacheStats.hits++;
        cm_cacheStats.totalRequests++;
        return item.access();
    }
    
    /**
     * @function has - 检查缓存是否存在
     * @param {string} key - 缓存键
     * @returns {boolean} 是否存在
     */
    has(key) {
        const item = this.items.get(key);
        return item && !item.isExpired();
    }
    
    /**
     * @function delete - 删除缓存
     * @param {string} key - 缓存键
     * @returns {boolean} 是否删除成功
     */
    delete(key) {
        const deleted = this.items.delete(key);
        if (deleted) {
            console.log(`🗑️ 缓存已删除: ${this.type}/${key}`);
        }
        return deleted;
    }
    
    /**
     * @function clear - 清空缓存
     */
    clear() {
        const count = this.items.size;
        this.items.clear();
        console.log(`🧹 缓存已清空: ${this.type} (${count}项)`);
    }
    
    /**
     * @function evictLRU - 驱逐最少使用的缓存项
     */
    evictLRU() {
        let lruKey = null;
        let lruTime = Date.now();
        
        for (const [key, item] of this.items) {
            if (item.lastAccessed < lruTime) {
                lruTime = item.lastAccessed;
                lruKey = key;
            }
        }
        
        if (lruKey) {
            this.items.delete(lruKey);
            cm_cacheStats.evictions++;
            console.log(`🔄 LRU驱逐: ${this.type}/${lruKey}`);
        }
    }
    
    /**
     * @function cleanup - 清理过期缓存
     */
    cleanup() {
        let cleanedCount = 0;
        
        for (const [key, item] of this.items) {
            if (item.isExpired()) {
                this.items.delete(key);
                cleanedCount++;
            }
        }
        
        if (cleanedCount > 0) {
            console.log(`🧹 清理过期缓存: ${this.type} (${cleanedCount}项)`);
        }
    }
    
    /**
     * @function startCleanup - 启动定期清理
     */
    startCleanup() {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
        }
        
        this.cleanupTimer = setInterval(() => {
            this.cleanup();
        }, this.config.cleanupInterval);
    }
    
    /**
     * @function stopCleanup - 停止定期清理
     */
    stopCleanup() {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
            this.cleanupTimer = null;
        }
    }
    
    /**
     * @function getStats - 获取缓存统计
     * @returns {Object} 统计信息
     */
    getStats() {
        const items = Array.from(this.items.values());
        
        return {
            type: this.type,
            size: this.items.size,
            maxSize: this.config.maxSize,
            totalAccess: items.reduce((sum, item) => sum + item.accessCount, 0),
            averageAge: items.length > 0 ? 
                items.reduce((sum, item) => sum + item.getAge(), 0) / items.length : 0,
            expiredItems: items.filter(item => item.isExpired()).length
        };
    }
    
    /**
     * @function destroy - 销毁缓存
     */
    destroy() {
        this.stopCleanup();
        this.clear();
    }
}
// #endregion

// #region 缓存管理器主要功能
/**
 * @function cm_initialize - 初始化缓存管理器
 * @description 初始化所有缓存类型
 * @returns {Promise<boolean>} 是否初始化成功
 */
async function cm_initialize() {
    if (cm_isInitialized) return true;

    try {
        console.log('📦 缓存管理器初始化中...');

        // 初始化各种类型的缓存
        for (const [type, config] of Object.entries(CACHE_CONFIG)) {
            const cacheType = CACHE_TYPES[type];
            cm_caches.set(cacheType, new Cache(cacheType, config));
        }

        // 重置统计信息
        cm_cacheStats = {
            hits: 0,
            misses: 0,
            evictions: 0,
            totalRequests: 0
        };

        cm_isInitialized = true;
        console.log('✅ 缓存管理器初始化完成');
        return true;

    } catch (error) {
        console.error('❌ 缓存管理器初始化失败:', error);
        return false;
    }
}

/**
 * @function cm_set - 设置缓存
 * @description 设置指定类型的缓存
 * @param {string} type - 缓存类型
 * @param {string} key - 缓存键
 * @param {*} value - 缓存值
 * @param {number} customTTL - 自定义TTL
 */
function cm_set(type, key, value, customTTL = null) {
    const cache = cm_caches.get(type);
    if (cache) {
        cache.set(key, value, customTTL);
    } else {
        console.warn(`⚠️ 未知的缓存类型: ${type}`);
    }
}

/**
 * @function cm_get - 获取缓存
 * @description 获取指定类型的缓存
 * @param {string} type - 缓存类型
 * @param {string} key - 缓存键
 * @returns {*} 缓存值或null
 */
function cm_get(type, key) {
    const cache = cm_caches.get(type);
    if (cache) {
        return cache.get(key);
    } else {
        console.warn(`⚠️ 未知的缓存类型: ${type}`);
        return null;
    }
}

/**
 * @function cm_has - 检查缓存是否存在
 * @description 检查指定缓存是否存在且未过期
 * @param {string} type - 缓存类型
 * @param {string} key - 缓存键
 * @returns {boolean} 是否存在
 */
function cm_has(type, key) {
    const cache = cm_caches.get(type);
    return cache ? cache.has(key) : false;
}

/**
 * @function cm_delete - 删除缓存
 * @description 删除指定的缓存项
 * @param {string} type - 缓存类型
 * @param {string} key - 缓存键
 * @returns {boolean} 是否删除成功
 */
function cm_delete(type, key) {
    const cache = cm_caches.get(type);
    return cache ? cache.delete(key) : false;
}

/**
 * @function cm_clear - 清空缓存
 * @description 清空指定类型的所有缓存
 * @param {string} type - 缓存类型
 */
function cm_clear(type) {
    const cache = cm_caches.get(type);
    if (cache) {
        cache.clear();
    } else {
        console.warn(`⚠️ 未知的缓存类型: ${type}`);
    }
}

/**
 * @function cm_clearAll - 清空所有缓存
 * @description 清空所有类型的缓存
 */
function cm_clearAll() {
    for (const cache of cm_caches.values()) {
        cache.clear();
    }

    // 重置统计信息
    cm_cacheStats = {
        hits: 0,
        misses: 0,
        evictions: 0,
        totalRequests: 0
    };

    console.log('🧹 所有缓存已清空');
}

/**
 * @function cm_getStats - 获取缓存统计
 * @description 获取所有缓存的统计信息
 * @returns {Object} 统计信息
 */
function cm_getStats() {
    const cacheStats = {};

    for (const [type, cache] of cm_caches) {
        cacheStats[type] = cache.getStats();
    }

    const hitRate = cm_cacheStats.totalRequests > 0 ?
        (cm_cacheStats.hits / cm_cacheStats.totalRequests * 100).toFixed(2) : 0;

    return {
        global: {
            ...cm_cacheStats,
            hitRate: hitRate + '%'
        },
        caches: cacheStats,
        isInitialized: cm_isInitialized
    };
}

/**
 * @function cm_generateKey - 生成缓存键
 * @description 根据参数生成标准化的缓存键
 * @param {string} prefix - 键前缀
 * @param {Object} params - 参数对象
 * @returns {string} 缓存键
 */
function cm_generateKey(prefix, params = {}) {
    const sortedParams = Object.keys(params)
        .sort()
        .map(key => `${key}=${JSON.stringify(params[key])}`)
        .join('&');

    return sortedParams ? `${prefix}?${sortedParams}` : prefix;
}

/**
 * @function cm_wrapFunction - 包装函数以支持缓存
 * @description 为函数添加缓存支持
 * @param {Function} fn - 原始函数
 * @param {string} cacheType - 缓存类型
 * @param {string} keyPrefix - 键前缀
 * @param {number} ttl - 自定义TTL
 * @returns {Function} 包装后的函数
 */
function cm_wrapFunction(fn, cacheType, keyPrefix, ttl = null) {
    return async function(...args) {
        // 生成缓存键
        const key = cm_generateKey(keyPrefix, { args: args });

        // 尝试从缓存获取
        const cached = cm_get(cacheType, key);
        if (cached !== null) {
            console.log(`🎯 缓存命中: ${cacheType}/${key}`);
            return cached;
        }

        // 执行原始函数
        try {
            const result = await fn.apply(this, args);

            // 缓存结果
            cm_set(cacheType, key, result, ttl);

            return result;
        } catch (error) {
            console.error(`❌ 函数执行失败: ${keyPrefix}`, error);
            throw error;
        }
    };
}

/**
 * @function cm_destroy - 销毁缓存管理器
 * @description 清理所有缓存和资源
 */
function cm_destroy() {
    for (const cache of cm_caches.values()) {
        cache.destroy();
    }

    cm_caches.clear();
    cm_isInitialized = false;

    console.log('💥 缓存管理器已销毁');
}

/**
 * @function getCacheManager - 获取缓存管理器实例
 * @description 获取全局缓存管理器实例
 * @returns {Object} 缓存管理器API对象
 */
function getCacheManager() {
    return {
        // 初始化
        initialize: cm_initialize,

        // 基本操作
        set: cm_set,
        get: cm_get,
        has: cm_has,
        delete: cm_delete,
        clear: cm_clear,
        clearAll: cm_clearAll,

        // 工具函数
        generateKey: cm_generateKey,
        wrapFunction: cm_wrapFunction,
        getStats: cm_getStats,

        // 销毁
        destroy: cm_destroy,

        // 常量
        CACHE_TYPES,
        CACHE_CONFIG
    };
}
// #endregion

console.log('📦 AI Side Panel 缓存管理器已加载');
