# AI Side Panel - Git忽略文件配置

# API密钥配置文件（包含敏感信息）
src/config/api-keys.js

# 备份的API密钥文件
src/config/api-keys.*.js
src/config/*-keys.js

# 开发环境文件
.env
.env.local
.env.development
.env.production

# 编辑器和IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 依赖目录
node_modules/
bower_components/

# 构建输出
dist/
build/
out/

# 临时文件
tmp/
temp/
*.tmp
*.temp

# 压缩文件
*.zip
*.rar
*.7z
*.tar.gz

# 测试覆盖率报告
coverage/
.nyc_output/

# 包管理器锁定文件（可选）
# package-lock.json
# yarn.lock

# 扩展打包文件
*.crx
*.pem

# 用户数据和配置
user-data/
user-config/
*.user.js

# 备份文件
*.bak
*.backup
*.old

# 文档生成
docs/generated/
api-docs/

# 安全相关文件
secrets/
private/
*.key
*.pem
*.p12
*.pfx

# 注意事项说明
# 
# 重要提醒：
# 1. src/config/api-keys.js 文件包含敏感的API密钥信息
# 2. 请勿将此文件提交到公共代码仓库
# 3. 在部署前请确保API密钥已正确配置
# 4. 建议定期轮换API密钥以确保安全性
# 5. 如需分享代码，请提供api-keys.js.template模板文件
