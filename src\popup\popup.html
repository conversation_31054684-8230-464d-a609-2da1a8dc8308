<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Side Panel - 快速设置</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="aisp-popup-container">
        <!-- 头部 -->
        <header class="aisp-popup-header">
            <div class="aisp-logo">
                <img src="../../icons/icon48.png" alt="AI Side Panel" class="aisp-logo-img">
                <h1 class="aisp-title" data-i18n="extensionName">AI Side Panel</h1>
            </div>
            <div class="aisp-version">v1.0.0</div>
        </header>

        <!-- 主要功能区 -->
        <main class="aisp-popup-main">
            <!-- 快速操作 -->
            <section class="aisp-quick-actions">
                <button id="aisp-open-sidepanel" class="aisp-btn aisp-btn-primary">
                    <span class="aisp-btn-icon">📋</span>
                    <span data-i18n="popup.openSidePanel">打开侧边栏</span>
                </button>

                <button id="aisp-view-analysis" class="aisp-btn aisp-btn-secondary">
                    <span class="aisp-btn-icon">📊</span>
                    <span data-i18n="popup.viewAnalysis">查看分析结果</span>
                </button>
            </section>

            <!-- 状态信息 -->
            <section class="aisp-status">
                <div class="aisp-status-item">
                    <span class="aisp-status-label" data-i18n="popup.aiStatus">AI分析:</span>
                    <span id="aisp-ai-status" class="aisp-status-value" data-i18n="popup.notConfigured">未配置</span>
                </div>

                <div class="aisp-status-item">
                    <span class="aisp-status-label" data-i18n="popup.templateStatus">模板系统:</span>
                    <span id="aisp-template-status" class="aisp-status-value" data-i18n="popup.enabled">已启用</span>
                </div>

                <div class="aisp-status-item">
                    <span class="aisp-status-label" data-i18n="popup.syncStatus">云端同步:</span>
                    <span id="aisp-sync-status" class="aisp-status-value" data-i18n="popup.disconnected">未连接</span>
                </div>
            </section>

            <!-- 快速设置 -->
            <section class="aisp-quick-settings">
                <div class="aisp-setting-item">
                    <label class="aisp-setting-label">
                        <input type="checkbox" id="aisp-auto-analysis" class="aisp-checkbox">
                        <span class="aisp-checkmark"></span>
                        <span data-i18n="popup.autoAnalysis">自动分析页面内容</span>
                    </label>
                </div>

                <div class="aisp-setting-item">
                    <label class="aisp-setting-label">
                        <input type="checkbox" id="aisp-template-enabled" class="aisp-checkbox" checked>
                        <span class="aisp-checkmark"></span>
                        <span data-i18n="popup.enableTemplate">启用快捷模板</span>
                    </label>
                </div>

                <div class="aisp-setting-item">
                    <label class="aisp-setting-label" data-i18n="popup.languageSettings">语言设置:</label>
                    <select id="aisp-language-select" class="aisp-select">
                        <option value="zh_CN">中文</option>
                        <option value="en_US">English</option>
                        <option value="ja_JP">日本語</option>
                        <option value="ko_KR">한국어</option>
                    </select>
                </div>
            </section>
        </main>

        <!-- 底部链接 -->
        <footer class="aisp-popup-footer">
            <div class="aisp-footer-links">
                <a href="#" id="aisp-open-settings" class="aisp-link" data-i18n="popup.detailedSettings">详细设置</a>
                <a href="#" id="aisp-open-help" class="aisp-link" data-i18n="popup.help">使用帮助</a>
            </div>
        </footer>
    </div>

    <!-- API配置 -->
    <script src="../config/api-keys.js"></script>

    <!-- 工具脚本 -->
    <script src="../utils/language-manager.js"></script>
    <script src="../utils/config-manager.js"></script>
    <script src="../utils/gemini-api.js"></script>
    <script src="../utils/api-manager.js"></script>

    <!-- 主要脚本 -->
    <script src="popup.js"></script>
</body>
</html>
