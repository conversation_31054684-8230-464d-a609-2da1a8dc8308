/**
 * @file AI Side Panel Google Drive API集成
 * @description 实现Google Drive知识库功能，包括OAuth2认证、文件管理、知识库同步等
 */

// #region 全局变量
let gd_isInitialized = false;
let gd_isAuthenticated = false;
let gd_accessToken = null;
let gd_refreshToken = null;
let gd_tokenExpiry = null;
let gd_knowledgeBaseFolder = null;
let gd_currentLanguage = 'zh_CN';

// Google Drive API配置
const GD_CONFIG = {
    CLIENT_ID: 'your-google-client-id.apps.googleusercontent.com',
    CLIENT_SECRET: 'your-google-client-secret',
    REDIRECT_URI: chrome.identity.getRedirectURL(),
    SCOPES: [
        'https://www.googleapis.com/auth/drive.file',
        'https://www.googleapis.com/auth/drive.metadata.readonly'
    ],
    API_BASE_URL: 'https://www.googleapis.com/drive/v3',
    UPLOAD_URL: 'https://www.googleapis.com/upload/drive/v3/files',
    KNOWLEDGE_BASE_FOLDER_NAME: 'AI Side Panel Knowledge Base'
};

// 知识库文件类型
const KNOWLEDGE_FILE_TYPES = {
    TEMPLATE: 'template',
    KNOWLEDGE: 'knowledge',
    FAQ: 'faq',
    SCRIPT: 'script',
    NOTE: 'note'
};

// 文件MIME类型映射
const MIME_TYPES = {
    JSON: 'application/json',
    TEXT: 'text/plain',
    MARKDOWN: 'text/markdown',
    FOLDER: 'application/vnd.google-apps.folder'
};

// 存储键
const STORAGE_KEYS = {
    ACCESS_TOKEN: 'gd_access_token',
    REFRESH_TOKEN: 'gd_refresh_token',
    TOKEN_EXPIRY: 'gd_token_expiry',
    FOLDER_ID: 'gd_knowledge_folder_id',
    SYNC_STATUS: 'gd_sync_status',
    LAST_SYNC: 'gd_last_sync'
};
// #endregion

// #region 初始化和认证
/**
 * @function gd_initialize - 初始化Google Drive API
 * @description 初始化Google Drive API集成
 * @returns {Promise<boolean>} 是否初始化成功
 */
async function gd_initialize() {
    if (gd_isInitialized) return true;
    
    try {
        console.log('☁️ Google Drive API初始化中...');
        
        // 获取当前语言
        if (typeof lang_getCurrentLanguage === 'function') {
            gd_currentLanguage = lang_getCurrentLanguage();
        }
        
        // 加载存储的认证信息
        await gd_loadStoredAuth();
        
        // 检查认证状态
        if (gd_accessToken) {
            await gd_validateToken();
        }
        
        // 添加语言变更监听器
        if (typeof lang_addChangeListener === 'function') {
            lang_addChangeListener((newLang, oldLang) => {
                gd_currentLanguage = newLang;
                console.log(`☁️ Google Drive语言已切换: ${oldLang} -> ${newLang}`);
            });
        }
        
        gd_isInitialized = true;
        console.log('✅ Google Drive API初始化完成');
        return true;
        
    } catch (error) {
        console.error('❌ Google Drive API初始化失败:', error);
        return false;
    }
}

/**
 * @function gd_authenticate - 进行OAuth2认证
 * @description 使用Chrome Identity API进行Google OAuth2认证
 * @returns {Promise<boolean>} 是否认证成功
 */
async function gd_authenticate() {
    try {
        console.log('🔐 开始Google Drive认证...');
        
        // 构建认证URL
        const authUrl = gd_buildAuthUrl();
        
        // 使用Chrome Identity API进行认证
        const redirectUrl = await new Promise((resolve, reject) => {
            chrome.identity.launchWebAuthFlow({
                url: authUrl,
                interactive: true
            }, (redirectUrl) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(redirectUrl);
                }
            });
        });
        
        // 从重定向URL中提取授权码
        const authCode = gd_extractAuthCode(redirectUrl);
        if (!authCode) {
            throw new Error('未能获取授权码');
        }
        
        // 交换访问令牌
        const tokenData = await gd_exchangeCodeForToken(authCode);
        
        // 保存令牌信息
        await gd_saveTokenData(tokenData);
        
        // 初始化知识库文件夹
        await gd_initializeKnowledgeBaseFolder();
        
        gd_isAuthenticated = true;
        console.log('✅ Google Drive认证成功');
        return true;
        
    } catch (error) {
        console.error('❌ Google Drive认证失败:', error);
        gd_isAuthenticated = false;
        return false;
    }
}

/**
 * @function gd_buildAuthUrl - 构建认证URL
 * @description 构建Google OAuth2认证URL
 * @returns {string} 认证URL
 */
function gd_buildAuthUrl() {
    const params = new URLSearchParams({
        client_id: GD_CONFIG.CLIENT_ID,
        redirect_uri: GD_CONFIG.REDIRECT_URI,
        scope: GD_CONFIG.SCOPES.join(' '),
        response_type: 'code',
        access_type: 'offline',
        prompt: 'consent'
    });
    
    return `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`;
}

/**
 * @function gd_extractAuthCode - 从重定向URL提取授权码
 * @description 从OAuth2重定向URL中提取授权码
 * @param {string} redirectUrl - 重定向URL
 * @returns {string|null} 授权码
 */
function gd_extractAuthCode(redirectUrl) {
    try {
        const url = new URL(redirectUrl);
        return url.searchParams.get('code');
    } catch (error) {
        console.error('提取授权码失败:', error);
        return null;
    }
}

/**
 * @function gd_exchangeCodeForToken - 交换访问令牌
 * @description 使用授权码交换访问令牌
 * @param {string} authCode - 授权码
 * @returns {Promise<Object>} 令牌数据
 */
async function gd_exchangeCodeForToken(authCode) {
    const tokenUrl = 'https://oauth2.googleapis.com/token';
    
    const requestBody = new URLSearchParams({
        client_id: GD_CONFIG.CLIENT_ID,
        client_secret: GD_CONFIG.CLIENT_SECRET,
        code: authCode,
        grant_type: 'authorization_code',
        redirect_uri: GD_CONFIG.REDIRECT_URI
    });
    
    const response = await fetch(tokenUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: requestBody
    });
    
    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`令牌交换失败: ${errorData.error_description || errorData.error}`);
    }
    
    return await response.json();
}

/**
 * @function gd_saveTokenData - 保存令牌数据
 * @description 保存访问令牌和刷新令牌到Chrome存储
 * @param {Object} tokenData - 令牌数据
 */
async function gd_saveTokenData(tokenData) {
    gd_accessToken = tokenData.access_token;
    gd_refreshToken = tokenData.refresh_token;
    gd_tokenExpiry = Date.now() + (tokenData.expires_in * 1000);
    
    await chrome.storage.local.set({
        [STORAGE_KEYS.ACCESS_TOKEN]: gd_accessToken,
        [STORAGE_KEYS.REFRESH_TOKEN]: gd_refreshToken,
        [STORAGE_KEYS.TOKEN_EXPIRY]: gd_tokenExpiry
    });
    
    console.log('💾 令牌数据已保存');
}

/**
 * @function gd_loadStoredAuth - 加载存储的认证信息
 * @description 从Chrome存储加载认证信息
 */
async function gd_loadStoredAuth() {
    try {
        const result = await chrome.storage.local.get([
            STORAGE_KEYS.ACCESS_TOKEN,
            STORAGE_KEYS.REFRESH_TOKEN,
            STORAGE_KEYS.TOKEN_EXPIRY,
            STORAGE_KEYS.FOLDER_ID
        ]);
        
        gd_accessToken = result[STORAGE_KEYS.ACCESS_TOKEN] || null;
        gd_refreshToken = result[STORAGE_KEYS.REFRESH_TOKEN] || null;
        gd_tokenExpiry = result[STORAGE_KEYS.TOKEN_EXPIRY] || null;
        gd_knowledgeBaseFolder = result[STORAGE_KEYS.FOLDER_ID] || null;
        
        if (gd_accessToken && gd_tokenExpiry && Date.now() < gd_tokenExpiry) {
            gd_isAuthenticated = true;
            console.log('📋 已加载存储的认证信息');
        }
        
    } catch (error) {
        console.error('加载认证信息失败:', error);
    }
}

/**
 * @function gd_validateToken - 验证访问令牌
 * @description 验证当前访问令牌是否有效
 * @returns {Promise<boolean>} 令牌是否有效
 */
async function gd_validateToken() {
    if (!gd_accessToken) return false;
    
    try {
        // 检查令牌是否过期
        if (gd_tokenExpiry && Date.now() >= gd_tokenExpiry) {
            console.log('🔄 访问令牌已过期，尝试刷新...');
            return await gd_refreshAccessToken();
        }
        
        // 测试API调用
        const response = await fetch(`${GD_CONFIG.API_BASE_URL}/about?fields=user`, {
            headers: {
                'Authorization': `Bearer ${gd_accessToken}`
            }
        });
        
        if (response.ok) {
            gd_isAuthenticated = true;
            return true;
        } else {
            console.log('🔄 访问令牌无效，尝试刷新...');
            return await gd_refreshAccessToken();
        }
        
    } catch (error) {
        console.error('验证令牌失败:', error);
        return false;
    }
}

/**
 * @function gd_refreshAccessToken - 刷新访问令牌
 * @description 使用刷新令牌获取新的访问令牌
 * @returns {Promise<boolean>} 是否刷新成功
 */
async function gd_refreshAccessToken() {
    if (!gd_refreshToken) {
        console.log('❌ 没有刷新令牌，需要重新认证');
        gd_isAuthenticated = false;
        return false;
    }
    
    try {
        const tokenUrl = 'https://oauth2.googleapis.com/token';
        
        const requestBody = new URLSearchParams({
            client_id: GD_CONFIG.CLIENT_ID,
            client_secret: GD_CONFIG.CLIENT_SECRET,
            refresh_token: gd_refreshToken,
            grant_type: 'refresh_token'
        });
        
        const response = await fetch(tokenUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: requestBody
        });
        
        if (!response.ok) {
            throw new Error('刷新令牌失败');
        }
        
        const tokenData = await response.json();
        
        // 更新访问令牌
        gd_accessToken = tokenData.access_token;
        gd_tokenExpiry = Date.now() + (tokenData.expires_in * 1000);
        
        // 如果有新的刷新令牌，也要更新
        if (tokenData.refresh_token) {
            gd_refreshToken = tokenData.refresh_token;
        }
        
        // 保存更新的令牌
        await chrome.storage.local.set({
            [STORAGE_KEYS.ACCESS_TOKEN]: gd_accessToken,
            [STORAGE_KEYS.REFRESH_TOKEN]: gd_refreshToken,
            [STORAGE_KEYS.TOKEN_EXPIRY]: gd_tokenExpiry
        });
        
        gd_isAuthenticated = true;
        console.log('✅ 访问令牌刷新成功');
        return true;
        
    } catch (error) {
        console.error('❌ 刷新访问令牌失败:', error);
        gd_isAuthenticated = false;
        return false;
    }
}
// #endregion

// #region 文件夹管理
/**
 * @function gd_initializeKnowledgeBaseFolder - 初始化知识库文件夹
 * @description 创建或获取知识库根文件夹
 * @returns {Promise<string>} 文件夹ID
 */
async function gd_initializeKnowledgeBaseFolder() {
    try {
        // 检查是否已有存储的文件夹ID
        if (gd_knowledgeBaseFolder) {
            const folderExists = await gd_checkFolderExists(gd_knowledgeBaseFolder);
            if (folderExists) {
                return gd_knowledgeBaseFolder;
            }
        }

        // 搜索现有的知识库文件夹
        const existingFolder = await gd_findFolderByName(GD_CONFIG.KNOWLEDGE_BASE_FOLDER_NAME);

        if (existingFolder) {
            gd_knowledgeBaseFolder = existingFolder.id;
        } else {
            // 创建新的知识库文件夹
            gd_knowledgeBaseFolder = await gd_createFolder(GD_CONFIG.KNOWLEDGE_BASE_FOLDER_NAME);
        }

        // 保存文件夹ID
        await chrome.storage.local.set({
            [STORAGE_KEYS.FOLDER_ID]: gd_knowledgeBaseFolder
        });

        // 创建子文件夹结构
        await gd_createSubFolders();

        console.log('📁 知识库文件夹初始化完成:', gd_knowledgeBaseFolder);
        return gd_knowledgeBaseFolder;

    } catch (error) {
        console.error('初始化知识库文件夹失败:', error);
        throw error;
    }
}

/**
 * @function gd_createSubFolders - 创建子文件夹结构
 * @description 在知识库根文件夹下创建分类子文件夹
 */
async function gd_createSubFolders() {
    const subFolders = [
        'Templates',      // 模板文件
        'Knowledge',      // 知识文档
        'FAQ',           // 常见问题
        'Scripts',       // 脚本文件
        'Notes',         // 笔记文件
        'Backups'        // 备份文件
    ];

    for (const folderName of subFolders) {
        try {
            const existingFolder = await gd_findFolderByName(folderName, gd_knowledgeBaseFolder);
            if (!existingFolder) {
                await gd_createFolder(folderName, gd_knowledgeBaseFolder);
                console.log(`📁 创建子文件夹: ${folderName}`);
            }
        } catch (error) {
            console.warn(`创建子文件夹失败: ${folderName}`, error);
        }
    }
}

/**
 * @function gd_createFolder - 创建文件夹
 * @description 在Google Drive中创建文件夹
 * @param {string} name - 文件夹名称
 * @param {string} parentId - 父文件夹ID
 * @returns {Promise<string>} 创建的文件夹ID
 */
async function gd_createFolder(name, parentId = null) {
    const metadata = {
        name: name,
        mimeType: MIME_TYPES.FOLDER
    };

    if (parentId) {
        metadata.parents = [parentId];
    }

    const response = await gd_makeApiRequest(`${GD_CONFIG.API_BASE_URL}/files`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(metadata)
    });

    return response.id;
}

/**
 * @function gd_findFolderByName - 根据名称查找文件夹
 * @description 在指定父文件夹中查找指定名称的文件夹
 * @param {string} name - 文件夹名称
 * @param {string} parentId - 父文件夹ID
 * @returns {Promise<Object|null>} 文件夹对象或null
 */
async function gd_findFolderByName(name, parentId = null) {
    let query = `name='${name}' and mimeType='${MIME_TYPES.FOLDER}' and trashed=false`;

    if (parentId) {
        query += ` and '${parentId}' in parents`;
    }

    const response = await gd_makeApiRequest(
        `${GD_CONFIG.API_BASE_URL}/files?q=${encodeURIComponent(query)}&fields=files(id,name)`
    );

    return response.files && response.files.length > 0 ? response.files[0] : null;
}

/**
 * @function gd_checkFolderExists - 检查文件夹是否存在
 * @description 检查指定ID的文件夹是否存在
 * @param {string} folderId - 文件夹ID
 * @returns {Promise<boolean>} 文件夹是否存在
 */
async function gd_checkFolderExists(folderId) {
    try {
        const response = await gd_makeApiRequest(
            `${GD_CONFIG.API_BASE_URL}/files/${folderId}?fields=id,name,trashed`
        );

        return response && !response.trashed;
    } catch (error) {
        return false;
    }
}
// #endregion

// #region 文件操作
/**
 * @function gd_uploadFile - 上传文件
 * @description 上传文件到Google Drive
 * @param {string} fileName - 文件名
 * @param {string} content - 文件内容
 * @param {string} mimeType - MIME类型
 * @param {string} folderId - 目标文件夹ID
 * @param {Object} metadata - 额外的元数据
 * @returns {Promise<Object>} 上传的文件信息
 */
async function gd_uploadFile(fileName, content, mimeType = MIME_TYPES.JSON, folderId = null, metadata = {}) {
    try {
        // 准备文件元数据
        const fileMetadata = {
            name: fileName,
            ...metadata
        };

        if (folderId) {
            fileMetadata.parents = [folderId];
        }

        // 创建multipart请求体
        const boundary = '-------314159265358979323846';
        const delimiter = "\r\n--" + boundary + "\r\n";
        const close_delim = "\r\n--" + boundary + "--";

        let body = delimiter +
            'Content-Type: application/json\r\n\r\n' +
            JSON.stringify(fileMetadata) + delimiter +
            'Content-Type: ' + mimeType + '\r\n\r\n' +
            content + close_delim;

        const response = await gd_makeApiRequest(GD_CONFIG.UPLOAD_URL + '?uploadType=multipart', {
            method: 'POST',
            headers: {
                'Content-Type': 'multipart/related; boundary="' + boundary + '"'
            },
            body: body
        });

        console.log('📤 文件上传成功:', fileName);
        return response;

    } catch (error) {
        console.error('文件上传失败:', error);
        throw error;
    }
}

/**
 * @function gd_downloadFile - 下载文件
 * @description 从Google Drive下载文件内容
 * @param {string} fileId - 文件ID
 * @returns {Promise<string>} 文件内容
 */
async function gd_downloadFile(fileId) {
    try {
        const response = await gd_makeApiRequest(
            `${GD_CONFIG.API_BASE_URL}/files/${fileId}?alt=media`,
            {
                method: 'GET'
            },
            false // 不解析为JSON
        );

        return await response.text();

    } catch (error) {
        console.error('文件下载失败:', error);
        throw error;
    }
}

/**
 * @function gd_updateFile - 更新文件
 * @description 更新Google Drive中的文件内容
 * @param {string} fileId - 文件ID
 * @param {string} content - 新的文件内容
 * @param {string} mimeType - MIME类型
 * @returns {Promise<Object>} 更新的文件信息
 */
async function gd_updateFile(fileId, content, mimeType = MIME_TYPES.JSON) {
    try {
        const response = await gd_makeApiRequest(
            `${GD_CONFIG.UPLOAD_URL}/${fileId}?uploadType=media`,
            {
                method: 'PATCH',
                headers: {
                    'Content-Type': mimeType
                },
                body: content
            }
        );

        console.log('📝 文件更新成功:', fileId);
        return response;

    } catch (error) {
        console.error('文件更新失败:', error);
        throw error;
    }
}

/**
 * @function gd_deleteFile - 删除文件
 * @description 删除Google Drive中的文件
 * @param {string} fileId - 文件ID
 * @returns {Promise<boolean>} 是否删除成功
 */
async function gd_deleteFile(fileId) {
    try {
        await gd_makeApiRequest(`${GD_CONFIG.API_BASE_URL}/files/${fileId}`, {
            method: 'DELETE'
        });

        console.log('🗑️ 文件删除成功:', fileId);
        return true;

    } catch (error) {
        console.error('文件删除失败:', error);
        return false;
    }
}

/**
 * @function gd_listFiles - 列出文件
 * @description 列出指定文件夹中的文件
 * @param {string} folderId - 文件夹ID
 * @param {Object} options - 查询选项
 * @returns {Promise<Array>} 文件列表
 */
async function gd_listFiles(folderId = null, options = {}) {
    try {
        const {
            mimeType = null,
            nameContains = null,
            orderBy = 'modifiedTime desc',
            pageSize = 100
        } = options;

        let query = 'trashed=false';

        if (folderId) {
            query += ` and '${folderId}' in parents`;
        }

        if (mimeType) {
            query += ` and mimeType='${mimeType}'`;
        }

        if (nameContains) {
            query += ` and name contains '${nameContains}'`;
        }

        const params = new URLSearchParams({
            q: query,
            orderBy: orderBy,
            pageSize: pageSize.toString(),
            fields: 'files(id,name,mimeType,size,modifiedTime,createdTime,parents)'
        });

        const response = await gd_makeApiRequest(
            `${GD_CONFIG.API_BASE_URL}/files?${params.toString()}`
        );

        return response.files || [];

    } catch (error) {
        console.error('列出文件失败:', error);
        return [];
    }
}
// #endregion

// #region 知识库同步
/**
 * @function gd_syncKnowledgeBase - 同步知识库
 * @description 同步本地知识库数据到Google Drive
 * @param {Object} options - 同步选项
 * @returns {Promise<Object>} 同步结果
 */
async function gd_syncKnowledgeBase(options = {}) {
    try {
        if (!gd_isAuthenticated) {
            throw new Error('未认证，无法同步知识库');
        }

        console.log('🔄 开始同步知识库...');

        const {
            syncTemplates = true,
            syncKnowledge = true,
            syncFAQ = true,
            forceUpload = false
        } = options;

        const syncResults = {
            templates: { uploaded: 0, downloaded: 0, errors: 0 },
            knowledge: { uploaded: 0, downloaded: 0, errors: 0 },
            faq: { uploaded: 0, downloaded: 0, errors: 0 },
            startTime: Date.now(),
            endTime: null,
            success: true,
            errors: []
        };

        // 同步模板
        if (syncTemplates) {
            try {
                const templateResult = await gd_syncTemplates(forceUpload);
                syncResults.templates = templateResult;
            } catch (error) {
                syncResults.errors.push(`模板同步失败: ${error.message}`);
                syncResults.templates.errors++;
            }
        }

        // 同步知识文档
        if (syncKnowledge) {
            try {
                const knowledgeResult = await gd_syncKnowledgeDocuments(forceUpload);
                syncResults.knowledge = knowledgeResult;
            } catch (error) {
                syncResults.errors.push(`知识文档同步失败: ${error.message}`);
                syncResults.knowledge.errors++;
            }
        }

        // 同步FAQ
        if (syncFAQ) {
            try {
                const faqResult = await gd_syncFAQ(forceUpload);
                syncResults.faq = faqResult;
            } catch (error) {
                syncResults.errors.push(`FAQ同步失败: ${error.message}`);
                syncResults.faq.errors++;
            }
        }

        syncResults.endTime = Date.now();
        syncResults.success = syncResults.errors.length === 0;

        // 更新同步状态
        await chrome.storage.local.set({
            [STORAGE_KEYS.LAST_SYNC]: syncResults.endTime,
            [STORAGE_KEYS.SYNC_STATUS]: syncResults
        });

        console.log('✅ 知识库同步完成:', syncResults);
        return syncResults;

    } catch (error) {
        console.error('❌ 知识库同步失败:', error);
        throw error;
    }
}

/**
 * @function gd_syncTemplates - 同步模板数据
 * @description 同步快捷回复模板到Google Drive
 * @param {boolean} forceUpload - 是否强制上传
 * @returns {Promise<Object>} 同步结果
 */
async function gd_syncTemplates(forceUpload = false) {
    const result = { uploaded: 0, downloaded: 0, errors: 0 };

    try {
        // 获取模板文件夹
        const templatesFolder = await gd_findFolderByName('Templates', gd_knowledgeBaseFolder);
        if (!templatesFolder) {
            throw new Error('模板文件夹不存在');
        }

        // 获取本地模板数据
        const templateManager = typeof getTemplateManager === 'function' ? getTemplateManager() : null;
        if (!templateManager) {
            throw new Error('模板管理器不可用');
        }

        const localTemplates = await templateManager.getTemplates();

        // 获取云端模板文件
        const cloudFiles = await gd_listFiles(templatesFolder.id, {
            mimeType: MIME_TYPES.JSON,
            nameContains: 'templates'
        });

        // 上传本地模板
        if (localTemplates.length > 0) {
            const templatesData = {
                version: '1.0',
                exportTime: Date.now(),
                templates: localTemplates,
                categories: templateManager.getCategories ? templateManager.getCategories() : {}
            };

            const fileName = `templates_${gd_currentLanguage}_${Date.now()}.json`;
            const content = JSON.stringify(templatesData, null, 2);

            await gd_uploadFile(fileName, content, MIME_TYPES.JSON, templatesFolder.id, {
                description: `AI Side Panel Templates - ${gd_currentLanguage}`,
                properties: {
                    type: KNOWLEDGE_FILE_TYPES.TEMPLATE,
                    language: gd_currentLanguage,
                    version: '1.0'
                }
            });

            result.uploaded++;
        }

        // 下载云端模板（如果需要）
        if (!forceUpload && cloudFiles.length > 0) {
            // 这里可以实现从云端下载模板的逻辑
            // 暂时跳过，专注于上传功能
        }

        return result;

    } catch (error) {
        console.error('同步模板失败:', error);
        result.errors++;
        return result;
    }
}

/**
 * @function gd_syncKnowledgeDocuments - 同步知识文档
 * @description 同步知识文档到Google Drive
 * @param {boolean} forceUpload - 是否强制上传
 * @returns {Promise<Object>} 同步结果
 */
async function gd_syncKnowledgeDocuments(forceUpload = false) {
    const result = { uploaded: 0, downloaded: 0, errors: 0 };

    try {
        // 获取知识文档文件夹
        const knowledgeFolder = await gd_findFolderByName('Knowledge', gd_knowledgeBaseFolder);
        if (!knowledgeFolder) {
            throw new Error('知识文档文件夹不存在');
        }

        // 这里可以实现知识文档的同步逻辑
        // 暂时返回空结果
        console.log('📚 知识文档同步功能待实现');

        return result;

    } catch (error) {
        console.error('同步知识文档失败:', error);
        result.errors++;
        return result;
    }
}

/**
 * @function gd_syncFAQ - 同步FAQ数据
 * @description 同步常见问题数据到Google Drive
 * @param {boolean} forceUpload - 是否强制上传
 * @returns {Promise<Object>} 同步结果
 */
async function gd_syncFAQ(forceUpload = false) {
    const result = { uploaded: 0, downloaded: 0, errors: 0 };

    try {
        // 获取FAQ文件夹
        const faqFolder = await gd_findFolderByName('FAQ', gd_knowledgeBaseFolder);
        if (!faqFolder) {
            throw new Error('FAQ文件夹不存在');
        }

        // 这里可以实现FAQ的同步逻辑
        // 暂时返回空结果
        console.log('❓ FAQ同步功能待实现');

        return result;

    } catch (error) {
        console.error('同步FAQ失败:', error);
        result.errors++;
        return result;
    }
}
// #endregion

// #region 标签系统
/**
 * @function gd_addFileTag - 为文件添加标签
 * @description 为Google Drive文件添加自定义标签
 * @param {string} fileId - 文件ID
 * @param {string} tagKey - 标签键
 * @param {string} tagValue - 标签值
 * @returns {Promise<boolean>} 是否添加成功
 */
async function gd_addFileTag(fileId, tagKey, tagValue) {
    try {
        const metadata = {
            properties: {
                [tagKey]: tagValue
            }
        };

        await gd_makeApiRequest(`${GD_CONFIG.API_BASE_URL}/files/${fileId}`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(metadata)
        });

        console.log(`🏷️ 标签已添加: ${tagKey}=${tagValue}`);
        return true;

    } catch (error) {
        console.error('添加文件标签失败:', error);
        return false;
    }
}

/**
 * @function gd_removeFileTag - 移除文件标签
 * @description 移除Google Drive文件的自定义标签
 * @param {string} fileId - 文件ID
 * @param {string} tagKey - 标签键
 * @returns {Promise<boolean>} 是否移除成功
 */
async function gd_removeFileTag(fileId, tagKey) {
    try {
        const metadata = {
            properties: {
                [tagKey]: null
            }
        };

        await gd_makeApiRequest(`${GD_CONFIG.API_BASE_URL}/files/${fileId}`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(metadata)
        });

        console.log(`🏷️ 标签已移除: ${tagKey}`);
        return true;

    } catch (error) {
        console.error('移除文件标签失败:', error);
        return false;
    }
}

/**
 * @function gd_getFilesByTag - 根据标签查找文件
 * @description 根据标签查找Google Drive文件
 * @param {string} tagKey - 标签键
 * @param {string} tagValue - 标签值
 * @param {string} folderId - 搜索范围文件夹ID
 * @returns {Promise<Array>} 匹配的文件列表
 */
async function gd_getFilesByTag(tagKey, tagValue, folderId = null) {
    try {
        let query = `trashed=false and properties has {key='${tagKey}' and value='${tagValue}'}`;

        if (folderId) {
            query += ` and '${folderId}' in parents`;
        }

        const params = new URLSearchParams({
            q: query,
            fields: 'files(id,name,mimeType,properties,modifiedTime)'
        });

        const response = await gd_makeApiRequest(
            `${GD_CONFIG.API_BASE_URL}/files?${params.toString()}`
        );

        return response.files || [];

    } catch (error) {
        console.error('根据标签查找文件失败:', error);
        return [];
    }
}
// #endregion

// #region 工具函数
/**
 * @function gd_makeApiRequest - 发起API请求
 * @description 发起带认证的Google Drive API请求
 * @param {string} url - 请求URL
 * @param {Object} options - 请求选项
 * @param {boolean} parseJson - 是否解析为JSON
 * @returns {Promise<Object|Response>} 响应数据
 */
async function gd_makeApiRequest(url, options = {}, parseJson = true) {
    if (!gd_isAuthenticated || !gd_accessToken) {
        throw new Error('未认证，无法发起API请求');
    }

    // 检查令牌是否需要刷新
    if (gd_tokenExpiry && Date.now() >= gd_tokenExpiry - 60000) { // 提前1分钟刷新
        await gd_refreshAccessToken();
    }

    const requestOptions = {
        ...options,
        headers: {
            'Authorization': `Bearer ${gd_accessToken}`,
            ...options.headers
        }
    };

    const response = await fetch(url, requestOptions);

    if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = `API请求失败: ${response.status} ${response.statusText}`;

        try {
            const errorData = JSON.parse(errorText);
            if (errorData.error && errorData.error.message) {
                errorMessage = errorData.error.message;
            }
        } catch (e) {
            // 忽略JSON解析错误
        }

        throw new Error(errorMessage);
    }

    return parseJson ? await response.json() : response;
}

/**
 * @function gd_logout - 注销
 * @description 清除认证信息并注销
 */
async function gd_logout() {
    try {
        // 清除内存中的认证信息
        gd_accessToken = null;
        gd_refreshToken = null;
        gd_tokenExpiry = null;
        gd_knowledgeBaseFolder = null;
        gd_isAuthenticated = false;

        // 清除存储的认证信息
        await chrome.storage.local.remove([
            STORAGE_KEYS.ACCESS_TOKEN,
            STORAGE_KEYS.REFRESH_TOKEN,
            STORAGE_KEYS.TOKEN_EXPIRY,
            STORAGE_KEYS.FOLDER_ID,
            STORAGE_KEYS.SYNC_STATUS,
            STORAGE_KEYS.LAST_SYNC
        ]);

        console.log('👋 已注销Google Drive');

    } catch (error) {
        console.error('注销失败:', error);
    }
}

/**
 * @function gd_getAuthStatus - 获取认证状态
 * @description 获取当前的认证状态信息
 * @returns {Object} 认证状态信息
 */
function gd_getAuthStatus() {
    return {
        isInitialized: gd_isInitialized,
        isAuthenticated: gd_isAuthenticated,
        hasAccessToken: !!gd_accessToken,
        hasRefreshToken: !!gd_refreshToken,
        tokenExpiry: gd_tokenExpiry,
        knowledgeBaseFolder: gd_knowledgeBaseFolder,
        timeUntilExpiry: gd_tokenExpiry ? Math.max(0, gd_tokenExpiry - Date.now()) : 0
    };
}

/**
 * @function gd_getSyncStatus - 获取同步状态
 * @description 获取知识库同步状态信息
 * @returns {Promise<Object>} 同步状态信息
 */
async function gd_getSyncStatus() {
    try {
        const result = await chrome.storage.local.get([
            STORAGE_KEYS.LAST_SYNC,
            STORAGE_KEYS.SYNC_STATUS
        ]);

        return {
            lastSync: result[STORAGE_KEYS.LAST_SYNC] || null,
            lastSyncStatus: result[STORAGE_KEYS.SYNC_STATUS] || null,
            timeSinceLastSync: result[STORAGE_KEYS.LAST_SYNC] ?
                Date.now() - result[STORAGE_KEYS.LAST_SYNC] : null
        };

    } catch (error) {
        console.error('获取同步状态失败:', error);
        return {
            lastSync: null,
            lastSyncStatus: null,
            timeSinceLastSync: null
        };
    }
}

/**
 * @function gd_getStorageQuota - 获取存储配额信息
 * @description 获取Google Drive存储配额信息
 * @returns {Promise<Object>} 存储配额信息
 */
async function gd_getStorageQuota() {
    try {
        const response = await gd_makeApiRequest(
            `${GD_CONFIG.API_BASE_URL}/about?fields=storageQuota`
        );

        const quota = response.storageQuota;

        return {
            limit: parseInt(quota.limit) || 0,
            usage: parseInt(quota.usage) || 0,
            usageInDrive: parseInt(quota.usageInDrive) || 0,
            usageInDriveTrash: parseInt(quota.usageInDriveTrash) || 0,
            available: quota.limit ? parseInt(quota.limit) - parseInt(quota.usage) : 0,
            usagePercentage: quota.limit ?
                Math.round((parseInt(quota.usage) / parseInt(quota.limit)) * 100) : 0
        };

    } catch (error) {
        console.error('获取存储配额失败:', error);
        return {
            limit: 0,
            usage: 0,
            usageInDrive: 0,
            usageInDriveTrash: 0,
            available: 0,
            usagePercentage: 0
        };
    }
}

/**
 * @function gd_formatFileSize - 格式化文件大小
 * @description 将字节数格式化为可读的文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化的文件大小
 */
function gd_formatFileSize(bytes) {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * @function gd_validateConfig - 验证配置
 * @description 验证Google Drive API配置是否正确
 * @returns {Object} 验证结果
 */
function gd_validateConfig() {
    const errors = [];
    const warnings = [];

    if (!GD_CONFIG.CLIENT_ID || GD_CONFIG.CLIENT_ID === 'your-google-client-id.apps.googleusercontent.com') {
        errors.push('Google Client ID未配置');
    }

    if (!GD_CONFIG.CLIENT_SECRET || GD_CONFIG.CLIENT_SECRET === 'your-google-client-secret') {
        errors.push('Google Client Secret未配置');
    }

    if (!GD_CONFIG.REDIRECT_URI) {
        errors.push('重定向URI未配置');
    }

    if (GD_CONFIG.SCOPES.length === 0) {
        warnings.push('未配置API权限范围');
    }

    return {
        isValid: errors.length === 0,
        errors: errors,
        warnings: warnings
    };
}

/**
 * @function getGoogleDriveAPI - 获取Google Drive API实例
 * @description 获取全局Google Drive API实例
 * @returns {Object} Google Drive API对象
 */
function getGoogleDriveAPI() {
    return {
        // 初始化和认证
        initialize: gd_initialize,
        authenticate: gd_authenticate,
        logout: gd_logout,

        // 状态查询
        getAuthStatus: gd_getAuthStatus,
        getSyncStatus: gd_getSyncStatus,
        getStorageQuota: gd_getStorageQuota,

        // 文件夹管理
        initializeKnowledgeBaseFolder: gd_initializeKnowledgeBaseFolder,
        createFolder: gd_createFolder,
        findFolderByName: gd_findFolderByName,

        // 文件操作
        uploadFile: gd_uploadFile,
        downloadFile: gd_downloadFile,
        updateFile: gd_updateFile,
        deleteFile: gd_deleteFile,
        listFiles: gd_listFiles,

        // 知识库同步
        syncKnowledgeBase: gd_syncKnowledgeBase,
        syncTemplates: gd_syncTemplates,

        // 标签系统
        addFileTag: gd_addFileTag,
        removeFileTag: gd_removeFileTag,
        getFilesByTag: gd_getFilesByTag,

        // 工具函数
        formatFileSize: gd_formatFileSize,
        validateConfig: gd_validateConfig,

        // 常量
        KNOWLEDGE_FILE_TYPES,
        MIME_TYPES
    };
}
// #endregion

console.log('☁️ AI Side Panel Google Drive API已加载');
