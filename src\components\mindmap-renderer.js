/**
 * @file AI Side Panel 思维导图渲染器
 * @description 使用原生JavaScript和SVG渲染思维导图，无需外部依赖
 */

// #region 思维导图渲染器类定义
/**
 * @class ui_MindMapRenderer - 思维导图渲染器类
 * @description 渲染和管理思维导图的显示
 */
class ui_MindMapRenderer {
    /**
     * @constructor
     * @param {string} containerId - 容器元素ID
     * @param {Object} options - 配置选项
     */
    constructor(containerId, options = {}) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.options = {
            width: 500,
            height: 400,
            nodeRadius: 35,
            fontSize: 13,
            lineColor: '#007AFF',
            nodeColor: '#F2F2F7',
            textColor: '#1D1D1F',
            rootColor: '#007AFF',
            branchColors: ['#34C759', '#FF9500', '#FF3B30', '#AF52DE', '#5AC8FA'],
            ...options
        };

        this.nodes = [];
        this.links = [];
        this.svg = null;
        this.isDragging = false;
        this.dragNode = null;
        this.scale = 1;
        this.translateX = 0;
        this.translateY = 0;
        this.expandedNodes = new Set();

        if (!this.container) {
            console.error(`思维导图容器未找到: ${containerId}`);
            return;
        }

        this.ui_initializeMindMap();
        this.ui_setupInteractions();
    }
    
    /**
     * @function ui_initializeMindMap - 初始化思维导图
     * @description 创建SVG容器和基本结构
     */
    ui_initializeMindMap() {
        // 清空容器
        this.container.innerHTML = '';

        // 创建工具栏
        this.ui_createToolbar();

        // 创建SVG容器
        const svgContainer = document.createElement('div');
        svgContainer.className = 'mindmap-svg-container';
        svgContainer.style.cssText = `
            width: 100%;
            height: calc(100% - 40px);
            overflow: hidden;
            border-radius: 12px;
            background: #FFFFFF;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
        `;

        // 创建SVG元素
        this.svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        this.svg.setAttribute('width', '100%');
        this.svg.setAttribute('height', '100%');
        this.svg.setAttribute('viewBox', `0 0 ${this.options.width} ${this.options.height}`);
        this.svg.style.background = 'transparent';
        this.svg.style.cursor = 'grab';

        // 创建渐变定义
        this.ui_createGradients();

        // 创建主要组
        this.mainGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        this.mainGroup.setAttribute('class', 'mindmap-main');
        this.svg.appendChild(this.mainGroup);

        // 创建连线组
        this.linksGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        this.linksGroup.setAttribute('class', 'mindmap-links');
        this.mainGroup.appendChild(this.linksGroup);

        // 创建节点组
        this.nodesGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        this.nodesGroup.setAttribute('class', 'mindmap-nodes');
        this.mainGroup.appendChild(this.nodesGroup);

        svgContainer.appendChild(this.svg);
        this.container.appendChild(svgContainer);

        console.log('增强思维导图初始化完成');
    }

    /**
     * @function ui_createToolbar - 创建工具栏
     */
    ui_createToolbar() {
        const toolbar = document.createElement('div');
        toolbar.className = 'mindmap-toolbar';
        toolbar.style.cssText = `
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background: rgba(248, 248, 248, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 8px;
            margin-bottom: 8px;
            font-size: 12px;
            color: #1D1D1F;
        `;

        // 左侧控制按钮
        const leftControls = document.createElement('div');
        leftControls.style.cssText = 'display: flex; gap: 8px; align-items: center;';

        // 缩放控制
        const zoomOut = this.ui_createToolbarButton('🔍-', '缩小', () => this.ui_zoom(0.8));
        const zoomReset = this.ui_createToolbarButton('⚪', '重置', () => this.ui_resetView());
        const zoomIn = this.ui_createToolbarButton('🔍+', '放大', () => this.ui_zoom(1.2));

        leftControls.appendChild(zoomOut);
        leftControls.appendChild(zoomReset);
        leftControls.appendChild(zoomIn);

        // 右侧信息
        const rightInfo = document.createElement('div');
        rightInfo.style.cssText = 'font-size: 11px; color: #86868B;';
        rightInfo.textContent = '拖拽移动 • 点击展开';

        toolbar.appendChild(leftControls);
        toolbar.appendChild(rightInfo);
        this.container.appendChild(toolbar);
    }

    /**
     * @function ui_createToolbarButton - 创建工具栏按钮
     */
    ui_createToolbarButton(text, title, onClick) {
        const button = document.createElement('button');
        button.textContent = text;
        button.title = title;
        button.style.cssText = `
            width: 28px;
            height: 28px;
            border: none;
            border-radius: 6px;
            background: rgba(0, 0, 0, 0.05);
            color: #1D1D1F;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        `;

        button.addEventListener('mouseenter', () => {
            button.style.background = 'rgba(0, 0, 0, 0.1)';
            button.style.transform = 'scale(1.05)';
        });

        button.addEventListener('mouseleave', () => {
            button.style.background = 'rgba(0, 0, 0, 0.05)';
            button.style.transform = 'scale(1)';
        });

        button.addEventListener('click', onClick);
        return button;
    }

    /**
     * @function ui_createGradients - 创建渐变定义
     */
    ui_createGradients() {
        const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');

        // 创建节点渐变
        const nodeGradient = document.createElementNS('http://www.w3.org/2000/svg', 'radialGradient');
        nodeGradient.setAttribute('id', 'nodeGradient');
        nodeGradient.setAttribute('cx', '30%');
        nodeGradient.setAttribute('cy', '30%');

        const stop1 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
        stop1.setAttribute('offset', '0%');
        stop1.setAttribute('stop-color', '#FFFFFF');

        const stop2 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
        stop2.setAttribute('offset', '100%');
        stop2.setAttribute('stop-color', '#F2F2F7');

        nodeGradient.appendChild(stop1);
        nodeGradient.appendChild(stop2);
        defs.appendChild(nodeGradient);

        // 创建阴影滤镜
        const shadow = document.createElementNS('http://www.w3.org/2000/svg', 'filter');
        shadow.setAttribute('id', 'shadow');
        shadow.setAttribute('x', '-50%');
        shadow.setAttribute('y', '-50%');
        shadow.setAttribute('width', '200%');
        shadow.setAttribute('height', '200%');

        const shadowOffset = document.createElementNS('http://www.w3.org/2000/svg', 'feDropShadow');
        shadowOffset.setAttribute('dx', '0');
        shadowOffset.setAttribute('dy', '2');
        shadowOffset.setAttribute('stdDeviation', '3');
        shadowOffset.setAttribute('flood-color', 'rgba(0, 0, 0, 0.1)');

        shadow.appendChild(shadowOffset);
        defs.appendChild(shadow);

        this.svg.appendChild(defs);
    }

    /**
     * @function ui_setupInteractions - 设置交互功能
     */
    ui_setupInteractions() {
        // 缩放和平移
        this.svg.addEventListener('wheel', (e) => {
            e.preventDefault();
            const delta = e.deltaY > 0 ? 0.9 : 1.1;
            this.ui_zoom(delta, e.offsetX, e.offsetY);
        });

        // 拖拽平移
        let isPanning = false;
        let startX, startY;

        this.svg.addEventListener('mousedown', (e) => {
            if (e.target === this.svg || e.target === this.mainGroup) {
                isPanning = true;
                startX = e.clientX - this.translateX;
                startY = e.clientY - this.translateY;
                this.svg.style.cursor = 'grabbing';
            }
        });

        this.svg.addEventListener('mousemove', (e) => {
            if (isPanning) {
                this.translateX = e.clientX - startX;
                this.translateY = e.clientY - startY;
                this.ui_updateTransform();
            }
        });

        this.svg.addEventListener('mouseup', () => {
            isPanning = false;
            this.svg.style.cursor = 'grab';
        });

        this.svg.addEventListener('mouseleave', () => {
            isPanning = false;
            this.svg.style.cursor = 'grab';
        });
    }

    /**
     * @function ui_zoom - 缩放功能
     */
    ui_zoom(factor, centerX = null, centerY = null) {
        const newScale = Math.max(0.3, Math.min(3, this.scale * factor));

        if (centerX !== null && centerY !== null) {
            // 以指定点为中心缩放
            const rect = this.svg.getBoundingClientRect();
            const x = centerX - rect.left;
            const y = centerY - rect.top;

            this.translateX = x - (x - this.translateX) * (newScale / this.scale);
            this.translateY = y - (y - this.translateY) * (newScale / this.scale);
        }

        this.scale = newScale;
        this.ui_updateTransform();
    }

    /**
     * @function ui_resetView - 重置视图
     */
    ui_resetView() {
        this.scale = 1;
        this.translateX = 0;
        this.translateY = 0;
        this.ui_updateTransform();
    }

    /**
     * @function ui_updateTransform - 更新变换
     */
    ui_updateTransform() {
        if (this.mainGroup) {
            this.mainGroup.setAttribute('transform',
                `translate(${this.translateX}, ${this.translateY}) scale(${this.scale})`
            );
        }
    }
    
    /**
     * @function renderMindMap - 渲染思维导图
     * @description 根据数据渲染思维导图
     * @param {Object} data - 思维导图数据
     */
    renderMindMap(data) {
        if (!data || !data.nodes) {
            this.ui_renderEmptyState();
            return;
        }
        
        this.nodes = data.nodes;
        this.links = data.links || [];
        
        // 计算节点位置
        this.ui_calculateNodePositions();
        
        // 渲染连线
        this.ui_renderLinks();
        
        // 渲染节点
        this.ui_renderNodes();
        
        console.log('思维导图渲染完成', data);
    }
    
    /**
     * @function ui_calculateNodePositions - 计算节点位置
     * @description 使用简单的层级布局算法计算节点位置
     */
    ui_calculateNodePositions() {
        if (this.nodes.length === 0) return;
        
        // 找到根节点
        const rootNode = this.nodes.find(node => node.isRoot) || this.nodes[0];
        
        // 设置根节点位置
        rootNode.x = this.options.width / 2;
        rootNode.y = this.options.height / 2;
        
        // 按层级组织节点
        const levels = this.ui_organizeLevels(rootNode);
        
        // 计算每层节点位置
        levels.forEach((levelNodes, levelIndex) => {
            if (levelIndex === 0) return; // 跳过根节点
            
            const angleStep = (2 * Math.PI) / levelNodes.length;
            const radius = 80 + levelIndex * 60;
            
            levelNodes.forEach((node, nodeIndex) => {
                const angle = angleStep * nodeIndex;
                node.x = rootNode.x + Math.cos(angle) * radius;
                node.y = rootNode.y + Math.sin(angle) * radius;
                
                // 确保节点在画布范围内
                node.x = Math.max(this.options.nodeRadius, Math.min(this.options.width - this.options.nodeRadius, node.x));
                node.y = Math.max(this.options.nodeRadius, Math.min(this.options.height - this.options.nodeRadius, node.y));
            });
        });
    }
    
    /**
     * @function ui_organizeLevels - 组织节点层级
     * @description 将节点按层级组织
     * @param {Object} rootNode - 根节点
     * @returns {Array} 层级数组
     */
    ui_organizeLevels(rootNode) {
        const levels = [[rootNode]];
        const visited = new Set([rootNode.id]);
        
        let currentLevel = 0;
        
        while (currentLevel < levels.length) {
            const nextLevel = [];
            
            levels[currentLevel].forEach(node => {
                // 找到当前节点的子节点
                this.links.forEach(link => {
                    if (link.source === node.id && !visited.has(link.target)) {
                        const targetNode = this.nodes.find(n => n.id === link.target);
                        if (targetNode) {
                            nextLevel.push(targetNode);
                            visited.add(targetNode.id);
                        }
                    }
                });
            });
            
            if (nextLevel.length > 0) {
                levels.push(nextLevel);
            }
            
            currentLevel++;
        }
        
        return levels;
    }
    
    /**
     * @function ui_renderNodes - 渲染节点
     * @description 渲染所有节点
     */
    ui_renderNodes() {
        // 清空现有节点
        this.nodesGroup.innerHTML = '';
        
        this.nodes.forEach(node => {
            this.ui_renderNode(node);
        });
    }
    
    /**
     * @function ui_renderNode - 渲染单个节点
     * @description 渲染单个思维导图节点
     * @param {Object} node - 节点数据
     */
    ui_renderNode(node) {
        // 创建节点组
        const nodeGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        nodeGroup.setAttribute('class', 'mindmap-node');
        nodeGroup.setAttribute('data-node-id', node.id);

        // 确定节点颜色
        const isRoot = node.isRoot || node.id === 'root';
        const nodeColor = isRoot ? this.options.rootColor :
                         (node.color || this.ui_getBranchColor(node.level || 1));

        // 创建节点阴影
        const shadowCircle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        shadowCircle.setAttribute('cx', node.x + 1);
        shadowCircle.setAttribute('cy', node.y + 2);
        shadowCircle.setAttribute('r', this.options.nodeRadius);
        shadowCircle.setAttribute('fill', 'rgba(0, 0, 0, 0.1)');
        shadowCircle.setAttribute('filter', 'blur(2px)');
        nodeGroup.appendChild(shadowCircle);

        // 创建节点圆圈
        const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        circle.setAttribute('cx', node.x);
        circle.setAttribute('cy', node.y);
        circle.setAttribute('r', this.options.nodeRadius);
        circle.setAttribute('fill', isRoot ? nodeColor : 'url(#nodeGradient)');
        circle.setAttribute('stroke', nodeColor);
        circle.setAttribute('stroke-width', isRoot ? '3' : '2');
        circle.style.cursor = 'pointer';
        circle.style.transition = 'all 0.3s ease';

        // 添加节点交互
        circle.addEventListener('click', (e) => {
            e.stopPropagation();
            this.ui_handleNodeClick(node);
        });

        circle.addEventListener('mouseenter', () => {
            circle.setAttribute('r', this.options.nodeRadius + 3);
            circle.setAttribute('stroke-width', isRoot ? '4' : '3');
            circle.style.filter = 'brightness(1.1)';
        });

        circle.addEventListener('mouseleave', () => {
            circle.setAttribute('r', this.options.nodeRadius);
            circle.setAttribute('stroke-width', isRoot ? '3' : '2');
            circle.style.filter = 'none';
        });

        nodeGroup.appendChild(circle);

        // 创建节点文本
        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.setAttribute('x', node.x);
        text.setAttribute('y', node.y + 4);
        text.setAttribute('text-anchor', 'middle');
        text.setAttribute('font-size', isRoot ? this.options.fontSize + 2 : this.options.fontSize);
        text.setAttribute('font-family', '-apple-system, BlinkMacSystemFont, SF Pro Display, sans-serif');
        text.setAttribute('font-weight', isRoot ? '600' : '500');
        text.setAttribute('fill', isRoot ? '#FFFFFF' : this.options.textColor);
        text.style.pointerEvents = 'none';
        text.style.userSelect = 'none';

        // 处理长文本 - 支持多行
        this.ui_wrapText(text, node.label, this.options.nodeRadius * 1.6);

        // 添加标题提示
        const title = document.createElementNS('http://www.w3.org/2000/svg', 'title');
        title.textContent = node.label + (node.description ? '\n' + node.description : '');
        nodeGroup.appendChild(title);

        nodeGroup.appendChild(text);

        // 如果节点有子节点，添加展开/收起指示器
        if (node.hasChildren) {
            this.ui_addExpandIndicator(nodeGroup, node);
        }

        this.nodesGroup.appendChild(nodeGroup);
    }

    /**
     * @function ui_getBranchColor - 获取分支颜色
     */
    ui_getBranchColor(level) {
        const colors = this.options.branchColors;
        return colors[(level - 1) % colors.length];
    }

    /**
     * @function ui_wrapText - 文本换行
     */
    ui_wrapText(textElement, text, maxWidth) {
        const words = text.split(' ');
        const lineHeight = 14;
        let line = '';
        let lineNumber = 0;
        const maxLines = 2;

        // 清空现有文本
        textElement.textContent = '';

        for (let i = 0; i < words.length && lineNumber < maxLines; i++) {
            const testLine = line + words[i] + ' ';
            const testWidth = testLine.length * 7; // 近似宽度计算

            if (testWidth > maxWidth && line !== '') {
                // 创建文本行
                const tspan = document.createElementNS('http://www.w3.org/2000/svg', 'tspan');
                tspan.setAttribute('x', textElement.getAttribute('x'));
                tspan.setAttribute('dy', lineNumber === 0 ? -lineHeight/2 : lineHeight);
                tspan.textContent = line.trim();
                textElement.appendChild(tspan);

                line = words[i] + ' ';
                lineNumber++;
            } else {
                line = testLine;
            }
        }

        // 添加最后一行
        if (line.trim() && lineNumber < maxLines) {
            const tspan = document.createElementNS('http://www.w3.org/2000/svg', 'tspan');
            tspan.setAttribute('x', textElement.getAttribute('x'));
            tspan.setAttribute('dy', lineNumber === 0 ? 0 : lineHeight);
            tspan.textContent = line.trim() + (words.length > 3 && lineNumber === maxLines - 1 ? '...' : '');
            textElement.appendChild(tspan);
        }
    }

    /**
     * @function ui_addExpandIndicator - 添加展开指示器
     */
    ui_addExpandIndicator(nodeGroup, node) {
        const isExpanded = this.expandedNodes.has(node.id);
        const indicator = document.createElementNS('http://www.w3.org/2000/svg', 'circle');

        indicator.setAttribute('cx', node.x + this.options.nodeRadius - 8);
        indicator.setAttribute('cy', node.y - this.options.nodeRadius + 8);
        indicator.setAttribute('r', '6');
        indicator.setAttribute('fill', '#FFFFFF');
        indicator.setAttribute('stroke', this.options.lineColor);
        indicator.setAttribute('stroke-width', '1');
        indicator.style.cursor = 'pointer';

        const icon = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        icon.setAttribute('x', node.x + this.options.nodeRadius - 8);
        icon.setAttribute('y', node.y - this.options.nodeRadius + 12);
        icon.setAttribute('text-anchor', 'middle');
        icon.setAttribute('font-size', '10');
        icon.setAttribute('fill', this.options.lineColor);
        icon.textContent = isExpanded ? '−' : '+';
        icon.style.pointerEvents = 'none';

        indicator.addEventListener('click', (e) => {
            e.stopPropagation();
            this.ui_toggleNodeExpansion(node);
        });

        nodeGroup.appendChild(indicator);
        nodeGroup.appendChild(icon);
    }
    
    /**
     * @function ui_renderLinks - 渲染连线
     * @description 渲染所有连线
     */
    ui_renderLinks() {
        // 清空现有连线
        this.linksGroup.innerHTML = '';
        
        this.links.forEach(link => {
            this.ui_renderLink(link);
        });
    }
    
    /**
     * @function ui_renderLink - 渲染单个连线
     * @description 渲染单个连线
     * @param {Object} link - 连线数据
     */
    ui_renderLink(link) {
        const sourceNode = this.nodes.find(n => n.id === link.source);
        const targetNode = this.nodes.find(n => n.id === link.target);
        
        if (!sourceNode || !targetNode) return;
        
        // 计算连线端点（避免与节点重叠）
        const dx = targetNode.x - sourceNode.x;
        const dy = targetNode.y - sourceNode.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance === 0) return;
        
        const unitX = dx / distance;
        const unitY = dy / distance;
        
        const startX = sourceNode.x + unitX * this.options.nodeRadius;
        const startY = sourceNode.y + unitY * this.options.nodeRadius;
        const endX = targetNode.x - unitX * this.options.nodeRadius;
        const endY = targetNode.y - unitY * this.options.nodeRadius;
        
        // 创建连线
        const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        line.setAttribute('x1', startX);
        line.setAttribute('y1', startY);
        line.setAttribute('x2', endX);
        line.setAttribute('y2', endY);
        line.setAttribute('stroke', this.options.lineColor);
        line.setAttribute('stroke-width', '2');
        line.setAttribute('marker-end', 'url(#arrowhead)');
        
        this.linksGroup.appendChild(line);
    }
    
    /**
     * @function ui_handleNodeClick - 处理节点点击
     * @description 处理节点点击事件
     * @param {Object} node - 被点击的节点
     */
    ui_handleNodeClick(node) {
        console.log('节点被点击:', node);

        // 触发自定义事件
        const event = new CustomEvent('mindmap-node-click', {
            detail: { node: node }
        });
        this.container.dispatchEvent(event);

        // 如果有子节点，切换展开状态
        if (node.hasChildren) {
            this.ui_toggleNodeExpansion(node);
        }
    }

    /**
     * @function ui_toggleNodeExpansion - 切换节点展开状态
     */
    ui_toggleNodeExpansion(node) {
        if (this.expandedNodes.has(node.id)) {
            this.expandedNodes.delete(node.id);
        } else {
            this.expandedNodes.add(node.id);
        }

        // 重新渲染思维导图
        this.renderMindMap({ nodes: this.nodes, links: this.links });
    }

    /**
     * @function generateMindMapFromAnalysis - 从AI分析结果生成思维导图
     * @param {Object} analysisResult - AI分析结果
     * @returns {Object} 思维导图数据
     */
    generateMindMapFromAnalysis(analysisResult) {
        const nodes = [];
        const links = [];
        let nodeId = 0;

        // 创建根节点
        const rootNode = {
            id: 'root',
            label: analysisResult.topic || analysisResult.contentType || '主题',
            isRoot: true,
            x: this.options.width / 2,
            y: this.options.height / 2,
            level: 0,
            hasChildren: true
        };
        nodes.push(rootNode);

        // 添加摘要节点
        if (analysisResult.summary) {
            const summaryNode = {
                id: `node_${++nodeId}`,
                label: '摘要',
                description: analysisResult.summary,
                level: 1
            };
            nodes.push(summaryNode);
            links.push({ source: 'root', target: summaryNode.id });
        }

        // 添加关键要点节点
        if (analysisResult.keyPoints && analysisResult.keyPoints.length > 0) {
            const keyPointsNode = {
                id: `node_${++nodeId}`,
                label: '关键要点',
                level: 1,
                hasChildren: true
            };
            nodes.push(keyPointsNode);
            links.push({ source: 'root', target: keyPointsNode.id });

            // 添加具体要点
            analysisResult.keyPoints.slice(0, 5).forEach((point, index) => {
                const pointNode = {
                    id: `point_${index}`,
                    label: `要点${index + 1}`,
                    description: point,
                    level: 2
                };
                nodes.push(pointNode);
                links.push({ source: keyPointsNode.id, target: pointNode.id });
            });
        }

        // 添加行动建议节点
        if (analysisResult.actionItems && analysisResult.actionItems.length > 0) {
            const actionNode = {
                id: `node_${++nodeId}`,
                label: '行动建议',
                level: 1,
                hasChildren: true
            };
            nodes.push(actionNode);
            links.push({ source: 'root', target: actionNode.id });

            analysisResult.actionItems.forEach((action, index) => {
                const actionItemNode = {
                    id: `action_${index}`,
                    label: `建议${index + 1}`,
                    description: action,
                    level: 2
                };
                nodes.push(actionItemNode);
                links.push({ source: actionNode.id, target: actionItemNode.id });
            });
        }

        // 添加标签节点
        if (analysisResult.tags && analysisResult.tags.length > 0) {
            const tagsNode = {
                id: `node_${++nodeId}`,
                label: '相关标签',
                level: 1,
                hasChildren: true
            };
            nodes.push(tagsNode);
            links.push({ source: 'root', target: tagsNode.id });

            analysisResult.tags.slice(0, 4).forEach((tag, index) => {
                const tagNode = {
                    id: `tag_${index}`,
                    label: tag,
                    level: 2
                };
                nodes.push(tagNode);
                links.push({ source: tagsNode.id, target: tagNode.id });
            });
        }

        return { nodes, links };
    }
    
    /**
     * @function ui_renderEmptyState - 渲染空状态
     * @description 当没有数据时显示空状态
     */
    ui_renderEmptyState() {
        this.svg.innerHTML = '';
        
        // 创建空状态文本
        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.setAttribute('x', this.options.width / 2);
        text.setAttribute('y', this.options.height / 2);
        text.setAttribute('text-anchor', 'middle');
        text.setAttribute('font-size', '14');
        text.setAttribute('font-family', 'Arial, sans-serif');
        text.setAttribute('fill', '#999');
        text.textContent = '暂无思维导图数据';
        
        this.svg.appendChild(text);
    }
    
    /**
     * @function createSampleData - 创建示例数据
     * @description 创建示例思维导图数据
     * @returns {Object} 示例数据
     */
    static createSampleData() {
        return {
            nodes: [
                { id: 'root', label: '主题', isRoot: true, color: '#667eea' },
                { id: 'node1', label: '分支1', color: '#f8f9fa' },
                { id: 'node2', label: '分支2', color: '#f8f9fa' },
                { id: 'node3', label: '分支3', color: '#f8f9fa' },
                { id: 'node1-1', label: '子分支1-1', color: '#e9ecef' },
                { id: 'node1-2', label: '子分支1-2', color: '#e9ecef' }
            ],
            links: [
                { source: 'root', target: 'node1' },
                { source: 'root', target: 'node2' },
                { source: 'root', target: 'node3' },
                { source: 'node1', target: 'node1-1' },
                { source: 'node1', target: 'node1-2' }
            ]
        };
    }
    
    /**
     * @function clear - 清空思维导图
     * @description 清空当前思维导图
     */
    clear() {
        this.nodes = [];
        this.links = [];
        this.ui_renderEmptyState();
    }
    
    /**
     * @function resize - 调整大小
     * @description 调整思维导图大小
     * @param {number} width - 新宽度
     * @param {number} height - 新高度
     */
    resize(width, height) {
        this.options.width = width;
        this.options.height = height;
        
        if (this.svg) {
            this.svg.setAttribute('viewBox', `0 0 ${width} ${height}`);
        }
        
        // 重新计算位置并渲染
        if (this.nodes.length > 0) {
            this.ui_calculateNodePositions();
            this.ui_renderLinks();
            this.ui_renderNodes();
        }
    }
}
// #endregion

console.log('AI Side Panel MindMap Renderer 已加载');
