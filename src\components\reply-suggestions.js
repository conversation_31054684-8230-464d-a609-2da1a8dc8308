/**
 * @file AI Side Panel 回复建议UI组件
 * @description 智能回复建议的用户界面组件，提供回复展示、复制、选择等功能
 */

// #region 回复建议组件类
/**
 * @class ReplySuggestions - 回复建议组件
 * @description 管理回复建议的显示和交互
 */
class ReplySuggestions {
    /**
     * @function constructor - 构造函数
     * @param {string|HTMLElement} container - 容器元素或选择器
     * @param {Object} options - 配置选项
     */
    constructor(container, options = {}) {
        this.container = typeof container === 'string' 
            ? document.querySelector(container) 
            : container;
            
        if (!this.container) {
            throw new Error('回复建议容器元素未找到');
        }
        
        this.options = {
            showCopyButton: true,
            showTypeLabels: true,
            enableSelection: true,
            maxReplies: 3,
            animationDuration: 300,
            language: 'zh_CN',
            onReplySelect: null,
            onReplyCopy: null,
            ...options
        };
        
        this.replyGenerator = null;
        this.currentReplies = [];
        this.selectedReply = null;
        this.isLoading = false;
        
        this.initialize();
    }
    
    /**
     * @function initialize - 初始化组件
     * @description 初始化回复建议组件
     */
    async initialize() {
        try {
            // 获取回复生成器
            if (typeof getReplyGenerator === 'function') {
                this.replyGenerator = getReplyGenerator();
                await this.replyGenerator.initialize();
            } else {
                console.warn('⚠️ 回复生成器不可用');
            }
            
            // 初始化容器
            this.setupContainer();
            
            // 添加样式
            this.addStyles();
            
            console.log('✅ 回复建议组件初始化完成');
        } catch (error) {
            console.error('❌ 回复建议组件初始化失败:', error);
        }
    }
    
    /**
     * @function setupContainer - 设置容器
     * @description 设置容器的基本结构和样式
     */
    setupContainer() {
        this.container.className = 'reply-suggestions-container';
        this.container.innerHTML = `
            <div class="reply-suggestions-header">
                <h4 class="reply-suggestions-title">💬 智能回复建议</h4>
                <div class="reply-suggestions-actions">
                    <button class="reply-refresh-btn" title="重新生成">🔄</button>
                </div>
            </div>
            <div class="reply-suggestions-content">
                <div class="reply-suggestions-placeholder">
                    <div class="placeholder-icon">🤖</div>
                    <div class="placeholder-text">输入内容后将为您生成智能回复建议</div>
                </div>
            </div>
        `;
        
        // 绑定刷新按钮事件
        const refreshBtn = this.container.querySelector('.reply-refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.regenerateReplies());
        }
    }
    
    /**
     * @function addStyles - 添加样式
     * @description 添加组件所需的CSS样式
     */
    addStyles() {
        const styleId = 'reply-suggestions-styles';
        if (document.getElementById(styleId)) return;
        
        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            .reply-suggestions-container {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(10px);
                border-radius: 12px;
                border: 1px solid rgba(0, 0, 0, 0.1);
                margin: 16px 0;
                overflow: hidden;
                transition: all 0.3s ease;
            }
            
            .reply-suggestions-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px;
                background: rgba(0, 122, 255, 0.05);
                border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            }
            
            .reply-suggestions-title {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
                color: #1d1d1f;
            }
            
            .reply-suggestions-actions {
                display: flex;
                gap: 8px;
            }
            
            .reply-refresh-btn {
                background: none;
                border: none;
                padding: 8px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 16px;
                transition: all 0.2s ease;
            }
            
            .reply-refresh-btn:hover {
                background: rgba(0, 122, 255, 0.1);
                transform: rotate(180deg);
            }
            
            .reply-suggestions-content {
                padding: 16px;
            }
            
            .reply-suggestions-placeholder {
                text-align: center;
                padding: 32px 16px;
                color: #86868b;
            }
            
            .placeholder-icon {
                font-size: 48px;
                margin-bottom: 16px;
            }
            
            .placeholder-text {
                font-size: 14px;
                line-height: 1.5;
            }
            
            .reply-suggestions-loading {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 32px;
                color: #86868b;
            }
            
            .loading-spinner {
                width: 20px;
                height: 20px;
                border: 2px solid #e5e5e7;
                border-top: 2px solid #007aff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin-right: 12px;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            .reply-suggestions-list {
                display: flex;
                flex-direction: column;
                gap: 12px;
            }
            
            .reply-suggestion-item {
                background: #f5f5f7;
                border-radius: 12px;
                padding: 16px;
                border: 2px solid transparent;
                cursor: pointer;
                transition: all 0.2s ease;
                position: relative;
            }
            
            .reply-suggestion-item:hover {
                background: #e8f4fd;
                border-color: rgba(0, 122, 255, 0.3);
                transform: translateY(-1px);
            }
            
            .reply-suggestion-item.selected {
                background: #e8f4fd;
                border-color: #007aff;
            }
            
            .reply-type-label {
                display: inline-block;
                background: #007aff;
                color: white;
                padding: 4px 8px;
                border-radius: 6px;
                font-size: 12px;
                font-weight: 500;
                margin-bottom: 8px;
            }
            
            .reply-type-helpful .reply-type-label {
                background: #34c759;
            }
            
            .reply-type-professional .reply-type-label {
                background: #5856d6;
            }
            
            .reply-type-friendly .reply-type-label {
                background: #ff9500;
            }
            
            .reply-content {
                font-size: 14px;
                line-height: 1.5;
                color: #1d1d1f;
                margin-bottom: 12px;
                word-wrap: break-word;
            }
            
            .reply-actions {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .reply-metadata {
                font-size: 12px;
                color: #86868b;
            }
            
            .reply-copy-btn {
                background: #007aff;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 6px;
                font-size: 12px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
            }
            
            .reply-copy-btn:hover {
                background: #0056b3;
                transform: scale(1.05);
            }
            
            .reply-copy-btn:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                transform: none;
            }
            
            .reply-copy-btn.success {
                background: #34c759;
            }
            
            .reply-copy-btn.error {
                background: #ff3b30;
            }
            
            .reply-confidence {
                display: inline-block;
                width: 8px;
                height: 8px;
                border-radius: 50%;
                margin-left: 8px;
            }
            
            .confidence-high {
                background: #34c759;
            }
            
            .confidence-medium {
                background: #ff9500;
            }
            
            .confidence-low {
                background: #ff3b30;
            }
        `;
        
        document.head.appendChild(style);
    }
    
    /**
     * @function generateReplies - 生成回复建议
     * @description 基于输入内容生成回复建议
     * @param {string} inputContent - 输入内容
     * @param {Object} context - 上下文信息
     * @param {Object} options - 生成选项
     * @returns {Promise<Array>} 回复建议数组
     */
    async generateReplies(inputContent, context = {}, options = {}) {
        if (!inputContent || !inputContent.trim()) {
            this.showPlaceholder();
            return [];
        }
        
        try {
            this.showLoading();
            
            if (!this.replyGenerator || !this.replyGenerator.isReady()) {
                throw new Error('回复生成器未就绪');
            }
            
            const generateOptions = {
                language: this.options.language,
                maxLength: 200,
                includeEmoji: true,
                ...options
            };
            
            const replies = await this.replyGenerator.generateReplies(
                inputContent, 
                context, 
                generateOptions
            );
            
            this.currentReplies = replies.slice(0, this.options.maxReplies);
            this.displayReplies(this.currentReplies);
            
            return this.currentReplies;
            
        } catch (error) {
            console.error('❌ 生成回复建议失败:', error);
            this.showError(error.message);
            return [];
        }
    }

    /**
     * @function displayReplies - 显示回复建议
     * @description 在界面中显示生成的回复建议
     * @param {Array} replies - 回复建议数组
     */
    displayReplies(replies) {
        const content = this.container.querySelector('.reply-suggestions-content');
        if (!content) return;

        if (!replies || replies.length === 0) {
            this.showPlaceholder();
            return;
        }

        const listHTML = replies.map((reply, index) => {
            const typeLabel = this.replyGenerator ?
                this.replyGenerator.getTypeLabel(reply.type, this.options.language) :
                reply.type;

            const confidenceClass = this.getConfidenceClass(reply.confidence);
            const confidenceText = Math.round(reply.confidence * 100) + '%';

            return `
                <div class="reply-suggestion-item reply-type-${reply.type}"
                     data-reply-id="${reply.id}"
                     data-reply-index="${index}">
                    ${this.options.showTypeLabels ? `
                        <div class="reply-type-label">${typeLabel}</div>
                    ` : ''}
                    <div class="reply-content">${this.escapeHtml(reply.content)}</div>
                    <div class="reply-actions">
                        <div class="reply-metadata">
                            <span class="reply-confidence-text">置信度: ${confidenceText}</span>
                            <span class="reply-confidence ${confidenceClass}"></span>
                        </div>
                        ${this.options.showCopyButton ? `
                            <button class="reply-copy-btn" data-reply-content="${this.escapeHtml(reply.content)}">
                                复制
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;
        }).join('');

        content.innerHTML = `
            <div class="reply-suggestions-list">
                ${listHTML}
            </div>
        `;

        // 绑定事件
        this.bindReplyEvents();

        // 添加显示动画
        this.animateReplies();
    }

    /**
     * @function bindReplyEvents - 绑定回复事件
     * @description 为回复建议绑定点击和复制事件
     */
    bindReplyEvents() {
        const replyItems = this.container.querySelectorAll('.reply-suggestion-item');
        const copyButtons = this.container.querySelectorAll('.reply-copy-btn');

        // 绑定回复选择事件
        replyItems.forEach((item, index) => {
            item.addEventListener('click', (event) => {
                if (event.target.classList.contains('reply-copy-btn')) return;

                this.selectReply(index);
            });
        });

        // 绑定复制按钮事件
        copyButtons.forEach((button) => {
            button.addEventListener('click', async (event) => {
                event.stopPropagation();

                const replyContent = button.getAttribute('data-reply-content');
                await this.copyReply(replyContent, button);
            });
        });
    }

    /**
     * @function selectReply - 选择回复
     * @description 选择一个回复建议
     * @param {number} index - 回复索引
     */
    selectReply(index) {
        if (!this.options.enableSelection) return;

        const replyItems = this.container.querySelectorAll('.reply-suggestion-item');

        // 移除之前的选择
        replyItems.forEach(item => item.classList.remove('selected'));

        // 选择新的回复
        if (replyItems[index]) {
            replyItems[index].classList.add('selected');
            this.selectedReply = this.currentReplies[index];

            // 触发选择回调
            if (this.options.onReplySelect && typeof this.options.onReplySelect === 'function') {
                this.options.onReplySelect(this.selectedReply, index);
            }
        }
    }

    /**
     * @function copyReply - 复制回复
     * @description 复制回复内容到剪贴板
     * @param {string} content - 回复内容
     * @param {HTMLElement} button - 复制按钮元素
     */
    async copyReply(content, button) {
        if (!this.replyGenerator) {
            console.error('回复生成器不可用');
            return;
        }

        const originalText = button.textContent;
        button.disabled = true;

        try {
            const success = await this.replyGenerator.copyToClipboard(content);

            if (success) {
                button.textContent = '已复制';
                button.classList.add('success');

                // 触发复制回调
                if (this.options.onReplyCopy && typeof this.options.onReplyCopy === 'function') {
                    this.options.onReplyCopy(content);
                }
            } else {
                throw new Error('复制失败');
            }

        } catch (error) {
            button.textContent = '失败';
            button.classList.add('error');
        }

        // 恢复按钮状态
        setTimeout(() => {
            button.textContent = originalText;
            button.disabled = false;
            button.classList.remove('success', 'error');
        }, 2000);
    }

    /**
     * @function animateReplies - 动画显示回复
     * @description 为回复建议添加显示动画
     */
    animateReplies() {
        const replyItems = this.container.querySelectorAll('.reply-suggestion-item');

        replyItems.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';

            setTimeout(() => {
                item.style.transition = `all ${this.options.animationDuration}ms ease`;
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    /**
     * @function showLoading - 显示加载状态
     * @description 显示回复生成的加载状态
     */
    showLoading() {
        const content = this.container.querySelector('.reply-suggestions-content');
        if (!content) return;

        this.isLoading = true;
        content.innerHTML = `
            <div class="reply-suggestions-loading">
                <div class="loading-spinner"></div>
                <span>正在生成智能回复建议...</span>
            </div>
        `;
    }

    /**
     * @function showPlaceholder - 显示占位符
     * @description 显示默认的占位符内容
     */
    showPlaceholder() {
        const content = this.container.querySelector('.reply-suggestions-content');
        if (!content) return;

        this.isLoading = false;
        this.currentReplies = [];
        this.selectedReply = null;

        content.innerHTML = `
            <div class="reply-suggestions-placeholder">
                <div class="placeholder-icon">🤖</div>
                <div class="placeholder-text">输入内容后将为您生成智能回复建议</div>
            </div>
        `;
    }

    /**
     * @function showError - 显示错误信息
     * @description 显示错误状态
     * @param {string} message - 错误消息
     */
    showError(message) {
        const content = this.container.querySelector('.reply-suggestions-content');
        if (!content) return;

        this.isLoading = false;
        content.innerHTML = `
            <div class="reply-suggestions-placeholder">
                <div class="placeholder-icon">❌</div>
                <div class="placeholder-text">生成回复失败: ${this.escapeHtml(message)}</div>
            </div>
        `;
    }

    /**
     * @function regenerateReplies - 重新生成回复
     * @description 重新生成当前的回复建议
     */
    async regenerateReplies() {
        if (this.isLoading || !this.lastInput) return;

        // 清空缓存以强制重新生成
        if (this.replyGenerator && this.replyGenerator.clearCache) {
            this.replyGenerator.clearCache();
        }

        await this.generateReplies(this.lastInput.content, this.lastInput.context, this.lastInput.options);
    }

    /**
     * @function getConfidenceClass - 获取置信度样式类
     * @description 根据置信度值获取对应的CSS类
     * @param {number} confidence - 置信度值 (0-1)
     * @returns {string} CSS类名
     */
    getConfidenceClass(confidence) {
        if (confidence >= 0.8) return 'confidence-high';
        if (confidence >= 0.6) return 'confidence-medium';
        return 'confidence-low';
    }

    /**
     * @function escapeHtml - 转义HTML
     * @description 转义HTML特殊字符
     * @param {string} text - 原始文本
     * @returns {string} 转义后的文本
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * @function updateLanguage - 更新语言
     * @description 更新组件的显示语言
     * @param {string} language - 新的语言代码
     */
    updateLanguage(language) {
        this.options.language = language;

        // 如果有当前回复，重新显示以更新标签
        if (this.currentReplies.length > 0) {
            this.displayReplies(this.currentReplies);
        }
    }

    /**
     * @function getSelectedReply - 获取选中的回复
     * @description 获取当前选中的回复建议
     * @returns {Object|null} 选中的回复对象
     */
    getSelectedReply() {
        return this.selectedReply;
    }

    /**
     * @function getCurrentReplies - 获取当前回复
     * @description 获取当前显示的所有回复建议
     * @returns {Array} 回复建议数组
     */
    getCurrentReplies() {
        return this.currentReplies;
    }

    /**
     * @function destroy - 销毁组件
     * @description 清理组件资源
     */
    destroy() {
        if (this.container) {
            this.container.innerHTML = '';
            this.container.className = '';
        }

        this.currentReplies = [];
        this.selectedReply = null;
        this.replyGenerator = null;
    }
}
// #endregion

console.log('💬 AI Side Panel 回复建议组件已加载');
