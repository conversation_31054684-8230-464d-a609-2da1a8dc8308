/**
 * @file AI Side Panel 快捷回复模板弹窗组件
 * @description 实现输入框上方的模板弹窗，支持模板搜索、预览、快速插入等功能
 */

// #region 模板弹窗组件类
/**
 * @class TemplatePopup - 模板弹窗组件
 * @description 管理快捷回复模板的弹窗显示和交互
 */
class TemplatePopup {
    /**
     * @function constructor - 构造函数
     * @param {string|HTMLElement} inputElement - 关联的输入框元素
     * @param {Object} options - 配置选项
     */
    constructor(inputElement, options = {}) {
        this.inputElement = typeof inputElement === 'string' 
            ? document.querySelector(inputElement) 
            : inputElement;
            
        if (!this.inputElement) {
            throw new Error('模板弹窗关联的输入框元素未找到');
        }
        
        this.options = {
            maxTemplates: 10,           // 最大显示模板数量
            showPreview: true,          // 是否显示预览
            enableSearch: true,         // 是否启用搜索
            enableKeyboard: true,       // 是否启用键盘导航
            autoShow: true,             // 是否自动显示
            position: 'top',            // 弹窗位置 ('top', 'bottom')
            language: 'zh_CN',          // 当前语言
            onTemplateSelect: null,     // 模板选择回调
            onTemplateInsert: null,     // 模板插入回调
            ...options
        };
        
        this.templateManager = null;
        this.popup = null;
        this.isVisible = false;
        this.currentTemplates = [];
        this.selectedIndex = -1;
        this.searchQuery = '';
        this.isInitialized = false;
        
        this.initialize();
    }
    
    /**
     * @function initialize - 初始化组件
     * @description 初始化模板弹窗组件
     */
    async initialize() {
        try {
            // 获取模板管理器
            if (typeof getTemplateManager === 'function') {
                this.templateManager = getTemplateManager();
                await this.templateManager.initialize();
            } else {
                console.warn('⚠️ 模板管理器不可用');
                return;
            }
            
            // 创建弹窗元素
            this.createPopup();
            
            // 添加样式
            this.addStyles();
            
            // 绑定事件
            this.bindEvents();
            
            this.isInitialized = true;
            console.log('✅ 模板弹窗组件初始化完成');
            
        } catch (error) {
            console.error('❌ 模板弹窗组件初始化失败:', error);
        }
    }
    
    /**
     * @function createPopup - 创建弹窗元素
     * @description 创建模板弹窗的DOM结构
     */
    createPopup() {
        this.popup = document.createElement('div');
        this.popup.className = 'template-popup';
        this.popup.style.display = 'none';
        
        this.popup.innerHTML = `
            <div class="template-popup-header">
                <div class="template-search-container">
                    <input type="text" 
                           class="template-search-input" 
                           placeholder="搜索模板..." 
                           autocomplete="off">
                    <div class="template-search-icon">🔍</div>
                </div>
                <div class="template-popup-actions">
                    <button class="template-manage-btn" title="管理模板">⚙️</button>
                    <button class="template-close-btn" title="关闭">✕</button>
                </div>
            </div>
            <div class="template-popup-content">
                <div class="template-list"></div>
                <div class="template-preview" style="display: none;">
                    <div class="template-preview-header">预览</div>
                    <div class="template-preview-content"></div>
                </div>
            </div>
            <div class="template-popup-footer">
                <div class="template-hint">
                    <span class="template-hint-key">Tab</span> 插入模板
                    <span class="template-hint-key">↑↓</span> 选择
                    <span class="template-hint-key">Esc</span> 关闭
                </div>
            </div>
        `;
        
        // 插入到输入框附近
        this.inputElement.parentNode.insertBefore(this.popup, this.inputElement);
        
        // 设置初始位置
        this.updatePosition();
    }
    
    /**
     * @function addStyles - 添加样式
     * @description 添加模板弹窗所需的CSS样式
     */
    addStyles() {
        const styleId = 'template-popup-styles';
        if (document.getElementById(styleId)) return;
        
        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            .template-popup {
                position: absolute;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-radius: 12px;
                border: 1px solid rgba(0, 0, 0, 0.1);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                z-index: 10000;
                min-width: 320px;
                max-width: 480px;
                max-height: 400px;
                overflow: hidden;
                font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
                transition: all 0.3s ease;
                opacity: 0;
                transform: translateY(-10px);
            }
            
            .template-popup.visible {
                opacity: 1;
                transform: translateY(0);
            }
            
            .template-popup-header {
                display: flex;
                align-items: center;
                padding: 12px 16px;
                background: rgba(0, 122, 255, 0.05);
                border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            }
            
            .template-search-container {
                flex: 1;
                position: relative;
                display: flex;
                align-items: center;
            }
            
            .template-search-input {
                width: 100%;
                padding: 8px 12px 8px 36px;
                border: 1px solid rgba(0, 0, 0, 0.1);
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                outline: none;
                transition: all 0.2s ease;
            }
            
            .template-search-input:focus {
                border-color: #007aff;
                background: rgba(255, 255, 255, 1);
            }
            
            .template-search-icon {
                position: absolute;
                left: 12px;
                color: #86868b;
                font-size: 14px;
                pointer-events: none;
            }
            
            .template-popup-actions {
                display: flex;
                gap: 8px;
                margin-left: 12px;
            }
            
            .template-manage-btn,
            .template-close-btn {
                background: none;
                border: none;
                padding: 8px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                color: #86868b;
                transition: all 0.2s ease;
            }
            
            .template-manage-btn:hover,
            .template-close-btn:hover {
                background: rgba(0, 122, 255, 0.1);
                color: #007aff;
            }
            
            .template-popup-content {
                display: flex;
                max-height: 280px;
            }
            
            .template-list {
                flex: 1;
                overflow-y: auto;
                padding: 8px 0;
            }
            
            .template-item {
                padding: 12px 16px;
                cursor: pointer;
                border-bottom: 1px solid rgba(0, 0, 0, 0.05);
                transition: all 0.2s ease;
                position: relative;
            }
            
            .template-item:last-child {
                border-bottom: none;
            }
            
            .template-item:hover,
            .template-item.selected {
                background: rgba(0, 122, 255, 0.1);
            }
            
            .template-item.selected::before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                bottom: 0;
                width: 3px;
                background: #007aff;
            }
            
            .template-title {
                font-size: 14px;
                font-weight: 600;
                color: #1d1d1f;
                margin-bottom: 4px;
                display: flex;
                align-items: center;
                gap: 8px;
            }
            
            .template-category {
                display: inline-block;
                background: #007aff;
                color: white;
                padding: 2px 6px;
                border-radius: 4px;
                font-size: 10px;
                font-weight: 500;
            }
            
            .template-content-preview {
                font-size: 12px;
                color: #86868b;
                line-height: 1.4;
                max-height: 32px;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
            }
            
            .template-preview {
                width: 200px;
                border-left: 1px solid rgba(0, 0, 0, 0.05);
                background: rgba(248, 248, 248, 0.8);
            }
            
            .template-preview-header {
                padding: 12px 16px;
                font-size: 12px;
                font-weight: 600;
                color: #86868b;
                border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            }
            
            .template-preview-content {
                padding: 16px;
                font-size: 13px;
                line-height: 1.5;
                color: #1d1d1f;
                white-space: pre-wrap;
                word-wrap: break-word;
            }
            
            .template-popup-footer {
                padding: 8px 16px;
                background: rgba(248, 248, 248, 0.8);
                border-top: 1px solid rgba(0, 0, 0, 0.05);
            }
            
            .template-hint {
                font-size: 11px;
                color: #86868b;
                display: flex;
                align-items: center;
                gap: 12px;
            }
            
            .template-hint-key {
                background: rgba(0, 0, 0, 0.1);
                padding: 2px 6px;
                border-radius: 4px;
                font-family: 'SF Mono', Monaco, monospace;
                font-size: 10px;
            }
            
            .template-empty {
                padding: 32px 16px;
                text-align: center;
                color: #86868b;
            }
            
            .template-empty-icon {
                font-size: 32px;
                margin-bottom: 12px;
            }
            
            .template-empty-text {
                font-size: 14px;
                line-height: 1.5;
            }
            
            .template-loading {
                padding: 32px 16px;
                text-align: center;
                color: #86868b;
            }
            
            .template-loading-spinner {
                width: 20px;
                height: 20px;
                border: 2px solid #e5e5e7;
                border-top: 2px solid #007aff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 12px;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            /* 滚动条样式 */
            .template-list::-webkit-scrollbar {
                width: 6px;
            }
            
            .template-list::-webkit-scrollbar-track {
                background: transparent;
            }
            
            .template-list::-webkit-scrollbar-thumb {
                background: rgba(0, 0, 0, 0.2);
                border-radius: 3px;
            }
            
            .template-list::-webkit-scrollbar-thumb:hover {
                background: rgba(0, 0, 0, 0.3);
            }
        `;
        
        document.head.appendChild(style);
    }

    /**
     * @function bindEvents - 绑定事件
     * @description 绑定输入框和弹窗的各种事件
     */
    bindEvents() {
        // 输入框事件
        this.inputElement.addEventListener('focus', () => this.handleInputFocus());
        this.inputElement.addEventListener('blur', (e) => this.handleInputBlur(e));
        this.inputElement.addEventListener('input', (e) => this.handleInputChange(e));
        this.inputElement.addEventListener('keydown', (e) => this.handleInputKeydown(e));

        // 搜索框事件
        const searchInput = this.popup.querySelector('.template-search-input');
        searchInput.addEventListener('input', (e) => this.handleSearchInput(e));
        searchInput.addEventListener('keydown', (e) => this.handleSearchKeydown(e));

        // 按钮事件
        const manageBtn = this.popup.querySelector('.template-manage-btn');
        const closeBtn = this.popup.querySelector('.template-close-btn');

        manageBtn.addEventListener('click', () => this.openTemplateManager());
        closeBtn.addEventListener('click', () => this.hide());

        // 模板列表事件
        const templateList = this.popup.querySelector('.template-list');
        templateList.addEventListener('click', (e) => this.handleTemplateClick(e));
        templateList.addEventListener('mouseover', (e) => this.handleTemplateHover(e));

        // 全局事件
        document.addEventListener('click', (e) => this.handleDocumentClick(e));
        window.addEventListener('resize', () => this.updatePosition());
    }

    /**
     * @function handleInputFocus - 处理输入框获得焦点
     * @description 输入框获得焦点时的处理逻辑
     */
    handleInputFocus() {
        if (this.options.autoShow && this.isInitialized) {
            this.show();
        }
    }

    /**
     * @function handleInputBlur - 处理输入框失去焦点
     * @description 输入框失去焦点时的处理逻辑
     * @param {Event} event - 事件对象
     */
    handleInputBlur(event) {
        // 延迟隐藏，允许点击弹窗内容
        setTimeout(() => {
            if (!this.popup.contains(document.activeElement)) {
                this.hide();
            }
        }, 150);
    }

    /**
     * @function handleInputChange - 处理输入框内容变化
     * @description 输入框内容变化时的处理逻辑
     * @param {Event} event - 事件对象
     */
    handleInputChange(event) {
        const value = event.target.value;

        // 智能预测补全
        if (this.options.enableSearch && value.length > 0) {
            this.searchTemplates(value);
        } else {
            this.loadTemplates();
        }
    }

    /**
     * @function handleInputKeydown - 处理输入框键盘事件
     * @description 处理输入框的键盘导航和快捷键
     * @param {Event} event - 键盘事件
     */
    handleInputKeydown(event) {
        if (!this.isVisible) return;

        switch (event.key) {
            case 'Tab':
                event.preventDefault();
                this.insertSelectedTemplate();
                break;

            case 'ArrowUp':
                event.preventDefault();
                this.selectPreviousTemplate();
                break;

            case 'ArrowDown':
                event.preventDefault();
                this.selectNextTemplate();
                break;

            case 'Escape':
                event.preventDefault();
                this.hide();
                break;

            case 'Enter':
                if (event.ctrlKey || event.metaKey) {
                    event.preventDefault();
                    this.insertSelectedTemplate();
                }
                break;
        }
    }

    /**
     * @function handleSearchInput - 处理搜索输入
     * @description 处理搜索框的输入事件
     * @param {Event} event - 输入事件
     */
    handleSearchInput(event) {
        const query = event.target.value;
        this.searchQuery = query;
        this.searchTemplates(query);
    }

    /**
     * @function handleSearchKeydown - 处理搜索框键盘事件
     * @description 处理搜索框的键盘事件
     * @param {Event} event - 键盘事件
     */
    handleSearchKeydown(event) {
        switch (event.key) {
            case 'ArrowUp':
            case 'ArrowDown':
            case 'Tab':
            case 'Enter':
                event.preventDefault();
                // 将焦点转移到输入框并处理键盘事件
                this.inputElement.focus();
                this.handleInputKeydown(event);
                break;

            case 'Escape':
                event.preventDefault();
                this.hide();
                break;
        }
    }

    /**
     * @function handleTemplateClick - 处理模板点击
     * @description 处理模板项的点击事件
     * @param {Event} event - 点击事件
     */
    handleTemplateClick(event) {
        const templateItem = event.target.closest('.template-item');
        if (!templateItem) return;

        const templateId = templateItem.getAttribute('data-template-id');
        const template = this.currentTemplates.find(t => t.id === templateId);

        if (template) {
            this.selectTemplate(template);
            this.insertTemplate(template);
        }
    }

    /**
     * @function handleTemplateHover - 处理模板悬浮
     * @description 处理模板项的悬浮事件
     * @param {Event} event - 悬浮事件
     */
    handleTemplateHover(event) {
        const templateItem = event.target.closest('.template-item');
        if (!templateItem) return;

        const templateId = templateItem.getAttribute('data-template-id');
        const template = this.currentTemplates.find(t => t.id === templateId);

        if (template) {
            this.selectTemplateById(templateId);
            this.showPreview(template);
        }
    }

    /**
     * @function handleDocumentClick - 处理文档点击
     * @description 处理文档的点击事件，用于关闭弹窗
     * @param {Event} event - 点击事件
     */
    handleDocumentClick(event) {
        if (!this.isVisible) return;

        // 如果点击在弹窗或输入框外，关闭弹窗
        if (!this.popup.contains(event.target) &&
            !this.inputElement.contains(event.target)) {
            this.hide();
        }
    }

    /**
     * @function show - 显示弹窗
     * @description 显示模板弹窗
     */
    async show() {
        if (!this.isInitialized || this.isVisible) return;

        try {
            // 加载模板
            await this.loadTemplates();

            // 更新位置
            this.updatePosition();

            // 显示弹窗
            this.popup.style.display = 'block';

            // 添加显示动画
            requestAnimationFrame(() => {
                this.popup.classList.add('visible');
            });

            this.isVisible = true;

            // 清空搜索
            const searchInput = this.popup.querySelector('.template-search-input');
            searchInput.value = '';
            this.searchQuery = '';

            console.log('📋 模板弹窗已显示');

        } catch (error) {
            console.error('显示模板弹窗失败:', error);
        }
    }

    /**
     * @function hide - 隐藏弹窗
     * @description 隐藏模板弹窗
     */
    hide() {
        if (!this.isVisible) return;

        // 移除显示动画
        this.popup.classList.remove('visible');

        // 延迟隐藏
        setTimeout(() => {
            this.popup.style.display = 'none';
        }, 300);

        this.isVisible = false;
        this.selectedIndex = -1;

        // 隐藏预览
        this.hidePreview();

        console.log('📋 模板弹窗已隐藏');
    }

    /**
     * @function loadTemplates - 加载模板
     * @description 从模板管理器加载模板数据
     */
    async loadTemplates() {
        if (!this.templateManager) return;

        try {
            const templateList = this.popup.querySelector('.template-list');
            templateList.innerHTML = '<div class="template-loading"><div class="template-loading-spinner"></div>加载模板中...</div>';

            // 获取模板
            const templates = await this.templateManager.getTemplates({
                language: this.options.language,
                limit: this.options.maxTemplates,
                active: true
            });

            this.currentTemplates = templates;
            this.displayTemplates(templates);

        } catch (error) {
            console.error('加载模板失败:', error);
            this.showError('加载模板失败');
        }
    }

    /**
     * @function searchTemplates - 搜索模板
     * @description 根据查询条件搜索模板
     * @param {string} query - 搜索查询
     */
    async searchTemplates(query) {
        if (!this.templateManager || !query.trim()) {
            await this.loadTemplates();
            return;
        }

        try {
            const templates = await this.templateManager.searchTemplates(query, {
                language: this.options.language,
                limit: this.options.maxTemplates
            });

            this.currentTemplates = templates;
            this.displayTemplates(templates);

        } catch (error) {
            console.error('搜索模板失败:', error);
            this.showError('搜索模板失败');
        }
    }

    /**
     * @function displayTemplates - 显示模板列表
     * @description 在弹窗中显示模板列表
     * @param {Array} templates - 模板数组
     */
    displayTemplates(templates) {
        const templateList = this.popup.querySelector('.template-list');

        if (!templates || templates.length === 0) {
            templateList.innerHTML = `
                <div class="template-empty">
                    <div class="template-empty-icon">📝</div>
                    <div class="template-empty-text">
                        ${this.searchQuery ? '未找到匹配的模板' : '暂无可用模板'}
                    </div>
                </div>
            `;
            return;
        }

        const templatesHTML = templates.map((template, index) => {
            const categoryColor = this.getCategoryColor(template.category);

            return `
                <div class="template-item"
                     data-template-id="${template.id}"
                     data-index="${index}">
                    <div class="template-title">
                        ${this.escapeHtml(template.title)}
                        ${template.category ? `
                            <span class="template-category" style="background-color: ${categoryColor}">
                                ${this.escapeHtml(template.category)}
                            </span>
                        ` : ''}
                    </div>
                    <div class="template-content-preview">
                        ${this.escapeHtml(template.content.substring(0, 100))}${template.content.length > 100 ? '...' : ''}
                    </div>
                </div>
            `;
        }).join('');

        templateList.innerHTML = templatesHTML;

        // 默认选择第一个模板
        if (templates.length > 0) {
            this.selectedIndex = 0;
            this.updateSelection();
            this.showPreview(templates[0]);
        }
    }

    /**
     * @function selectTemplate - 选择模板
     * @description 选择指定的模板
     * @param {Object} template - 模板对象
     */
    selectTemplate(template) {
        const index = this.currentTemplates.findIndex(t => t.id === template.id);
        if (index !== -1) {
            this.selectedIndex = index;
            this.updateSelection();
            this.showPreview(template);

            // 触发选择回调
            if (this.options.onTemplateSelect && typeof this.options.onTemplateSelect === 'function') {
                this.options.onTemplateSelect(template);
            }
        }
    }

    /**
     * @function selectTemplateById - 根据ID选择模板
     * @description 根据模板ID选择模板
     * @param {string} templateId - 模板ID
     */
    selectTemplateById(templateId) {
        const template = this.currentTemplates.find(t => t.id === templateId);
        if (template) {
            this.selectTemplate(template);
        }
    }

    /**
     * @function selectNextTemplate - 选择下一个模板
     * @description 选择列表中的下一个模板
     */
    selectNextTemplate() {
        if (this.currentTemplates.length === 0) return;

        this.selectedIndex = (this.selectedIndex + 1) % this.currentTemplates.length;
        this.updateSelection();
        this.showPreview(this.currentTemplates[this.selectedIndex]);
    }

    /**
     * @function selectPreviousTemplate - 选择上一个模板
     * @description 选择列表中的上一个模板
     */
    selectPreviousTemplate() {
        if (this.currentTemplates.length === 0) return;

        this.selectedIndex = this.selectedIndex <= 0
            ? this.currentTemplates.length - 1
            : this.selectedIndex - 1;
        this.updateSelection();
        this.showPreview(this.currentTemplates[this.selectedIndex]);
    }

    /**
     * @function updateSelection - 更新选择状态
     * @description 更新模板列表的选择状态显示
     */
    updateSelection() {
        const templateItems = this.popup.querySelectorAll('.template-item');

        templateItems.forEach((item, index) => {
            if (index === this.selectedIndex) {
                item.classList.add('selected');
                // 滚动到可见区域
                item.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
            } else {
                item.classList.remove('selected');
            }
        });
    }

    /**
     * @function insertSelectedTemplate - 插入选中的模板
     * @description 插入当前选中的模板到输入框
     */
    insertSelectedTemplate() {
        if (this.selectedIndex >= 0 && this.currentTemplates[this.selectedIndex]) {
            const template = this.currentTemplates[this.selectedIndex];
            this.insertTemplate(template);
        }
    }

    /**
     * @function insertTemplate - 插入模板
     * @description 将模板内容插入到输入框
     * @param {Object} template - 模板对象
     */
    insertTemplate(template) {
        if (!template || !this.inputElement) return;

        try {
            // 获取当前光标位置
            const cursorPosition = this.inputElement.selectionStart || 0;
            const currentValue = this.inputElement.value;

            // 插入模板内容
            const beforeCursor = currentValue.substring(0, cursorPosition);
            const afterCursor = currentValue.substring(this.inputElement.selectionEnd || cursorPosition);
            const newValue = beforeCursor + template.content + afterCursor;

            // 更新输入框内容
            this.inputElement.value = newValue;

            // 设置新的光标位置
            const newCursorPosition = cursorPosition + template.content.length;
            this.inputElement.setSelectionRange(newCursorPosition, newCursorPosition);

            // 触发input事件
            this.inputElement.dispatchEvent(new Event('input', { bubbles: true }));

            // 隐藏弹窗
            this.hide();

            // 聚焦输入框
            this.inputElement.focus();

            // 触发插入回调
            if (this.options.onTemplateInsert && typeof this.options.onTemplateInsert === 'function') {
                this.options.onTemplateInsert(template);
            }

            console.log('📋 模板已插入:', template.title);

        } catch (error) {
            console.error('插入模板失败:', error);
        }
    }

    /**
     * @function showPreview - 显示模板预览
     * @description 在预览区域显示模板内容
     * @param {Object} template - 模板对象
     */
    showPreview(template) {
        if (!this.options.showPreview || !template) return;

        const preview = this.popup.querySelector('.template-preview');
        const previewContent = this.popup.querySelector('.template-preview-content');

        if (preview && previewContent) {
            previewContent.textContent = template.content;
            preview.style.display = 'block';
        }
    }

    /**
     * @function hidePreview - 隐藏模板预览
     * @description 隐藏模板预览区域
     */
    hidePreview() {
        const preview = this.popup.querySelector('.template-preview');
        if (preview) {
            preview.style.display = 'none';
        }
    }

    /**
     * @function updatePosition - 更新弹窗位置
     * @description 根据输入框位置更新弹窗位置
     */
    updatePosition() {
        if (!this.inputElement || !this.popup) return;

        const inputRect = this.inputElement.getBoundingClientRect();
        const popupRect = this.popup.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;

        let top, left;

        if (this.options.position === 'bottom' ||
            (this.options.position === 'top' && inputRect.top < popupRect.height + 20)) {
            // 显示在输入框下方
            top = inputRect.bottom + 8;
        } else {
            // 显示在输入框上方
            top = inputRect.top - popupRect.height - 8;
        }

        // 水平居中对齐
        left = inputRect.left + (inputRect.width - popupRect.width) / 2;

        // 确保不超出视口
        if (left < 10) left = 10;
        if (left + popupRect.width > viewportWidth - 10) {
            left = viewportWidth - popupRect.width - 10;
        }

        if (top < 10) top = 10;
        if (top + popupRect.height > viewportHeight - 10) {
            top = viewportHeight - popupRect.height - 10;
        }

        this.popup.style.left = left + 'px';
        this.popup.style.top = top + 'px';
    }

    /**
     * @function openTemplateManager - 打开模板管理器
     * @description 打开模板管理界面
     */
    openTemplateManager() {
        // 这里可以打开模板管理界面
        console.log('打开模板管理器');

        // 暂时显示开发中提示
        alert('模板管理功能正在开发中...');
    }

    /**
     * @function showError - 显示错误信息
     * @description 在模板列表中显示错误信息
     * @param {string} message - 错误消息
     */
    showError(message) {
        const templateList = this.popup.querySelector('.template-list');
        templateList.innerHTML = `
            <div class="template-empty">
                <div class="template-empty-icon">❌</div>
                <div class="template-empty-text">${this.escapeHtml(message)}</div>
            </div>
        `;
    }

    /**
     * @function getCategoryColor - 获取分类颜色
     * @description 根据分类名称获取对应的颜色
     * @param {string} category - 分类名称
     * @returns {string} 颜色值
     */
    getCategoryColor(category) {
        const colors = {
            '问候': '#34c759',
            '感谢': '#007aff',
            '道歉': '#ff9500',
            '询问': '#5856d6',
            '确认': '#00c7be',
            '结束': '#8e8e93'
        };

        return colors[category] || '#007aff';
    }

    /**
     * @function escapeHtml - 转义HTML
     * @description 转义HTML特殊字符
     * @param {string} text - 原始文本
     * @returns {string} 转义后的文本
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * @function updateLanguage - 更新语言
     * @description 更新组件的显示语言
     * @param {string} language - 新的语言代码
     */
    updateLanguage(language) {
        this.options.language = language;

        // 重新加载模板
        if (this.isVisible) {
            this.loadTemplates();
        }
    }

    /**
     * @function destroy - 销毁组件
     * @description 清理组件资源
     */
    destroy() {
        if (this.popup && this.popup.parentNode) {
            this.popup.parentNode.removeChild(this.popup);
        }

        this.popup = null;
        this.templateManager = null;
        this.currentTemplates = [];
        this.isInitialized = false;
    }
}
// #endregion

console.log('📋 AI Side Panel 模板弹窗组件已加载');
