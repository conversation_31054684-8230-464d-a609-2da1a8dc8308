/**
 * @file 调试面板样式
 * @description Apple Design风格的调试面板样式
 */

/* #region 调试面板主容器 */
.aisp-debug-panel {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
    font-size: 13px;
    line-height: 1.4;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .aisp-debug-panel {
        background: rgba(28, 28, 30, 0.95);
        border-top-color: rgba(255, 255, 255, 0.1);
        color: #ffffff;
    }
}

/* #endregion */

/* #region 调试面板头部 */
.aisp-debug-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background: rgba(248, 248, 248, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

@media (prefers-color-scheme: dark) {
    .aisp-debug-header {
        background: rgba(44, 44, 46, 0.8);
        border-bottom-color: rgba(255, 255, 255, 0.1);
    }
}

.aisp-debug-title {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #1d1d1f;
    display: flex;
    align-items: center;
    gap: 8px;
}

@media (prefers-color-scheme: dark) {
    .aisp-debug-title {
        color: #f5f5f7;
    }
}

.aisp-debug-icon {
    font-size: 16px;
}

.aisp-debug-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.aisp-debug-filter {
    padding: 4px 8px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    background: #ffffff;
    font-size: 12px;
    color: #1d1d1f;
    outline: none;
    transition: all 0.2s ease;
}

.aisp-debug-filter:focus {
    border-color: #007aff;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

@media (prefers-color-scheme: dark) {
    .aisp-debug-filter {
        background: #2c2c2e;
        border-color: rgba(255, 255, 255, 0.2);
        color: #ffffff;
    }
}

.aisp-debug-btn {
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.05);
    color: #1d1d1f;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.2s ease;
}

.aisp-debug-btn:hover {
    background: rgba(0, 0, 0, 0.1);
    transform: scale(1.05);
}

.aisp-debug-btn:active {
    transform: scale(0.95);
}

@media (prefers-color-scheme: dark) {
    .aisp-debug-btn {
        background: rgba(255, 255, 255, 0.1);
        color: #ffffff;
    }
    
    .aisp-debug-btn:hover {
        background: rgba(255, 255, 255, 0.2);
    }
}

/* #endregion */

/* #region 调试面板内容 */
.aisp-debug-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.aisp-debug-stats {
    display: flex;
    padding: 12px 16px;
    gap: 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background: rgba(250, 250, 250, 0.8);
}

@media (prefers-color-scheme: dark) {
    .aisp-debug-stats {
        background: rgba(36, 36, 38, 0.8);
        border-bottom-color: rgba(255, 255, 255, 0.1);
    }
}

.aisp-debug-stat {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.aisp-debug-stat-label {
    font-size: 11px;
    color: #86868b;
    font-weight: 500;
}

.aisp-debug-stat-value {
    font-size: 14px;
    font-weight: 600;
    color: #1d1d1f;
}

@media (prefers-color-scheme: dark) {
    .aisp-debug-stat-value {
        color: #f5f5f7;
    }
}

.aisp-debug-logs {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
    background: #ffffff;
}

@media (prefers-color-scheme: dark) {
    .aisp-debug-logs {
        background: #1c1c1e;
    }
}

/* #endregion */

/* #region 日志条目 */
.aisp-debug-log-entry {
    margin-bottom: 8px;
    padding: 8px 12px;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.02);
    border-left: 3px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
}

.aisp-debug-log-entry:hover {
    background: rgba(0, 0, 0, 0.05);
    transform: translateX(2px);
}

.aisp-debug-log-entry.expanded {
    background: rgba(0, 122, 255, 0.05);
}

@media (prefers-color-scheme: dark) {
    .aisp-debug-log-entry {
        background: rgba(255, 255, 255, 0.05);
    }
    
    .aisp-debug-log-entry:hover {
        background: rgba(255, 255, 255, 0.1);
    }
    
    .aisp-debug-log-entry.expanded {
        background: rgba(0, 122, 255, 0.15);
    }
}

/* 日志级别颜色 */
.aisp-debug-log-error {
    border-left-color: #ff3b30;
}

.aisp-debug-log-warn {
    border-left-color: #ff9500;
}

.aisp-debug-log-info {
    border-left-color: #007aff;
}

.aisp-debug-log-debug {
    border-left-color: #34c759;
}

.aisp-debug-log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.aisp-debug-log-time {
    font-size: 11px;
    color: #86868b;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
}

.aisp-debug-log-level {
    font-size: 11px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

.aisp-debug-log-message {
    font-size: 12px;
    color: #1d1d1f;
    line-height: 1.4;
    word-break: break-word;
}

@media (prefers-color-scheme: dark) {
    .aisp-debug-log-message {
        color: #f5f5f7;
    }
}

.aisp-debug-log-data {
    margin-top: 8px;
    padding: 8px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
    font-size: 11px;
    color: #1d1d1f;
    max-height: 200px;
    overflow-y: auto;
    display: none;
}

.aisp-debug-log-entry.expanded .aisp-debug-log-data {
    display: block;
}

@media (prefers-color-scheme: dark) {
    .aisp-debug-log-data {
        background: rgba(255, 255, 255, 0.1);
        color: #f5f5f7;
    }
}

.aisp-debug-log-data pre {
    margin: 0;
    white-space: pre-wrap;
    word-break: break-word;
}

/* #endregion */

/* #region 调试面板底部 */
.aisp-debug-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 16px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    background: rgba(248, 248, 248, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

@media (prefers-color-scheme: dark) {
    .aisp-debug-footer {
        background: rgba(44, 44, 46, 0.8);
        border-top-color: rgba(255, 255, 255, 0.1);
    }
}

.aisp-debug-checkbox {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #1d1d1f;
    cursor: pointer;
    user-select: none;
}

@media (prefers-color-scheme: dark) {
    .aisp-debug-checkbox {
        color: #f5f5f7;
    }
}

.aisp-debug-checkbox input[type="checkbox"] {
    width: 14px;
    height: 14px;
    accent-color: #007aff;
}

.aisp-debug-log-count {
    font-size: 11px;
    color: #86868b;
    font-weight: 500;
}

/* #endregion */

/* #region 滚动条样式 */
.aisp-debug-logs::-webkit-scrollbar {
    width: 6px;
}

.aisp-debug-logs::-webkit-scrollbar-track {
    background: transparent;
}

.aisp-debug-logs::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.aisp-debug-logs::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

@media (prefers-color-scheme: dark) {
    .aisp-debug-logs::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
    }
    
    .aisp-debug-logs::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.4);
    }
}

/* #endregion */

/* #region 响应式设计 */
@media (max-width: 400px) {
    .aisp-debug-stats {
        flex-direction: column;
        gap: 12px;
    }
    
    .aisp-debug-stat {
        flex-direction: row;
        align-items: center;
        gap: 8px;
    }
    
    .aisp-debug-controls {
        gap: 4px;
    }
    
    .aisp-debug-btn {
        width: 24px;
        height: 24px;
        font-size: 11px;
    }
}

/* #endregion */
