/**
 * @file AI Side Panel 聊天界面组件
 * @description 处理聊天界面的消息显示、用户交互和界面更新
 */

// #region 聊天界面类定义
/**
 * @class ui_ChatInterface - 聊天界面组件类
 * @description 管理聊天消息的显示和交互
 */
class ui_ChatInterface {
    /**
     * @constructor
     * @param {string} containerId - 聊天容器的ID
     */
    constructor(containerId) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.messages = [];
        this.messageIdCounter = 0;
        
        if (!this.container) {
            console.error(`聊天容器未找到: ${containerId}`);
            return;
        }
        
        this.ui_initializeChatInterface();
    }
    
    /**
     * @function ui_initializeChatInterface - 初始化聊天界面
     * @description 设置聊天界面的基本结构和事件监听
     */
    ui_initializeChatInterface() {
        this.container.className = 'aisp-message-list';
        
        // 添加滚动事件监听
        this.container.addEventListener('scroll', util_throttle(() => {
            this.ui_handleScroll();
        }, 100));
        
        console.log('聊天界面初始化完成');
    }
    
    /**
     * @function addMessage - 添加消息
     * @description 向聊天界面添加新消息
     * @param {string} content - 消息内容
     * @param {string} type - 消息类型 ('user', 'ai', 'system')
     * @param {Object} options - 可选参数
     * @returns {string} 消息ID
     */
    addMessage(content, type = 'user', options = {}) {
        const messageId = `msg_${++this.messageIdCounter}`;
        const timestamp = new Date();

        const message = {
            id: messageId,
            content: content,
            type: type,
            timestamp: timestamp,
            ...options
        };

        this.messages.push(message);
        this.ui_renderMessage(message);
        this.ui_scrollToBottom();

        return messageId;
    }

    /**
     * @function addStreamMessage - 添加流式消息
     * @description 添加支持流式更新的消息
     * @param {string} type - 消息类型 ('user', 'ai', 'system')
     * @param {Object} options - 可选参数
     * @returns {Object} 消息对象和更新函数
     */
    addStreamMessage(type = 'ai', options = {}) {
        const messageId = `msg_${++this.messageIdCounter}`;
        const timestamp = new Date();

        const message = {
            id: messageId,
            content: '',
            type: type,
            timestamp: timestamp,
            isStreaming: true,
            ...options
        };

        this.messages.push(message);
        const messageElement = this.ui_renderMessage(message);
        this.ui_scrollToBottom();

        // 返回更新函数
        const updateContent = (newContent, isComplete = false) => {
            message.content = newContent;
            message.isStreaming = !isComplete;

            const textDiv = messageElement.querySelector('.aisp-message-text');
            if (textDiv) {
                if (message.type === 'ai' && !isComplete) {
                    // AI消息使用打字机效果
                    this.ui_typewriterEffect(textDiv, newContent);
                } else {
                    textDiv.textContent = newContent;
                }
            }

            if (isComplete) {
                messageElement.classList.remove('aisp-message-streaming');
                messageElement.classList.add('aisp-message-complete');
            }

            this.ui_scrollToBottom();
        };

        return {
            messageId,
            updateContent,
            element: messageElement
        };
    }
    
    /**
     * @function ui_renderMessage - 渲染消息
     * @description 将消息渲染到DOM中
     * @param {Object} message - 消息对象
     */
    ui_renderMessage(message) {
        const messageElement = this.ui_createMessageElement(message);
        this.container.appendChild(messageElement);
        
        // 添加动画效果
        requestAnimationFrame(() => {
            messageElement.classList.add('aisp-message-visible');
        });
    }
    
    /**
     * @function ui_createMessageElement - 创建消息元素
     * @description 创建消息的DOM元素
     * @param {Object} message - 消息对象
     * @returns {Element} 消息DOM元素
     */
    ui_createMessageElement(message) {
        const messageDiv = util_createElement('div', {
            className: `aisp-message aisp-message-${message.type}`,
            'data-message-id': message.id
        });
        
        // 创建消息内容容器
        const contentDiv = util_createElement('div', {
            className: 'aisp-message-content'
        });
        
        // 创建消息文本
        const textDiv = util_createElement('div', {
            className: 'aisp-message-text'
        });
        
        // 根据消息类型处理内容
        if (message.type === 'system') {
            textDiv.innerHTML = this.ui_formatSystemMessage(message.content);
        } else {
            textDiv.textContent = message.content;
        }
        
        contentDiv.appendChild(textDiv);
        
        // 添加时间戳
        if (message.showTimestamp !== false) {
            const timestampDiv = util_createElement('div', {
                className: 'aisp-message-timestamp'
            }, util_getRelativeTime(message.timestamp));
            
            contentDiv.appendChild(timestampDiv);
        }
        
        // 添加操作按钮
        if (message.type === 'ai' || message.type === 'system') {
            const actionsDiv = this.ui_createMessageActions(message);
            contentDiv.appendChild(actionsDiv);
        }
        
        messageDiv.appendChild(contentDiv);
        
        return messageDiv;
    }
    
    /**
     * @function ui_formatSystemMessage - 格式化系统消息
     * @description 格式化系统消息的HTML内容
     * @param {string} content - 消息内容
     * @returns {string} 格式化后的HTML
     */
    ui_formatSystemMessage(content) {
        // 处理Markdown样式的文本
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }
    
    /**
     * @function ui_createMessageActions - 创建消息操作按钮
     * @description 为消息创建操作按钮（复制、重新生成等）
     * @param {Object} message - 消息对象
     * @returns {Element} 操作按钮容器
     */
    ui_createMessageActions(message) {
        const actionsDiv = util_createElement('div', {
            className: 'aisp-message-actions'
        });
        
        // 复制按钮
        const copyBtn = util_createElement('button', {
            className: 'aisp-message-action-btn aisp-copy-btn',
            title: lang_getCommonText('copy') || '复制'
        }, '📋');
        
        copyBtn.addEventListener('click', () => {
            this.ui_copyMessage(message);
        });
        
        actionsDiv.appendChild(copyBtn);
        
        // 如果是AI消息，添加重新生成按钮
        if (message.type === 'ai') {
            const regenerateBtn = util_createElement('button', {
                className: 'aisp-message-action-btn aisp-regenerate-btn',
                title: '重新生成'
            }, '🔄');
            
            regenerateBtn.addEventListener('click', () => {
                this.ui_regenerateMessage(message);
            });
            
            actionsDiv.appendChild(regenerateBtn);
        }
        
        return actionsDiv;
    }
    
    /**
     * @function ui_copyMessage - 复制消息内容
     * @description 将消息内容复制到剪贴板
     * @param {Object} message - 消息对象
     */
    async ui_copyMessage(message) {
        try {
            await navigator.clipboard.writeText(message.content);
            this.ui_showToast('已复制到剪贴板', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            this.ui_showToast('复制失败', 'error');
        }
    }
    
    /**
     * @function ui_regenerateMessage - 重新生成消息
     * @description 重新生成AI消息
     * @param {Object} message - 消息对象
     */
    ui_regenerateMessage(message) {
        // 这里将在后续实现AI集成时完善
        this.ui_showToast('重新生成功能正在开发中', 'info');
    }
    
    /**
     * @function ui_scrollToBottom - 滚动到底部
     * @description 将聊天界面滚动到最新消息
     */
    ui_scrollToBottom() {
        if (this.container) {
            this.container.scrollTop = this.container.scrollHeight;
        }
    }
    
    /**
     * @function ui_handleScroll - 处理滚动事件
     * @description 处理聊天界面的滚动事件
     */
    ui_handleScroll() {
        // 检查是否滚动到顶部，用于加载历史消息
        if (this.container.scrollTop === 0) {
            this.ui_loadMoreMessages();
        }
    }
    
    /**
     * @function ui_loadMoreMessages - 加载更多消息
     * @description 加载历史消息（占位函数）
     */
    ui_loadMoreMessages() {
        // 这里将在后续实现消息历史功能时完善
        console.log('加载更多消息功能待实现');
    }

    /**
     * @function ui_typewriterEffect - 打字机效果
     * @description 智能打字机效果，支持增量更新、性能优化和内存管理
     * @param {Element} element - 目标元素
     * @param {string} text - 要显示的文本
     * @param {number} speed - 基础打字速度（毫秒）
     */
    ui_typewriterEffect(element, text, speed = 30) {
        if (!element || !text) return;

        // 清除之前的打字机效果
        if (element._typewriterTimer) {
            clearTimeout(element._typewriterTimer);
            element._typewriterTimer = null;
        }

        // 清除暂停状态
        if (element._typewriterPaused) {
            element._typewriterPaused = false;
        }

        const currentText = element.textContent || '';

        // 如果新文本是当前文本的扩展，只添加新字符
        if (text.startsWith(currentText) && text.length > currentText.length) {
            const newChars = text.slice(currentText.length);
            let charIndex = 0;

            // 智能速度调整：根据文本长度动态调整速度
            const adaptiveSpeed = this._calculateAdaptiveSpeed(newChars.length, speed);

            // 性能监控
            const startTime = Date.now();
            let lastUpdateTime = startTime;

            const addNextChar = () => {
                // 检查元素是否仍在DOM中
                if (!element.isConnected) {
                    return;
                }

                // 检查是否被暂停
                if (element._typewriterPaused) {
                    element._typewriterTimer = setTimeout(addNextChar, 100);
                    return;
                }

                if (charIndex < newChars.length) {
                    element.textContent += newChars[charIndex];
                    charIndex++;

                    // 节流滚动更新
                    const now = Date.now();
                    if (now - lastUpdateTime > 50) { // 最多每50ms滚动一次
                        this.ui_scrollToBottom();
                        lastUpdateTime = now;
                    }

                    // 继续添加下一个字符
                    element._typewriterTimer = setTimeout(addNextChar, adaptiveSpeed);
                } else {
                    // 完成时的最终滚动
                    this.ui_scrollToBottom();

                    // 记录性能指标
                    const duration = Date.now() - startTime;
                    this.logger?.debug('打字机效果完成', {
                        textLength: newChars.length,
                        duration,
                        avgSpeed: Math.round(newChars.length / (duration / 1000))
                    });
                }
            };

            addNextChar();
        } else if (currentText !== text) {
            // 如果文本完全不同，直接替换
            element.textContent = text;
            this.ui_scrollToBottom();
        }
    }

    /**
     * @function _calculateAdaptiveSpeed - 计算自适应打字速度
     * @description 根据文本长度动态调整打字速度
     * @param {number} textLength - 文本长度
     * @param {number} baseSpeed - 基础速度
     * @returns {number} 调整后的速度
     * @private
     */
    _calculateAdaptiveSpeed(textLength, baseSpeed) {
        if (textLength <= 50) {
            return baseSpeed; // 短文本使用正常速度
        } else if (textLength <= 200) {
            return Math.max(baseSpeed * 0.7, 15); // 中等文本加快30%
        } else {
            return Math.max(baseSpeed * 0.5, 10); // 长文本加快50%
        }
    }

    /**
     * @function ui_pauseTypewriter - 暂停打字机效果
     * @description 暂停指定元素的打字机效果
     * @param {Element} element - 目标元素
     */
    ui_pauseTypewriter(element) {
        if (element) {
            element._typewriterPaused = true;
        }
    }

    /**
     * @function ui_resumeTypewriter - 恢复打字机效果
     * @description 恢复指定元素的打字机效果
     * @param {Element} element - 目标元素
     */
    ui_resumeTypewriter(element) {
        if (element) {
            element._typewriterPaused = false;
        }
    }

    /**
     * @function ui_updateStreamingMessage - 更新流式消息
     * @description 更新正在流式传输的消息内容
     * @param {string} messageId - 消息ID
     * @param {string} content - 新内容
     * @param {boolean} isComplete - 是否完成
     */
    ui_updateStreamingMessage(messageId, content, isComplete = false) {
        const messageElement = this.container.querySelector(`[data-message-id="${messageId}"]`);
        if (!messageElement) return;

        const textDiv = messageElement.querySelector('.aisp-message-text');
        if (!textDiv) return;

        // 找到对应的消息对象
        const message = this.messages.find(msg => msg.id === messageId);
        if (message) {
            message.content = content;
            message.isStreaming = !isComplete;
        }

        // 使用打字机效果更新内容
        if (!isComplete) {
            this.ui_typewriterEffect(textDiv, content, 20); // 更快的速度用于流式更新
        } else {
            textDiv.textContent = content;
            messageElement.classList.remove('aisp-message-streaming');
            messageElement.classList.add('aisp-message-complete');
        }

        this.ui_scrollToBottom();
    }
    
    /**
     * @function clearMessages - 清空所有消息
     * @description 清空聊天界面的所有消息，并清理定时器
     */
    clearMessages() {
        // 清理所有打字机效果定时器
        if (this.container) {
            const elements = this.container.querySelectorAll('[data-message-id]');
            elements.forEach(element => {
                const textDiv = element.querySelector('.aisp-message-text');
                if (textDiv && textDiv._typewriterTimer) {
                    clearTimeout(textDiv._typewriterTimer);
                    textDiv._typewriterTimer = null;
                }
            });
            this.container.innerHTML = '';
        }

        this.messages = [];
        this.messageIdCounter = 0;
    }

    /**
     * @function destroy - 销毁聊天界面
     * @description 清理所有资源，防止内存泄漏
     */
    destroy() {
        this.clearMessages();

        // 移除事件监听器
        if (this.container) {
            this.container.removeEventListener('scroll', this._scrollHandler);
        }

        // 清空引用
        this.container = null;
        this.messages = null;
    }
    
    /**
     * @function removeMessage - 删除指定消息
     * @description 删除指定ID的消息
     * @param {string} messageId - 消息ID
     */
    removeMessage(messageId) {
        // 从数组中移除
        this.messages = this.messages.filter(msg => msg.id !== messageId);
        
        // 从DOM中移除
        const messageElement = this.container.querySelector(`[data-message-id="${messageId}"]`);
        if (messageElement) {
            messageElement.remove();
        }
    }
    
    /**
     * @function updateMessage - 更新消息内容
     * @description 更新指定消息的内容
     * @param {string} messageId - 消息ID
     * @param {string} newContent - 新内容
     */
    updateMessage(messageId, newContent) {
        // 更新数组中的消息
        const message = this.messages.find(msg => msg.id === messageId);
        if (message) {
            message.content = newContent;
            
            // 更新DOM中的消息
            const messageElement = this.container.querySelector(`[data-message-id="${messageId}"]`);
            if (messageElement) {
                const textElement = messageElement.querySelector('.aisp-message-text');
                if (textElement) {
                    if (message.type === 'system') {
                        textElement.innerHTML = this.ui_formatSystemMessage(newContent);
                    } else {
                        textElement.textContent = newContent;
                    }
                }
            }
        }
    }
    
    /**
     * @function ui_showToast - 显示提示消息
     * @description 显示临时提示消息
     * @param {string} message - 提示内容
     * @param {string} type - 提示类型
     */
    ui_showToast(message, type = 'info') {
        // 创建提示元素
        const toast = util_createElement('div', {
            className: `aisp-toast aisp-toast-${type}`
        }, message);
        
        // 添加到页面
        document.body.appendChild(toast);
        
        // 显示动画
        requestAnimationFrame(() => {
            toast.classList.add('aisp-toast-visible');
        });
        
        // 自动移除
        setTimeout(() => {
            toast.classList.remove('aisp-toast-visible');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }
    
    /**
     * @function getMessages - 获取所有消息
     * @description 获取当前所有消息
     * @returns {Array} 消息数组
     */
    getMessages() {
        return [...this.messages];
    }
    
    /**
     * @function getMessage - 获取指定消息
     * @description 获取指定ID的消息
     * @param {string} messageId - 消息ID
     * @returns {Object|null} 消息对象或null
     */
    getMessage(messageId) {
        return this.messages.find(msg => msg.id === messageId) || null;
    }
}
// #endregion

console.log('AI Side Panel Chat Interface 已加载');
