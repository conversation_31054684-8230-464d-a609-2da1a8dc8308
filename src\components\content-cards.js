/**
 * @file 内容卡片系统
 * @description 提供统一的卡片组件，支持页面总结、关键要点、思维导图等内容的展示
 */

// #region 内容卡片管理器

/**
 * @class ContentCardManager - 内容卡片管理器
 * @description 管理各种类型的内容卡片
 */
class ContentCardManager {
    constructor() {
        this.cards = new Map();
        this.cardContainer = null;
        this.animationDuration = 300;
        this.init();
    }

    /**
     * @function init - 初始化卡片管理器
     */
    init() {
        this.setupCardContainer();
        console.log('[CONTENT-CARDS] 内容卡片管理器已初始化');
    }

    /**
     * @function setupCardContainer - 设置卡片容器
     */
    setupCardContainer() {
        this.cardContainer = document.getElementById('aisp-message-list');
        if (!this.cardContainer) {
            console.error('[CONTENT-CARDS] 未找到卡片容器');
            return;
        }

        // 添加卡片容器样式
        this.cardContainer.style.cssText += `
            scroll-behavior: smooth;
            padding: 8px;
            gap: 12px;
        `;
    }

    /**
     * @function createCard - 创建内容卡片
     * @param {Object} options - 卡片配置选项
     * @returns {HTMLElement} 卡片元素
     */
    createCard(options) {
        const {
            id,
            type = 'default',
            title,
            content,
            icon = '📄',
            collapsible = true,
            actions = [],
            metadata = {}
        } = options;

        // 创建卡片容器
        const card = document.createElement('div');
        card.className = `aisp-content-card aisp-card-${type}`;
        card.id = id;
        card.setAttribute('data-card-type', type);

        // 应用Apple Design风格样式
        card.style.cssText = `
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            margin-bottom: 12px;
            overflow: hidden;
            transition: all ${this.animationDuration}ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
        `;

        // 创建卡片头部
        const header = this.createCardHeader(title, icon, collapsible, actions);
        card.appendChild(header);

        // 创建卡片内容
        const contentElement = this.createCardContent(content, type);
        card.appendChild(contentElement);

        // 添加交互事件
        this.setupCardInteractions(card, collapsible);

        // 存储卡片引用
        this.cards.set(id, {
            element: card,
            type,
            collapsed: false,
            metadata
        });

        return card;
    }

    /**
     * @function createCardHeader - 创建卡片头部
     */
    createCardHeader(title, icon, collapsible, actions) {
        const header = document.createElement('div');
        header.className = 'aisp-card-header';
        header.style.cssText = `
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 20px;
            background: rgba(248, 248, 248, 0.6);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            cursor: ${collapsible ? 'pointer' : 'default'};
            user-select: none;
        `;

        // 左侧标题区域
        const titleArea = document.createElement('div');
        titleArea.style.cssText = 'display: flex; align-items: center; gap: 12px; flex: 1;';

        const iconElement = document.createElement('span');
        iconElement.className = 'aisp-card-icon';
        iconElement.textContent = icon;
        iconElement.style.cssText = 'font-size: 18px;';

        const titleElement = document.createElement('h4');
        titleElement.className = 'aisp-card-title';
        titleElement.textContent = title;
        titleElement.style.cssText = `
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #1D1D1F;
            line-height: 1.2;
        `;

        titleArea.appendChild(iconElement);
        titleArea.appendChild(titleElement);

        // 右侧操作区域
        const actionsArea = document.createElement('div');
        actionsArea.className = 'aisp-card-actions';
        actionsArea.style.cssText = 'display: flex; align-items: center; gap: 8px;';

        // 添加操作按钮
        actions.forEach(action => {
            const button = this.createActionButton(action);
            actionsArea.appendChild(button);
        });

        // 添加折叠按钮
        if (collapsible) {
            const collapseButton = document.createElement('button');
            collapseButton.className = 'aisp-card-collapse';
            collapseButton.innerHTML = '⌄';
            collapseButton.style.cssText = `
                width: 24px;
                height: 24px;
                border: none;
                background: transparent;
                color: #86868B;
                cursor: pointer;
                border-radius: 6px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                transition: all 0.2s ease;
                transform: rotate(0deg);
            `;

            collapseButton.addEventListener('mouseenter', () => {
                collapseButton.style.background = 'rgba(0, 0, 0, 0.05)';
            });

            collapseButton.addEventListener('mouseleave', () => {
                collapseButton.style.background = 'transparent';
            });

            actionsArea.appendChild(collapseButton);
        }

        header.appendChild(titleArea);
        header.appendChild(actionsArea);

        return header;
    }

    /**
     * @function createActionButton - 创建操作按钮
     */
    createActionButton(action) {
        const button = document.createElement('button');
        button.className = 'aisp-card-action-btn';
        button.title = action.title || action.label;
        button.innerHTML = action.icon || action.label;
        
        button.style.cssText = `
            width: 28px;
            height: 28px;
            border: none;
            background: rgba(0, 0, 0, 0.05);
            color: #1D1D1F;
            cursor: pointer;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            transition: all 0.2s ease;
        `;

        button.addEventListener('mouseenter', () => {
            button.style.background = 'rgba(0, 0, 0, 0.1)';
            button.style.transform = 'scale(1.05)';
        });

        button.addEventListener('mouseleave', () => {
            button.style.background = 'rgba(0, 0, 0, 0.05)';
            button.style.transform = 'scale(1)';
        });

        if (action.onClick) {
            button.addEventListener('click', (e) => {
                e.stopPropagation();
                action.onClick();
            });
        }

        return button;
    }

    /**
     * @function createCardContent - 创建卡片内容
     */
    createCardContent(content, type) {
        const contentElement = document.createElement('div');
        contentElement.className = 'aisp-card-content';
        contentElement.style.cssText = `
            padding: 20px;
            line-height: 1.6;
            color: #1D1D1F;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            transition: all ${this.animationDuration}ms ease;
        `;

        // 根据类型渲染不同的内容
        switch (type) {
            case 'summary':
                this.renderSummaryContent(contentElement, content);
                break;
            case 'keypoints':
                this.renderKeyPointsContent(contentElement, content);
                break;
            case 'mindmap':
                this.renderMindMapContent(contentElement, content);
                break;
            case 'replies':
                this.renderRepliesContent(contentElement, content);
                break;
            default:
                this.renderDefaultContent(contentElement, content);
        }

        return contentElement;
    }

    /**
     * @function renderSummaryContent - 渲染摘要内容
     */
    renderSummaryContent(container, content) {
        const summary = document.createElement('p');
        summary.style.cssText = `
            margin: 0;
            line-height: 1.7;
            color: #1D1D1F;
        `;
        summary.textContent = content;
        container.appendChild(summary);
    }

    /**
     * @function renderKeyPointsContent - 渲染关键要点内容
     */
    renderKeyPointsContent(container, content) {
        if (Array.isArray(content)) {
            const list = document.createElement('ul');
            list.style.cssText = `
                margin: 0;
                padding-left: 20px;
                list-style: none;
            `;

            content.forEach((point, index) => {
                const item = document.createElement('li');
                item.style.cssText = `
                    margin-bottom: 12px;
                    position: relative;
                    padding-left: 24px;
                    line-height: 1.6;
                `;

                // 添加自定义项目符号
                const bullet = document.createElement('span');
                bullet.style.cssText = `
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 16px;
                    height: 16px;
                    background: #007AFF;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 10px;
                    font-weight: 600;
                `;
                bullet.textContent = index + 1;

                item.appendChild(bullet);
                item.appendChild(document.createTextNode(point));
                list.appendChild(item);
            });

            container.appendChild(list);
        } else {
            this.renderDefaultContent(container, content);
        }
    }

    /**
     * @function renderMindMapContent - 渲染增强的思维导图内容
     * @description 渲染交互式思维导图，支持工具栏、导出和响应式设计
     */
    renderMindMapContent(container, content) {
        const mindmapId = `mindmap-${Date.now()}`;

        // 创建完整的思维导图界面
        const mindmapWrapper = document.createElement('div');
        mindmapWrapper.className = 'aisp-mindmap-wrapper';
        mindmapWrapper.innerHTML = `
            <div class="aisp-mindmap-toolbar">
                <div class="aisp-mindmap-controls">
                    <button class="aisp-btn aisp-btn-small" data-action="zoom-in" title="放大视图">
                        <span class="aisp-btn-icon">🔍</span>
                        <span>放大</span>
                    </button>
                    <button class="aisp-btn aisp-btn-small" data-action="zoom-out" title="缩小视图">
                        <span class="aisp-btn-icon">🔍</span>
                        <span>缩小</span>
                    </button>
                    <button class="aisp-btn aisp-btn-small" data-action="reset-view" title="重置视图">
                        <span class="aisp-btn-icon">🎯</span>
                        <span>重置</span>
                    </button>
                    <button class="aisp-btn aisp-btn-small" data-action="expand-all" title="展开所有节点">
                        <span class="aisp-btn-icon">📂</span>
                        <span>展开</span>
                    </button>
                    <button class="aisp-btn aisp-btn-small" data-action="collapse-all" title="折叠所有节点">
                        <span class="aisp-btn-icon">📁</span>
                        <span>折叠</span>
                    </button>
                </div>
                <div class="aisp-mindmap-export">
                    <button class="aisp-btn aisp-btn-small" data-action="export-png" title="导出为PNG图片">
                        <span class="aisp-btn-icon">📷</span>
                        <span>PNG</span>
                    </button>
                    <button class="aisp-btn aisp-btn-small" data-action="export-svg" title="导出为SVG矢量图">
                        <span class="aisp-btn-icon">🎨</span>
                        <span>SVG</span>
                    </button>
                    <button class="aisp-btn aisp-btn-small" data-action="fullscreen" title="全屏查看">
                        <span class="aisp-btn-icon">⛶</span>
                        <span>全屏</span>
                    </button>
                </div>
            </div>
            <div class="aisp-mindmap-container" id="${mindmapId}">
                <div class="aisp-mindmap-loading">
                    <div class="aisp-spinner"></div>
                    <span>正在生成思维导图...</span>
                </div>
            </div>
            <div class="aisp-mindmap-info">
                <span class="aisp-mindmap-stats" id="mindmap-stats-${mindmapId}">
                    节点: 0 | 连接: 0
                </span>
                <span class="aisp-mindmap-zoom" id="mindmap-zoom-${mindmapId}">
                    缩放: 100%
                </span>
            </div>
        `;

        // 添加样式
        mindmapWrapper.style.cssText = `
            width: 100%;
            border: 1px solid var(--aisp-separator-non-opaque);
            border-radius: var(--aisp-corner-radius-large);
            overflow: hidden;
            background: var(--aisp-background-tertiary);
        `;

        container.appendChild(mindmapWrapper);

        // 添加事件监听器
        this._addMindMapEventListeners(mindmapWrapper, mindmapId);

        // 延迟渲染思维导图
        setTimeout(() => {
            this._renderEnhancedMindMap(mindmapId, content);
        }, 100);
    }

    /**
     * @function renderRepliesContent - 渲染回复建议内容
     */
    renderRepliesContent(container, content) {
        if (Array.isArray(content)) {
            content.forEach((reply, index) => {
                const replyCard = document.createElement('div');
                replyCard.style.cssText = `
                    background: rgba(0, 122, 255, 0.05);
                    border: 1px solid rgba(0, 122, 255, 0.2);
                    border-radius: 8px;
                    padding: 12px;
                    margin-bottom: 8px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                `;

                replyCard.addEventListener('mouseenter', () => {
                    replyCard.style.background = 'rgba(0, 122, 255, 0.1)';
                });

                replyCard.addEventListener('mouseleave', () => {
                    replyCard.style.background = 'rgba(0, 122, 255, 0.05)';
                });

                replyCard.addEventListener('click', () => {
                    this.copyToClipboard(reply);
                    this.showToast('回复已复制到剪贴板');
                });

                replyCard.textContent = reply;
                container.appendChild(replyCard);
            });
        } else {
            this.renderDefaultContent(container, content);
        }
    }

    /**
     * @function renderDefaultContent - 渲染默认内容
     */
    renderDefaultContent(container, content) {
        if (typeof content === 'string') {
            container.innerHTML = content;
        } else if (content instanceof HTMLElement) {
            container.appendChild(content);
        } else {
            container.textContent = String(content);
        }
    }

    /**
     * @function setupCardInteractions - 设置卡片交互
     */
    setupCardInteractions(card, collapsible) {
        if (!collapsible) return;

        const header = card.querySelector('.aisp-card-header');
        const content = card.querySelector('.aisp-card-content');
        const collapseButton = card.querySelector('.aisp-card-collapse');

        if (header && content) {
            header.addEventListener('click', () => {
                this.toggleCard(card.id);
            });
        }
    }

    /**
     * @function toggleCard - 切换卡片展开/收起状态
     */
    toggleCard(cardId) {
        const cardData = this.cards.get(cardId);
        if (!cardData) return;

        const { element } = cardData;
        const content = element.querySelector('.aisp-card-content');
        const collapseButton = element.querySelector('.aisp-card-collapse');

        if (cardData.collapsed) {
            // 展开
            content.style.maxHeight = '400px';
            content.style.padding = '20px';
            collapseButton.style.transform = 'rotate(0deg)';
            cardData.collapsed = false;
        } else {
            // 收起
            content.style.maxHeight = '0';
            content.style.padding = '0 20px';
            collapseButton.style.transform = 'rotate(-90deg)';
            cardData.collapsed = true;
        }
    }

    /**
     * @function addCard - 添加卡片到容器
     */
    addCard(cardOptions) {
        const card = this.createCard(cardOptions);
        
        // 添加进入动画
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        this.cardContainer.appendChild(card);
        
        // 触发动画
        requestAnimationFrame(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        });

        // 滚动到新卡片
        setTimeout(() => {
            card.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }, this.animationDuration);

        return card;
    }

    /**
     * @function removeCard - 移除卡片
     */
    removeCard(cardId) {
        const cardData = this.cards.get(cardId);
        if (!cardData) return;

        const { element } = cardData;
        
        // 添加退出动画
        element.style.opacity = '0';
        element.style.transform = 'translateY(-20px)';
        
        setTimeout(() => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
            this.cards.delete(cardId);
        }, this.animationDuration);
    }

    /**
     * @function copyToClipboard - 复制到剪贴板
     */
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
        } catch (error) {
            console.error('复制失败:', error);
        }
    }

    /**
     * @function showToast - 显示提示消息
     */
    showToast(message) {
        // 简单的提示实现
        const toast = document.createElement('div');
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 10000;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        document.body.appendChild(toast);
        
        requestAnimationFrame(() => {
            toast.style.opacity = '1';
        });
        
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 2000);
    }
}

// 创建全局卡片管理器实例
let contentCardManager = null;

/**
 * @function aisp_initializeContentCards - 初始化内容卡片系统
 */
function aisp_initializeContentCards() {
    if (!contentCardManager) {
        contentCardManager = new ContentCardManager();
    }
}

/**
 * @function aisp_addContentCard - 添加内容卡片
 */
function aisp_addContentCard(options) {
    if (contentCardManager) {
        return contentCardManager.addCard(options);
    }
}

/**
 * @function aisp_removeContentCard - 移除内容卡片
 */
function aisp_removeContentCard(cardId) {
    if (contentCardManager) {
        contentCardManager.removeCard(cardId);
    }
}

// #endregion

console.log('内容卡片系统已加载');

// 添加内容卡片的CSS样式
const cardStyles = document.createElement('style');
cardStyles.textContent = `
/* 内容卡片样式 */
.aisp-content-card {
    position: relative;
}

.aisp-content-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.aisp-card-content::-webkit-scrollbar {
    width: 4px;
}

.aisp-card-content::-webkit-scrollbar-track {
    background: transparent;
}

.aisp-card-content::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
}

.aisp-card-content::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .aisp-content-card {
        background: rgba(28, 28, 30, 0.8);
        border-color: rgba(255, 255, 255, 0.1);
        color: #F5F5F7;
    }

    .aisp-card-header {
        background: rgba(44, 44, 46, 0.6);
        border-bottom-color: rgba(255, 255, 255, 0.05);
    }

    .aisp-card-title {
        color: #F5F5F7;
    }

    .aisp-card-content {
        color: #F5F5F7;
    }
}

/* 动画效果 */
@keyframes cardSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.aisp-content-card {
    animation: cardSlideIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
`;

document.head.appendChild(cardStyles);

// 扩展 aisp_ContentCardsRenderer 类的原型，添加思维导图增强功能
if (typeof aisp_ContentCardsRenderer !== 'undefined') {
    /**
     * @function _renderEnhancedMindMap - 渲染增强的思维导图
     * @description 渲染具有高级功能的思维导图
     * @param {string} mindmapId - 思维导图容器ID
     * @param {Object} content - 思维导图数据
     * @private
     */
    aisp_ContentCardsRenderer.prototype._renderEnhancedMindMap = function(mindmapId, content) {
        const container = document.getElementById(mindmapId);
        if (!container) return;

        try {
            // 清除加载状态
            container.innerHTML = '';

            // 生成思维导图数据
            const mindmapData = this._generateMindMapFromAnalysis(content);

            // 创建SVG容器
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.setAttribute('width', '100%');
            svg.setAttribute('height', '300');
            svg.setAttribute('viewBox', '0 0 450 300');
            svg.style.cssText = `
                background: var(--aisp-background-primary);
                border-radius: var(--aisp-corner-radius-medium);
            `;

            // 渲染思维导图
            this._renderMindMapNodes(svg, mindmapData);

            container.appendChild(svg);

            // 更新统计信息
            this._updateMindMapStats(mindmapId, mindmapData);

            // 添加交互功能
            this._addMindMapInteractions(svg, mindmapData);

        } catch (error) {
            console.error('渲染思维导图失败:', error);
            container.innerHTML = `
                <div class="aisp-mindmap-error">
                    <span>思维导图生成失败</span>
                    <button class="aisp-btn aisp-btn-small" onclick="location.reload()">重试</button>
                </div>
            `;
        }
    };

    /**
     * @function _generateMindMapFromAnalysis - 从分析结果生成思维导图数据
     * @description 将AI分析结果转换为思维导图数据结构
     * @param {Object} analysisResult - 分析结果
     * @returns {Object} 思维导图数据
     * @private
     */
    aisp_ContentCardsRenderer.prototype._generateMindMapFromAnalysis = function(analysisResult) {
        const nodes = [];
        const links = [];
        let nodeId = 0;

        // 根节点
        const rootNode = {
            id: nodeId++,
            label: '页面分析',
            x: 225,
            y: 150,
            level: 0,
            type: 'root',
            expanded: true
        };
        nodes.push(rootNode);

        // 摘要节点
        if (analysisResult.summary) {
            const summaryNode = {
                id: nodeId++,
                label: '摘要',
                x: 100,
                y: 80,
                level: 1,
                type: 'summary',
                content: analysisResult.summary,
                expanded: true
            };
            nodes.push(summaryNode);
            links.push({ source: rootNode.id, target: summaryNode.id });
        }

        // 关键要点节点
        if (analysisResult.keyPoints && analysisResult.keyPoints.length > 0) {
            const keyPointsNode = {
                id: nodeId++,
                label: '关键要点',
                x: 350,
                y: 80,
                level: 1,
                type: 'keypoints',
                expanded: true
            };
            nodes.push(keyPointsNode);
            links.push({ source: rootNode.id, target: keyPointsNode.id });

            // 添加具体要点
            analysisResult.keyPoints.slice(0, 5).forEach((point, index) => {
                const pointNode = {
                    id: nodeId++,
                    label: `要点 ${index + 1}`,
                    x: 350 + (index % 2 === 0 ? -50 : 50),
                    y: 40 + index * 25,
                    level: 2,
                    type: 'point',
                    content: point,
                    expanded: false
                };
                nodes.push(pointNode);
                links.push({ source: keyPointsNode.id, target: pointNode.id });
            });
        }

        // 情感分析节点
        if (analysisResult.sentiment) {
            const sentimentNode = {
                id: nodeId++,
                label: '情感分析',
                x: 225,
                y: 220,
                level: 1,
                type: 'sentiment',
                content: analysisResult.sentiment,
                expanded: true
            };
            nodes.push(sentimentNode);
            links.push({ source: rootNode.id, target: sentimentNode.id });
        }

        return { nodes, links };
    };

    /**
     * @function _addMindMapEventListeners - 添加思维导图事件监听器
     * @description 为思维导图工具栏添加事件监听器
     * @param {HTMLElement} wrapper - 思维导图包装器
     * @param {string} mindmapId - 思维导图ID
     * @private
     */
    aisp_ContentCardsRenderer.prototype._addMindMapEventListeners = function(wrapper, mindmapId) {
        const toolbar = wrapper.querySelector('.aisp-mindmap-toolbar');
        if (!toolbar) return;

        toolbar.addEventListener('click', (event) => {
            const button = event.target.closest('button[data-action]');
            if (!button) return;

            const action = button.dataset.action;
            this._handleMindMapAction(action, mindmapId);
        });
    };

    /**
     * @function _handleMindMapAction - 处理思维导图操作
     * @description 处理工具栏按钮点击事件
     * @param {string} action - 操作类型
     * @param {string} mindmapId - 思维导图ID
     * @private
     */
    aisp_ContentCardsRenderer.prototype._handleMindMapAction = function(action, mindmapId) {
        const container = document.getElementById(mindmapId);
        const svg = container?.querySelector('svg');

        if (!svg) return;

        switch (action) {
            case 'zoom-in':
                this._zoomMindMap(svg, 1.2);
                break;
            case 'zoom-out':
                this._zoomMindMap(svg, 0.8);
                break;
            case 'reset-view':
                this._resetMindMapView(svg);
                break;
            case 'expand-all':
                this._expandAllNodes(svg);
                break;
            case 'collapse-all':
                this._collapseAllNodes(svg);
                break;
            case 'export-png':
                this._exportMindMapAsPNG(svg, mindmapId);
                break;
            case 'export-svg':
                this._exportMindMapAsSVG(svg, mindmapId);
                break;
            case 'fullscreen':
                this._showFullscreenMindMap(mindmapId);
                break;
        }
    };
}
