/**
 * AI Side Panel 弹窗样式 - Apple Design System
 * 采用Apple Design System的设计原则
 */

/* #region 基础样式重置 - Apple 风格 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--aisp-font-family);
    font-size: var(--aisp-font-size-callout);
    line-height: 1.47059;
    color: var(--aisp-label-primary);
    background: var(--aisp-background-primary);
    width: 320px;
    min-height: 420px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}
/* #endregion */

/* #region 弹窗容器 - Apple 风格 */
.aisp-popup-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 0;
    background: var(--aisp-background-primary);
    position: relative;
}

/* 毛玻璃背景效果 */
.aisp-popup-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--aisp-blur-background);
    -webkit-backdrop-filter: var(--aisp-blur-material-thin);
    backdrop-filter: var(--aisp-blur-material-thin);
    z-index: -1;
}
/* #endregion */

/* #region 头部样式 - Apple 风格 */
.aisp-popup-header {
    background: var(--aisp-background-secondary);
    color: var(--aisp-label-primary);
    padding: var(--aisp-spacing-5);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--aisp-separator-non-opaque);
    -webkit-backdrop-filter: var(--aisp-blur-material);
    backdrop-filter: var(--aisp-blur-material);
}

.aisp-logo {
    display: flex;
    align-items: center;
    gap: var(--aisp-spacing-3);
}

.aisp-logo-img {
    width: 28px;
    height: 28px;
    border-radius: var(--aisp-corner-radius-small);
    box-shadow: var(--aisp-shadow-1);
}

.aisp-title {
    font-size: var(--aisp-font-size-headline);
    font-weight: var(--aisp-font-weight-semibold);
    margin: 0;
    color: var(--aisp-label-primary);
    letter-spacing: -0.02em;
}

.aisp-version {
    font-size: var(--aisp-font-size-caption1);
    font-weight: var(--aisp-font-weight-medium);
    color: var(--aisp-label-secondary);
    background: var(--aisp-fill-secondary);
    padding: var(--aisp-spacing-1) var(--aisp-spacing-3);
    border-radius: var(--aisp-corner-radius-continuous);
    border: 1px solid var(--aisp-separator-non-opaque);
}
/* #endregion */

/* #region 主要内容区 - Apple 风格 */
.aisp-popup-main {
    flex: 1;
    padding: var(--aisp-spacing-5);
    display: flex;
    flex-direction: column;
    gap: var(--aisp-spacing-5);
    background: var(--aisp-background-primary);
}
/* #endregion */

/* #region 快速操作按钮 - Apple 风格 */
.aisp-quick-actions {
    display: flex;
    flex-direction: column;
    gap: var(--aisp-spacing-3);
}

.aisp-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--aisp-spacing-3);
    padding: var(--aisp-spacing-4) var(--aisp-spacing-5);
    border: none;
    border-radius: var(--aisp-corner-radius-large);
    font-family: var(--aisp-font-family);
    font-size: var(--aisp-font-size-callout);
    font-weight: var(--aisp-font-weight-medium);
    cursor: pointer;
    transition: all var(--aisp-animation-duration-short) var(--aisp-animation-easing);
    text-decoration: none;
    position: relative;
    overflow: hidden;
    -webkit-user-select: none;
    user-select: none;
}

.aisp-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--aisp-shadow-2);
}

.aisp-btn:active {
    transform: scale(0.96);
}

.aisp-btn-primary {
    background: var(--aisp-system-blue);
    color: white;
    box-shadow: var(--aisp-shadow-1);
}

.aisp-btn-primary:hover {
    background: var(--aisp-system-blue-hover);
}

.aisp-btn-secondary {
    background: var(--aisp-fill-secondary);
    color: var(--aisp-label-primary);
    border: 1px solid var(--aisp-separator-non-opaque);
    -webkit-backdrop-filter: var(--aisp-blur-material-thin);
    backdrop-filter: var(--aisp-blur-material-thin);
}

.aisp-btn-secondary:hover {
    background: var(--aisp-fill-primary);
    border-color: var(--aisp-separator-opaque);
}

.aisp-btn-icon {
    font-size: var(--aisp-font-size-headline);
    line-height: 1;
}
/* #endregion */

/* #region 状态信息 - Apple 风格 */
.aisp-status {
    background: var(--aisp-background-tertiary);
    border-radius: var(--aisp-corner-radius-large);
    padding: var(--aisp-spacing-4);
    border: 1px solid var(--aisp-separator-non-opaque);
    -webkit-backdrop-filter: var(--aisp-blur-material-thin);
    backdrop-filter: var(--aisp-blur-material-thin);
}

.aisp-status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--aisp-spacing-2) 0;
}

.aisp-status-item:not(:last-child) {
    border-bottom: 1px solid var(--aisp-separator-non-opaque);
    margin-bottom: var(--aisp-spacing-2);
    padding-bottom: var(--aisp-spacing-3);
}

.aisp-status-label {
    font-weight: var(--aisp-font-weight-medium);
    color: var(--aisp-label-primary);
    font-size: var(--aisp-font-size-footnote);
}

.aisp-status-value {
    font-size: var(--aisp-font-size-caption1);
    padding: var(--aisp-spacing-1) var(--aisp-spacing-3);
    border-radius: var(--aisp-corner-radius-continuous);
    font-weight: var(--aisp-font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.aisp-status-value.status-ok {
    background: rgba(52, 199, 89, 0.15);
    color: var(--aisp-system-green);
    border: 1px solid rgba(52, 199, 89, 0.3);
}

.aisp-status-value.status-warning {
    background: rgba(255, 204, 0, 0.15);
    color: var(--aisp-system-yellow);
    border: 1px solid rgba(255, 204, 0, 0.3);
}

.aisp-status-value.status-disabled {
    background: rgba(255, 59, 48, 0.15);
    color: var(--aisp-system-red);
    border: 1px solid rgba(255, 59, 48, 0.3);
}
/* #endregion */

/* #region 快速设置 - Apple 风格 */
.aisp-quick-settings {
    display: flex;
    flex-direction: column;
    gap: var(--aisp-spacing-4);
}

.aisp-setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--aisp-spacing-4);
    padding: var(--aisp-spacing-2) 0;
}

.aisp-setting-label {
    display: flex;
    align-items: center;
    gap: var(--aisp-spacing-3);
    cursor: pointer;
    font-size: var(--aisp-font-size-callout);
    color: var(--aisp-label-primary);
    flex: 1;
    font-weight: var(--aisp-font-weight-regular);
}

/* Apple 风格复选框 */
.aisp-checkbox {
    display: none;
}

.aisp-checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--aisp-separator-opaque);
    border-radius: var(--aisp-corner-radius-small);
    position: relative;
    transition: all var(--aisp-animation-duration-short) var(--aisp-animation-easing);
    flex-shrink: 0;
    background: var(--aisp-background-tertiary);
}

.aisp-checkbox:checked + .aisp-checkmark {
    background: var(--aisp-system-blue);
    border-color: var(--aisp-system-blue);
    transform: scale(1.05);
}

.aisp-checkbox:checked + .aisp-checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: var(--aisp-font-size-footnote);
    font-weight: var(--aisp-font-weight-semibold);
}

.aisp-checkmark:hover {
    border-color: var(--aisp-system-blue);
    background: var(--aisp-system-blue-light);
}

/* Apple 风格选择框 */
.aisp-select {
    padding: var(--aisp-spacing-2) var(--aisp-spacing-4);
    border: 1px solid var(--aisp-separator-opaque);
    border-radius: var(--aisp-corner-radius-medium);
    background: var(--aisp-background-tertiary);
    font-family: var(--aisp-font-family);
    font-size: var(--aisp-font-size-footnote);
    font-weight: var(--aisp-font-weight-medium);
    color: var(--aisp-label-primary);
    cursor: pointer;
    min-width: 100px;
    -webkit-appearance: none;
    appearance: none;
    transition: all var(--aisp-animation-duration-short) var(--aisp-animation-easing);
}

.aisp-select:focus {
    outline: none;
    border-color: var(--aisp-system-blue);
    box-shadow: 0 0 0 2px var(--aisp-system-blue-light);
    background: var(--aisp-background-primary);
}

.aisp-select:hover {
    border-color: var(--aisp-system-blue);
}
/* #endregion */

/* #region 底部链接 - Apple 风格 */
.aisp-popup-footer {
    padding: var(--aisp-spacing-4) var(--aisp-spacing-5);
    border-top: 1px solid var(--aisp-separator-non-opaque);
    background: var(--aisp-background-secondary);
    -webkit-backdrop-filter: var(--aisp-blur-material);
    backdrop-filter: var(--aisp-blur-material);
}

.aisp-footer-links {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.aisp-link {
    color: var(--aisp-system-blue);
    text-decoration: none;
    font-size: var(--aisp-font-size-footnote);
    font-weight: var(--aisp-font-weight-medium);
    padding: var(--aisp-spacing-2) var(--aisp-spacing-3);
    border-radius: var(--aisp-corner-radius-small);
    transition: all var(--aisp-animation-duration-short) var(--aisp-animation-easing);
}

.aisp-link:hover {
    color: var(--aisp-system-blue-hover);
    background: var(--aisp-system-blue-light);
    text-decoration: none;
}

.aisp-link:active {
    transform: scale(0.95);
}
/* #endregion */

/* #region 消息提示 - Apple 风格 */
.aisp-message {
    position: fixed;
    top: var(--aisp-spacing-5);
    left: 50%;
    transform: translateX(-50%);
    padding: var(--aisp-spacing-3) var(--aisp-spacing-5);
    border-radius: var(--aisp-corner-radius-large);
    font-size: var(--aisp-font-size-footnote);
    font-weight: var(--aisp-font-weight-medium);
    z-index: 1000;
    box-shadow: var(--aisp-shadow-3);
    -webkit-backdrop-filter: var(--aisp-blur-material);
    backdrop-filter: var(--aisp-blur-material);
    animation: aisp-toast-appear var(--aisp-animation-duration-medium) var(--aisp-animation-easing);
    max-width: 280px;
    text-align: center;
}

.aisp-message-success {
    background: rgba(52, 199, 89, 0.9);
    color: white;
    border: 1px solid var(--aisp-system-green);
}

.aisp-message-error {
    background: rgba(255, 59, 48, 0.9);
    color: white;
    border: 1px solid var(--aisp-system-red);
}

.aisp-message-warning {
    background: rgba(255, 204, 0, 0.9);
    color: var(--aisp-label-primary);
    border: 1px solid var(--aisp-system-yellow);
}

.aisp-message-info {
    background: rgba(0, 122, 255, 0.9);
    color: white;
    border: 1px solid var(--aisp-system-blue);
}

@keyframes aisp-toast-appear {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-10px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0) scale(1);
    }
}
/* #endregion */

/* #region 响应式设计 - Apple 风格弹窗适配 */
@media (max-width: 320px) {
    body {
        width: 100%;
        min-height: 380px;
    }

    .aisp-popup-header {
        padding: var(--aisp-spacing-4);
    }

    .aisp-popup-main {
        padding: var(--aisp-spacing-4);
        gap: var(--aisp-spacing-4);
    }

    .aisp-btn {
        padding: var(--aisp-spacing-3) var(--aisp-spacing-4);
        font-size: var(--aisp-font-size-footnote);
    }

    .aisp-title {
        font-size: var(--aisp-font-size-callout);
    }

    .aisp-logo-img {
        width: 24px;
        height: 24px;
    }

    .aisp-status {
        padding: var(--aisp-spacing-3);
    }

    .aisp-quick-settings {
        gap: var(--aisp-spacing-3);
    }

    .aisp-popup-footer {
        padding: var(--aisp-spacing-3) var(--aisp-spacing-4);
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    .aisp-message {
        animation: none;
    }

    .aisp-btn:hover {
        transform: none;
    }

    .aisp-btn:active {
        transform: none;
    }

    .aisp-checkmark {
        transition: none;
    }

    * {
        transition-duration: 0.01ms !important;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .aisp-btn {
        border: 2px solid currentColor;
    }

    .aisp-checkmark {
        border-width: 3px;
    }

    .aisp-select {
        border-width: 2px;
    }

    .aisp-status-value {
        border-width: 2px;
    }
}

/* 深色主题特殊处理 */
@media (prefers-color-scheme: dark) {
    .aisp-message-warning {
        color: var(--aisp-label-primary);
    }
}
/* #endregion */
