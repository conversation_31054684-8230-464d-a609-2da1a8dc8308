/**
 * AI Side Panel 内容注入样式 - Apple Design System
 * 用于content script注入到网页中的元素样式，采用Apple Design风格
 */

/* #region 模板弹窗样式 - Apple 风格 */
.aisp-template-popup {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    z-index: 999999;
    max-width: 320px;
    min-width: 280px;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif;
    font-size: 16px;
    line-height: 1.47059;
    color: #1d1d1f;
    overflow: hidden;
}

.aisp-template-header {
    padding: 16px 20px;
    background: rgba(0, 122, 255, 0.95);
    color: white;
    font-weight: 600;
    font-size: 15px;
    text-align: center;
    letter-spacing: -0.01em;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
}

.aisp-template-list {
    max-height: 320px;
    overflow-y: auto;
    padding: 12px 0;
}

/* Apple 风格滚动条 */
.aisp-template-list::-webkit-scrollbar {
    width: 8px;
}

.aisp-template-list::-webkit-scrollbar-track {
    background: transparent;
}

.aisp-template-list::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    border: 2px solid transparent;
    background-clip: content-box;
}

.aisp-template-list::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
    background-clip: content-box;
}

.aisp-template-item {
    padding: 16px 20px;
    cursor: pointer;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    transition: all 0.2s cubic-bezier(0.25, 0.1, 0.25, 1);
    position: relative;
}

.aisp-template-item:hover {
    background: rgba(0, 122, 255, 0.08);
    transform: translateX(4px);
}

.aisp-template-item:active {
    background: rgba(0, 122, 255, 0.15);
    transform: scale(0.98);
}

.aisp-template-item:last-child {
    border-bottom: none;
}

.aisp-template-title {
    font-weight: 600;
    color: #1d1d1f;
    margin-bottom: 6px;
    font-size: 15px;
    letter-spacing: -0.01em;
}

.aisp-template-preview {
    color: #86868b;
    font-size: 13px;
    line-height: 1.47059;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 400;
}

.aisp-template-footer {
    padding: 12px 20px;
    background: rgba(248, 248, 248, 0.95);
    border-top: 1px solid rgba(0, 0, 0, 0.08);
    text-align: center;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
}

.aisp-template-footer small {
    color: #86868b;
    font-size: 12px;
    font-weight: 500;
    letter-spacing: 0.01em;
}
/* #endregion */

/* #region 输入框高亮样式 - Apple 风格 */
.aisp-input-focused {
    outline: 2px solid #007AFF !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.15) !important;
    border-radius: 8px !important;
}

.aisp-input-with-template {
    position: relative;
}

.aisp-input-indicator {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    color: white;
    font-weight: 600;
    z-index: 999998;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
    animation: aisp-pulse 2s ease-in-out infinite;
    border: 2px solid white;
}

@keyframes aisp-pulse {
    0% {
        transform: scale(1);
        opacity: 1;
        box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
    }
    50% {
        transform: scale(1.1);
        opacity: 0.9;
        box-shadow: 0 4px 16px rgba(0, 122, 255, 0.4);
    }
    100% {
        transform: scale(1);
        opacity: 1;
        box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
    }
}
/* #endregion */

/* #region 智能预测提示 - Apple 风格 */
.aisp-prediction-tooltip {
    position: absolute;
    background: rgba(28, 28, 30, 0.95);
    color: white;
    padding: 8px 12px;
    border-radius: 12px;
    font-size: 13px;
    font-weight: 500;
    z-index: 999997;
    pointer-events: none;
    white-space: nowrap;
    max-width: 240px;
    overflow: hidden;
    text-overflow: ellipsis;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.aisp-prediction-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -6px;
    border-width: 6px;
    border-style: solid;
    border-color: rgba(28, 28, 30, 0.95) transparent transparent transparent;
}

.aisp-prediction-highlight {
    background: rgba(0, 122, 255, 0.2) !important;
    border-radius: 4px;
    padding: 2px 4px;
    margin: 0 1px;
    border: 1px solid rgba(0, 122, 255, 0.3);
}
/* #endregion */

/* #region 快捷键提示 - Apple 风格 */
.aisp-shortcut-hint {
    position: fixed;
    bottom: 24px;
    right: 24px;
    background: rgba(28, 28, 30, 0.95);
    color: white;
    padding: 12px 16px;
    border-radius: 16px;
    font-size: 13px;
    font-weight: 500;
    z-index: 999996;
    animation: aisp-fadeInUp 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);
    pointer-events: none;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    max-width: 280px;
}

@keyframes aisp-fadeInUp {
    from {
        opacity: 0;
        transform: translateY(16px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.aisp-shortcut-key {
    background: rgba(255, 255, 255, 0.25);
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-weight: 600;
    font-size: 12px;
    margin: 0 3px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-family: -apple-system, BlinkMacSystemFont, 'SF Mono', Monaco, 'Cascadia Code', monospace;
}
/* #endregion */

/* #region 加载状态 - Apple 风格 */
.aisp-content-loading {
    position: fixed;
    top: 24px;
    right: 24px;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 16px;
    padding: 16px 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    z-index: 999995;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    font-weight: 500;
    color: #1d1d1f;
    animation: aisp-slideInRight 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);
    max-width: 280px;
}

@keyframes aisp-slideInRight {
    from {
        opacity: 0;
        transform: translateX(24px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

.aisp-content-loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(0, 122, 255, 0.2);
    border-top: 2px solid #007AFF;
    border-radius: 50%;
    animation: aisp-spin 1s linear infinite;
}

@keyframes aisp-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
/* #endregion */

/* #region 错误提示 - Apple 风格 */
.aisp-error-toast {
    position: fixed;
    top: 24px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(255, 59, 48, 0.95);
    color: white;
    padding: 16px 24px;
    border-radius: 16px;
    font-size: 15px;
    font-weight: 500;
    z-index: 999994;
    animation: aisp-slideInDown 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);
    max-width: 360px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(255, 59, 48, 0.3);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

@keyframes aisp-slideInDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-24px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0) scale(1);
    }
}

.aisp-success-toast {
    background: rgba(52, 199, 89, 0.95);
    box-shadow: 0 8px 32px rgba(52, 199, 89, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.aisp-warning-toast {
    background: rgba(255, 204, 0, 0.95);
    color: #1d1d1f;
    box-shadow: 0 8px 32px rgba(255, 204, 0, 0.3);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.aisp-info-toast {
    background: rgba(0, 122, 255, 0.95);
    color: white;
    box-shadow: 0 8px 32px rgba(0, 122, 255, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}
/* #endregion */

/* #region 响应式适配 - Apple 风格 */
@media (max-width: 768px) {
    .aisp-template-popup {
        max-width: 300px;
        min-width: 260px;
        border-radius: 12px;
    }

    .aisp-shortcut-hint {
        bottom: 16px;
        right: 16px;
        font-size: 12px;
        padding: 10px 14px;
        border-radius: 12px;
    }

    .aisp-content-loading {
        top: 16px;
        right: 16px;
        padding: 12px 16px;
        font-size: 13px;
        border-radius: 12px;
    }

    .aisp-error-toast {
        top: 16px;
        max-width: 320px;
        padding: 14px 20px;
        font-size: 14px;
        border-radius: 12px;
    }
}

@media (max-width: 480px) {
    .aisp-template-popup {
        max-width: 280px;
        min-width: 240px;
        border-radius: 12px;
    }

    .aisp-template-item {
        padding: 14px 16px;
    }

    .aisp-template-header {
        padding: 14px 16px;
        font-size: 14px;
    }

    .aisp-template-footer {
        padding: 10px 16px;
    }

    .aisp-shortcut-hint {
        bottom: 12px;
        right: 12px;
        font-size: 11px;
        padding: 8px 12px;
        max-width: 240px;
    }

    .aisp-content-loading {
        top: 12px;
        right: 12px;
        padding: 10px 14px;
        font-size: 12px;
        max-width: 240px;
    }

    .aisp-error-toast {
        top: 12px;
        max-width: 280px;
        padding: 12px 16px;
        font-size: 13px;
    }
}
/* #endregion */

/* #region 防止样式冲突 - Apple 风格 */
.aisp-template-popup * {
    box-sizing: border-box !important;
    margin: 0 !important;
    padding: 0 !important;
}

.aisp-template-popup,
.aisp-template-popup *,
.aisp-shortcut-hint,
.aisp-shortcut-hint *,
.aisp-content-loading,
.aisp-content-loading *,
.aisp-error-toast,
.aisp-error-toast *,
.aisp-success-toast,
.aisp-success-toast *,
.aisp-warning-toast,
.aisp-warning-toast *,
.aisp-info-toast,
.aisp-info-toast * {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif !important;
}

/* 确保所有注入元素不受页面样式影响 */
.aisp-template-popup {
    all: initial;
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    z-index: 999999;
    max-width: 320px;
    min-width: 280px;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif;
    font-size: 16px;
    line-height: 1.47059;
    color: #1d1d1f;
    overflow: hidden;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
    .aisp-template-popup {
        background: rgba(28, 28, 30, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: #f2f2f7;
    }

    .aisp-template-header {
        background: rgba(0, 122, 255, 0.95);
    }

    .aisp-template-footer {
        background: rgba(44, 44, 46, 0.95);
    }

    .aisp-template-item:hover {
        background: rgba(0, 122, 255, 0.15);
    }

    .aisp-content-loading {
        background: rgba(28, 28, 30, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: #f2f2f7;
    }

    .aisp-prediction-tooltip {
        background: rgba(44, 44, 46, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    .aisp-template-popup,
    .aisp-shortcut-hint,
    .aisp-content-loading,
    .aisp-error-toast,
    .aisp-success-toast,
    .aisp-warning-toast,
    .aisp-info-toast {
        animation: none !important;
    }

    .aisp-template-item {
        transition: none !important;
    }

    .aisp-input-indicator {
        animation: none !important;
    }

    .aisp-content-loading-spinner {
        animation: none !important;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .aisp-template-popup {
        border-width: 2px;
        box-shadow: 0 0 0 1px #000;
    }

    .aisp-template-item {
        border-bottom-width: 2px;
    }

    .aisp-input-focused {
        outline-width: 3px !important;
    }
}
/* #endregion */
