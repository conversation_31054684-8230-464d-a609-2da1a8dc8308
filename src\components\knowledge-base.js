/**
 * @file AI Side Panel 知识库管理组件
 * @description Google Drive知识库的用户界面组件，提供同步、管理、查看等功能
 */

// #region 知识库管理组件类
/**
 * @class KnowledgeBase - 知识库管理组件
 * @description 管理Google Drive知识库的显示和交互
 */
class KnowledgeBase {
    /**
     * @function constructor - 构造函数
     * @param {string|HTMLElement} container - 容器元素或选择器
     * @param {Object} options - 配置选项
     */
    constructor(container, options = {}) {
        this.container = typeof container === 'string' 
            ? document.querySelector(container) 
            : container;
            
        if (!this.container) {
            throw new Error('知识库容器元素未找到');
        }
        
        this.options = {
            showSyncButton: true,
            showAuthButton: true,
            showStorageInfo: true,
            autoSync: false,
            syncInterval: 300000, // 5分钟
            language: 'zh_CN',
            onSyncComplete: null,
            onAuthComplete: null,
            onError: null,
            ...options
        };
        
        this.googleDriveAPI = null;
        this.isInitialized = false;
        this.isSyncing = false;
        this.syncTimer = null;
        this.authStatus = null;
        this.syncStatus = null;
        
        this.initialize();
    }
    
    /**
     * @function initialize - 初始化组件
     * @description 初始化知识库管理组件
     */
    async initialize() {
        try {
            // 获取Google Drive API
            if (typeof getGoogleDriveAPI === 'function') {
                this.googleDriveAPI = getGoogleDriveAPI();
                await this.googleDriveAPI.initialize();
            } else {
                console.warn('⚠️ Google Drive API不可用');
                this.showError('Google Drive API不可用');
                return;
            }
            
            // 初始化容器
            this.setupContainer();
            
            // 添加样式
            this.addStyles();
            
            // 绑定事件
            this.bindEvents();
            
            // 更新状态
            await this.updateStatus();
            
            // 设置自动同步
            if (this.options.autoSync) {
                this.startAutoSync();
            }
            
            this.isInitialized = true;
            console.log('✅ 知识库管理组件初始化完成');
            
        } catch (error) {
            console.error('❌ 知识库管理组件初始化失败:', error);
            this.showError('初始化失败: ' + error.message);
        }
    }
    
    /**
     * @function setupContainer - 设置容器
     * @description 设置容器的基本结构和样式
     */
    setupContainer() {
        this.container.className = 'knowledge-base-container';
        this.container.innerHTML = `
            <div class="knowledge-base-header">
                <h4 class="knowledge-base-title">☁️ Google Drive 知识库</h4>
                <div class="knowledge-base-actions">
                    <button class="kb-auth-btn" title="认证Google Drive">🔐</button>
                    <button class="kb-sync-btn" title="同步知识库">🔄</button>
                    <button class="kb-settings-btn" title="设置">⚙️</button>
                </div>
            </div>
            <div class="knowledge-base-content">
                <div class="kb-status-section">
                    <div class="kb-auth-status">
                        <div class="kb-status-item">
                            <span class="kb-status-label">认证状态:</span>
                            <span class="kb-status-value kb-auth-value">检查中...</span>
                        </div>
                        <div class="kb-status-item">
                            <span class="kb-status-label">最后同步:</span>
                            <span class="kb-status-value kb-sync-value">从未同步</span>
                        </div>
                    </div>
                    <div class="kb-storage-info" style="display: none;">
                        <div class="kb-storage-bar">
                            <div class="kb-storage-used"></div>
                        </div>
                        <div class="kb-storage-text">
                            <span class="kb-storage-usage">0 B</span> / 
                            <span class="kb-storage-total">0 B</span>
                            (<span class="kb-storage-percentage">0%</span>)
                        </div>
                    </div>
                </div>
                <div class="kb-sync-section">
                    <div class="kb-sync-progress" style="display: none;">
                        <div class="kb-progress-bar">
                            <div class="kb-progress-fill"></div>
                        </div>
                        <div class="kb-progress-text">准备同步...</div>
                    </div>
                    <div class="kb-sync-results" style="display: none;">
                        <div class="kb-result-item">
                            <span class="kb-result-label">模板:</span>
                            <span class="kb-result-value kb-templates-result">0 上传, 0 下载</span>
                        </div>
                        <div class="kb-result-item">
                            <span class="kb-result-label">知识文档:</span>
                            <span class="kb-result-value kb-knowledge-result">0 上传, 0 下载</span>
                        </div>
                        <div class="kb-result-item">
                            <span class="kb-result-label">FAQ:</span>
                            <span class="kb-result-value kb-faq-result">0 上传, 0 下载</span>
                        </div>
                    </div>
                </div>
                <div class="kb-error-section" style="display: none;">
                    <div class="kb-error-message"></div>
                    <button class="kb-error-dismiss">✕</button>
                </div>
            </div>
        `;
    }
    
    /**
     * @function addStyles - 添加样式
     * @description 添加组件所需的CSS样式
     */
    addStyles() {
        const styleId = 'knowledge-base-styles';
        if (document.getElementById(styleId)) return;
        
        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            .knowledge-base-container {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(10px);
                border-radius: 12px;
                border: 1px solid rgba(0, 0, 0, 0.1);
                margin: 16px 0;
                overflow: hidden;
                transition: all 0.3s ease;
            }
            
            .knowledge-base-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px;
                background: rgba(52, 199, 89, 0.05);
                border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            }
            
            .knowledge-base-title {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
                color: #1d1d1f;
            }
            
            .knowledge-base-actions {
                display: flex;
                gap: 8px;
            }
            
            .kb-auth-btn,
            .kb-sync-btn,
            .kb-settings-btn {
                background: none;
                border: none;
                padding: 8px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 16px;
                transition: all 0.2s ease;
            }
            
            .kb-auth-btn:hover,
            .kb-sync-btn:hover,
            .kb-settings-btn:hover {
                background: rgba(52, 199, 89, 0.1);
            }
            
            .kb-sync-btn.syncing {
                animation: spin 1s linear infinite;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            .knowledge-base-content {
                padding: 16px;
            }
            
            .kb-status-section {
                margin-bottom: 16px;
            }
            
            .kb-auth-status {
                display: flex;
                flex-direction: column;
                gap: 8px;
                margin-bottom: 12px;
            }
            
            .kb-status-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 14px;
            }
            
            .kb-status-label {
                color: #86868b;
                font-weight: 500;
            }
            
            .kb-status-value {
                color: #1d1d1f;
                font-weight: 600;
            }
            
            .kb-auth-value.authenticated {
                color: #34c759;
            }
            
            .kb-auth-value.not-authenticated {
                color: #ff3b30;
            }
            
            .kb-storage-info {
                margin-top: 12px;
            }
            
            .kb-storage-bar {
                width: 100%;
                height: 6px;
                background: #e5e5e7;
                border-radius: 3px;
                overflow: hidden;
                margin-bottom: 8px;
            }
            
            .kb-storage-used {
                height: 100%;
                background: linear-gradient(90deg, #34c759, #30d158);
                border-radius: 3px;
                transition: width 0.3s ease;
                width: 0%;
            }
            
            .kb-storage-text {
                font-size: 12px;
                color: #86868b;
                text-align: center;
            }
            
            .kb-sync-section {
                margin-bottom: 16px;
            }
            
            .kb-sync-progress {
                margin-bottom: 12px;
            }
            
            .kb-progress-bar {
                width: 100%;
                height: 4px;
                background: #e5e5e7;
                border-radius: 2px;
                overflow: hidden;
                margin-bottom: 8px;
            }
            
            .kb-progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #007aff, #5ac8fa);
                border-radius: 2px;
                transition: width 0.3s ease;
                width: 0%;
            }
            
            .kb-progress-text {
                font-size: 12px;
                color: #86868b;
                text-align: center;
            }
            
            .kb-sync-results {
                background: rgba(52, 199, 89, 0.05);
                border-radius: 8px;
                padding: 12px;
            }
            
            .kb-result-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 13px;
                margin-bottom: 4px;
            }
            
            .kb-result-item:last-child {
                margin-bottom: 0;
            }
            
            .kb-result-label {
                color: #86868b;
                font-weight: 500;
            }
            
            .kb-result-value {
                color: #1d1d1f;
                font-weight: 600;
            }
            
            .kb-error-section {
                background: rgba(255, 59, 48, 0.05);
                border: 1px solid rgba(255, 59, 48, 0.2);
                border-radius: 8px;
                padding: 12px;
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
            }
            
            .kb-error-message {
                flex: 1;
                font-size: 13px;
                color: #ff3b30;
                line-height: 1.4;
            }
            
            .kb-error-dismiss {
                background: none;
                border: none;
                color: #ff3b30;
                cursor: pointer;
                font-size: 14px;
                padding: 0;
                margin-left: 8px;
            }
            
            .kb-error-dismiss:hover {
                opacity: 0.7;
            }
        `;
        
        document.head.appendChild(style);
    }

    /**
     * @function bindEvents - 绑定事件
     * @description 绑定组件的各种事件
     */
    bindEvents() {
        // 认证按钮
        const authBtn = this.container.querySelector('.kb-auth-btn');
        authBtn.addEventListener('click', () => this.handleAuth());

        // 同步按钮
        const syncBtn = this.container.querySelector('.kb-sync-btn');
        syncBtn.addEventListener('click', () => this.handleSync());

        // 设置按钮
        const settingsBtn = this.container.querySelector('.kb-settings-btn');
        settingsBtn.addEventListener('click', () => this.handleSettings());

        // 错误消息关闭按钮
        const errorDismiss = this.container.querySelector('.kb-error-dismiss');
        errorDismiss.addEventListener('click', () => this.hideError());
    }

    /**
     * @function handleAuth - 处理认证
     * @description 处理Google Drive认证
     */
    async handleAuth() {
        try {
            if (!this.googleDriveAPI) {
                throw new Error('Google Drive API不可用');
            }

            this.showProgress('正在认证Google Drive...');

            const success = await this.googleDriveAPI.authenticate();

            if (success) {
                await this.updateStatus();
                this.showSuccess('Google Drive认证成功');

                // 触发认证完成回调
                if (this.options.onAuthComplete && typeof this.options.onAuthComplete === 'function') {
                    this.options.onAuthComplete();
                }
            } else {
                throw new Error('认证失败');
            }

        } catch (error) {
            console.error('Google Drive认证失败:', error);
            this.showError('认证失败: ' + error.message);

            // 触发错误回调
            if (this.options.onError && typeof this.options.onError === 'function') {
                this.options.onError(error);
            }
        } finally {
            this.hideProgress();
        }
    }

    /**
     * @function handleSync - 处理同步
     * @description 处理知识库同步
     */
    async handleSync() {
        if (this.isSyncing) return;

        try {
            if (!this.googleDriveAPI) {
                throw new Error('Google Drive API不可用');
            }

            const authStatus = this.googleDriveAPI.getAuthStatus();
            if (!authStatus.isAuthenticated) {
                throw new Error('请先认证Google Drive');
            }

            this.isSyncing = true;
            this.showSyncProgress();

            const syncResult = await this.googleDriveAPI.syncKnowledgeBase({
                syncTemplates: true,
                syncKnowledge: true,
                syncFAQ: true
            });

            this.showSyncResults(syncResult);
            await this.updateStatus();

            // 触发同步完成回调
            if (this.options.onSyncComplete && typeof this.options.onSyncComplete === 'function') {
                this.options.onSyncComplete(syncResult);
            }

        } catch (error) {
            console.error('知识库同步失败:', error);
            this.showError('同步失败: ' + error.message);

            // 触发错误回调
            if (this.options.onError && typeof this.options.onError === 'function') {
                this.options.onError(error);
            }
        } finally {
            this.isSyncing = false;
            this.hideSyncProgress();
        }
    }

    /**
     * @function handleSettings - 处理设置
     * @description 打开设置界面
     */
    handleSettings() {
        // 这里可以打开设置界面
        console.log('打开知识库设置');

        // 暂时显示开发中提示
        alert('知识库设置功能正在开发中...');
    }

    /**
     * @function updateStatus - 更新状态
     * @description 更新认证和同步状态显示
     */
    async updateStatus() {
        try {
            if (!this.googleDriveAPI) return;

            // 更新认证状态
            this.authStatus = this.googleDriveAPI.getAuthStatus();
            this.updateAuthStatus();

            // 更新同步状态
            this.syncStatus = await this.googleDriveAPI.getSyncStatus();
            this.updateSyncStatus();

            // 更新存储信息
            if (this.authStatus.isAuthenticated && this.options.showStorageInfo) {
                await this.updateStorageInfo();
            }

        } catch (error) {
            console.error('更新状态失败:', error);
        }
    }

    /**
     * @function updateAuthStatus - 更新认证状态
     * @description 更新认证状态显示
     */
    updateAuthStatus() {
        const authValue = this.container.querySelector('.kb-auth-value');
        const authBtn = this.container.querySelector('.kb-auth-btn');

        if (this.authStatus.isAuthenticated) {
            authValue.textContent = '已认证';
            authValue.className = 'kb-status-value kb-auth-value authenticated';
            authBtn.title = '重新认证';
        } else {
            authValue.textContent = '未认证';
            authValue.className = 'kb-status-value kb-auth-value not-authenticated';
            authBtn.title = '认证Google Drive';
        }
    }

    /**
     * @function updateSyncStatus - 更新同步状态
     * @description 更新同步状态显示
     */
    updateSyncStatus() {
        const syncValue = this.container.querySelector('.kb-sync-value');

        if (this.syncStatus.lastSync) {
            const lastSyncDate = new Date(this.syncStatus.lastSync);
            const now = new Date();
            const diffMinutes = Math.floor((now - lastSyncDate) / (1000 * 60));

            if (diffMinutes < 1) {
                syncValue.textContent = '刚刚';
            } else if (diffMinutes < 60) {
                syncValue.textContent = `${diffMinutes}分钟前`;
            } else if (diffMinutes < 1440) {
                const diffHours = Math.floor(diffMinutes / 60);
                syncValue.textContent = `${diffHours}小时前`;
            } else {
                const diffDays = Math.floor(diffMinutes / 1440);
                syncValue.textContent = `${diffDays}天前`;
            }
        } else {
            syncValue.textContent = '从未同步';
        }
    }

    /**
     * @function updateStorageInfo - 更新存储信息
     * @description 更新Google Drive存储信息显示
     */
    async updateStorageInfo() {
        try {
            const storageInfo = await this.googleDriveAPI.getStorageQuota();
            const storageSection = this.container.querySelector('.kb-storage-info');
            const storageUsed = this.container.querySelector('.kb-storage-used');
            const storageUsage = this.container.querySelector('.kb-storage-usage');
            const storageTotal = this.container.querySelector('.kb-storage-total');
            const storagePercentage = this.container.querySelector('.kb-storage-percentage');

            // 更新存储条
            storageUsed.style.width = storageInfo.usagePercentage + '%';

            // 更新文本
            storageUsage.textContent = this.googleDriveAPI.formatFileSize(storageInfo.usage);
            storageTotal.textContent = this.googleDriveAPI.formatFileSize(storageInfo.limit);
            storagePercentage.textContent = storageInfo.usagePercentage + '%';

            // 显示存储信息
            storageSection.style.display = 'block';

        } catch (error) {
            console.error('更新存储信息失败:', error);
        }
    }

    /**
     * @function showSyncProgress - 显示同步进度
     * @description 显示同步进度界面
     */
    showSyncProgress() {
        const syncBtn = this.container.querySelector('.kb-sync-btn');
        const progressSection = this.container.querySelector('.kb-sync-progress');
        const resultsSection = this.container.querySelector('.kb-sync-results');

        syncBtn.classList.add('syncing');
        progressSection.style.display = 'block';
        resultsSection.style.display = 'none';

        this.updateProgress(0, '开始同步...');
    }

    /**
     * @function hideSyncProgress - 隐藏同步进度
     * @description 隐藏同步进度界面
     */
    hideSyncProgress() {
        const syncBtn = this.container.querySelector('.kb-sync-btn');
        const progressSection = this.container.querySelector('.kb-sync-progress');

        syncBtn.classList.remove('syncing');

        setTimeout(() => {
            progressSection.style.display = 'none';
        }, 1000);
    }

    /**
     * @function updateProgress - 更新进度
     * @description 更新同步进度显示
     * @param {number} percentage - 进度百分比
     * @param {string} text - 进度文本
     */
    updateProgress(percentage, text) {
        const progressFill = this.container.querySelector('.kb-progress-fill');
        const progressText = this.container.querySelector('.kb-progress-text');

        progressFill.style.width = percentage + '%';
        progressText.textContent = text;
    }

    /**
     * @function showSyncResults - 显示同步结果
     * @description 显示同步结果信息
     * @param {Object} syncResult - 同步结果
     */
    showSyncResults(syncResult) {
        const resultsSection = this.container.querySelector('.kb-sync-results');
        const templatesResult = this.container.querySelector('.kb-templates-result');
        const knowledgeResult = this.container.querySelector('.kb-knowledge-result');
        const faqResult = this.container.querySelector('.kb-faq-result');

        // 更新结果显示
        templatesResult.textContent = `${syncResult.templates.uploaded} 上传, ${syncResult.templates.downloaded} 下载`;
        knowledgeResult.textContent = `${syncResult.knowledge.uploaded} 上传, ${syncResult.knowledge.downloaded} 下载`;
        faqResult.textContent = `${syncResult.faq.uploaded} 上传, ${syncResult.faq.downloaded} 下载`;

        // 显示结果
        resultsSection.style.display = 'block';

        // 自动隐藏结果
        setTimeout(() => {
            resultsSection.style.display = 'none';
        }, 10000);
    }

    /**
     * @function showProgress - 显示进度
     * @description 显示通用进度信息
     * @param {string} message - 进度消息
     */
    showProgress(message) {
        this.updateProgress(50, message);
        this.container.querySelector('.kb-sync-progress').style.display = 'block';
    }

    /**
     * @function hideProgress - 隐藏进度
     * @description 隐藏通用进度信息
     */
    hideProgress() {
        setTimeout(() => {
            this.container.querySelector('.kb-sync-progress').style.display = 'none';
        }, 500);
    }

    /**
     * @function showSuccess - 显示成功消息
     * @description 显示成功操作的消息
     * @param {string} message - 成功消息
     */
    showSuccess(message) {
        // 暂时使用进度条显示成功消息
        this.updateProgress(100, message);
        this.container.querySelector('.kb-sync-progress').style.display = 'block';

        setTimeout(() => {
            this.hideProgress();
        }, 2000);
    }

    /**
     * @function showError - 显示错误信息
     * @description 显示错误消息
     * @param {string} message - 错误消息
     */
    showError(message) {
        const errorSection = this.container.querySelector('.kb-error-section');
        const errorMessage = this.container.querySelector('.kb-error-message');

        errorMessage.textContent = message;
        errorSection.style.display = 'flex';

        // 自动隐藏错误消息
        setTimeout(() => {
            this.hideError();
        }, 10000);
    }

    /**
     * @function hideError - 隐藏错误信息
     * @description 隐藏错误消息
     */
    hideError() {
        const errorSection = this.container.querySelector('.kb-error-section');
        errorSection.style.display = 'none';
    }

    /**
     * @function startAutoSync - 开始自动同步
     * @description 启动自动同步定时器
     */
    startAutoSync() {
        if (this.syncTimer) {
            clearInterval(this.syncTimer);
        }

        this.syncTimer = setInterval(async () => {
            if (!this.isSyncing && this.authStatus && this.authStatus.isAuthenticated) {
                console.log('🔄 执行自动同步...');
                await this.handleSync();
            }
        }, this.options.syncInterval);

        console.log(`⏰ 自动同步已启动，间隔: ${this.options.syncInterval / 1000}秒`);
    }

    /**
     * @function stopAutoSync - 停止自动同步
     * @description 停止自动同步定时器
     */
    stopAutoSync() {
        if (this.syncTimer) {
            clearInterval(this.syncTimer);
            this.syncTimer = null;
            console.log('⏰ 自动同步已停止');
        }
    }

    /**
     * @function updateLanguage - 更新语言
     * @description 更新组件的显示语言
     * @param {string} language - 新的语言代码
     */
    updateLanguage(language) {
        this.options.language = language;

        // 这里可以更新界面文本的语言
        // 暂时只更新配置
        console.log('🌐 知识库组件语言已更新:', language);
    }

    /**
     * @function getStatus - 获取状态
     * @description 获取当前组件状态
     * @returns {Object} 状态信息
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            isSyncing: this.isSyncing,
            authStatus: this.authStatus,
            syncStatus: this.syncStatus,
            autoSyncEnabled: !!this.syncTimer,
            lastUpdate: Date.now()
        };
    }

    /**
     * @function refresh - 刷新状态
     * @description 刷新组件状态显示
     */
    async refresh() {
        try {
            await this.updateStatus();
            console.log('🔄 知识库状态已刷新');
        } catch (error) {
            console.error('刷新状态失败:', error);
            this.showError('刷新状态失败: ' + error.message);
        }
    }

    /**
     * @function logout - 注销
     * @description 注销Google Drive认证
     */
    async logout() {
        try {
            if (this.googleDriveAPI) {
                await this.googleDriveAPI.logout();
                await this.updateStatus();
                this.showSuccess('已注销Google Drive');
            }
        } catch (error) {
            console.error('注销失败:', error);
            this.showError('注销失败: ' + error.message);
        }
    }

    /**
     * @function destroy - 销毁组件
     * @description 清理组件资源
     */
    destroy() {
        // 停止自动同步
        this.stopAutoSync();

        // 清理容器
        if (this.container) {
            this.container.innerHTML = '';
            this.container.className = '';
        }

        // 清理引用
        this.googleDriveAPI = null;
        this.authStatus = null;
        this.syncStatus = null;
        this.isInitialized = false;
    }
}
// #endregion

console.log('☁️ AI Side Panel 知识库管理组件已加载');
