/**
 * @file AI Side Panel 性能监控组件
 * @description 实时性能监控界面，显示性能指标、测试结果等信息
 */

// #region 性能监控组件类
/**
 * @class PerformanceMonitor - 性能监控组件
 * @description 管理性能监控的显示和交互
 */
class PerformanceMonitor {
    /**
     * @function constructor - 构造函数
     * @param {string|HTMLElement} container - 容器元素或选择器
     * @param {Object} options - 配置选项
     */
    constructor(container, options = {}) {
        this.container = typeof container === 'string' 
            ? document.querySelector(container) 
            : container;
            
        if (!this.container) {
            throw new Error('性能监控容器元素未找到');
        }
        
        this.options = {
            updateInterval: 5000,      // 更新间隔（毫秒）
            showDetailedMetrics: true, // 显示详细指标
            enableAutoTest: false,     // 启用自动测试
            testInterval: 300000,      // 测试间隔（5分钟）
            language: 'zh_CN',
            onMetricsUpdate: null,
            onTestComplete: null,
            onError: null,
            ...options
        };
        
        this.performanceOptimizer = null;
        this.testFramework = null;
        this.isInitialized = false;
        this.isMonitoring = false;
        this.updateTimer = null;
        this.testTimer = null;
        this.currentMetrics = null;
        this.testResults = null;
        
        this.initialize();
    }
    
    /**
     * @function initialize - 初始化组件
     * @description 初始化性能监控组件
     */
    async initialize() {
        try {
            // 获取性能优化器
            if (typeof getPerformanceOptimizer === 'function') {
                this.performanceOptimizer = getPerformanceOptimizer();
                await this.performanceOptimizer.initialize();
            } else {
                console.warn('⚠️ 性能优化器不可用');
            }
            
            // 获取测试框架
            if (typeof getTestFramework === 'function') {
                this.testFramework = getTestFramework();
                await this.testFramework.initialize();
            } else {
                console.warn('⚠️ 测试框架不可用');
            }
            
            // 初始化容器
            this.setupContainer();
            
            // 添加样式
            this.addStyles();
            
            // 绑定事件
            this.bindEvents();
            
            // 开始监控
            this.startMonitoring();
            
            this.isInitialized = true;
            console.log('✅ 性能监控组件初始化完成');
            
        } catch (error) {
            console.error('❌ 性能监控组件初始化失败:', error);
            this.showError('初始化失败: ' + error.message);
        }
    }
    
    /**
     * @function setupContainer - 设置容器
     * @description 设置容器的基本结构和样式
     */
    setupContainer() {
        this.container.className = 'performance-monitor-container';
        this.container.innerHTML = `
            <div class="performance-monitor-header">
                <h4 class="performance-monitor-title">📊 性能监控</h4>
                <div class="performance-monitor-actions">
                    <button class="pm-refresh-btn" title="刷新数据">🔄</button>
                    <button class="pm-test-btn" title="运行测试">🧪</button>
                    <button class="pm-settings-btn" title="设置">⚙️</button>
                </div>
            </div>
            <div class="performance-monitor-content">
                <div class="pm-metrics-section">
                    <div class="pm-section-title">实时指标</div>
                    <div class="pm-metrics-grid">
                        <div class="pm-metric-card">
                            <div class="pm-metric-label">API调用</div>
                            <div class="pm-metric-value pm-api-calls">0</div>
                            <div class="pm-metric-detail pm-api-detail">缓存命中率: 0%</div>
                        </div>
                        <div class="pm-metric-card">
                            <div class="pm-metric-label">内存使用</div>
                            <div class="pm-metric-value pm-memory-usage">0 MB</div>
                            <div class="pm-metric-detail pm-memory-detail">峰值: 0 MB</div>
                        </div>
                        <div class="pm-metric-card">
                            <div class="pm-metric-label">渲染性能</div>
                            <div class="pm-metric-value pm-render-time">0 ms</div>
                            <div class="pm-metric-detail pm-render-detail">平均时间</div>
                        </div>
                        <div class="pm-metric-card">
                            <div class="pm-metric-label">运行时间</div>
                            <div class="pm-metric-value pm-uptime">0s</div>
                            <div class="pm-metric-detail pm-uptime-detail">自启动以来</div>
                        </div>
                    </div>
                </div>
                <div class="pm-test-section">
                    <div class="pm-section-title">测试结果</div>
                    <div class="pm-test-summary">
                        <div class="pm-test-item">
                            <span class="pm-test-label">功能测试:</span>
                            <span class="pm-test-value pm-functional-result">未运行</span>
                        </div>
                        <div class="pm-test-item">
                            <span class="pm-test-label">兼容性测试:</span>
                            <span class="pm-test-value pm-compatibility-result">未运行</span>
                        </div>
                        <div class="pm-test-item">
                            <span class="pm-test-label">性能测试:</span>
                            <span class="pm-test-value pm-performance-result">未运行</span>
                        </div>
                        <div class="pm-test-item">
                            <span class="pm-test-label">用户体验:</span>
                            <span class="pm-test-value pm-ux-result">未运行</span>
                        </div>
                    </div>
                    <div class="pm-test-progress" style="display: none;">
                        <div class="pm-progress-bar">
                            <div class="pm-progress-fill"></div>
                        </div>
                        <div class="pm-progress-text">准备测试...</div>
                    </div>
                </div>
                <div class="pm-status-section">
                    <div class="pm-section-title">系统状态</div>
                    <div class="pm-status-grid">
                        <div class="pm-status-item">
                            <span class="pm-status-label">缓存管理器:</span>
                            <span class="pm-status-value pm-cache-status">检查中...</span>
                        </div>
                        <div class="pm-status-item">
                            <span class="pm-status-label">性能优化器:</span>
                            <span class="pm-status-value pm-optimizer-status">检查中...</span>
                        </div>
                        <div class="pm-status-item">
                            <span class="pm-status-label">测试框架:</span>
                            <span class="pm-status-value pm-test-status">检查中...</span>
                        </div>
                        <div class="pm-status-item">
                            <span class="pm-status-label">监控状态:</span>
                            <span class="pm-status-value pm-monitor-status">启动中...</span>
                        </div>
                    </div>
                </div>
                <div class="pm-error-section" style="display: none;">
                    <div class="pm-error-message"></div>
                    <button class="pm-error-dismiss">✕</button>
                </div>
            </div>
        `;
    }
    
    /**
     * @function addStyles - 添加样式
     * @description 添加组件所需的CSS样式
     */
    addStyles() {
        const styleId = 'performance-monitor-styles';
        if (document.getElementById(styleId)) return;
        
        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            .performance-monitor-container {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(10px);
                border-radius: 12px;
                border: 1px solid rgba(0, 0, 0, 0.1);
                margin: 16px 0;
                overflow: hidden;
                transition: all 0.3s ease;
            }
            
            .performance-monitor-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px;
                background: rgba(0, 122, 255, 0.05);
                border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            }
            
            .performance-monitor-title {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
                color: #1d1d1f;
            }
            
            .performance-monitor-actions {
                display: flex;
                gap: 8px;
            }
            
            .pm-refresh-btn,
            .pm-test-btn,
            .pm-settings-btn {
                background: none;
                border: none;
                padding: 8px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 16px;
                transition: all 0.2s ease;
            }
            
            .pm-refresh-btn:hover,
            .pm-test-btn:hover,
            .pm-settings-btn:hover {
                background: rgba(0, 122, 255, 0.1);
            }
            
            .pm-refresh-btn.refreshing {
                animation: spin 1s linear infinite;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            .performance-monitor-content {
                padding: 16px;
            }
            
            .pm-section-title {
                font-size: 14px;
                font-weight: 600;
                color: #1d1d1f;
                margin-bottom: 12px;
                padding-bottom: 8px;
                border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            }
            
            .pm-metrics-section,
            .pm-test-section,
            .pm-status-section {
                margin-bottom: 24px;
            }
            
            .pm-metrics-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 12px;
            }
            
            .pm-metric-card {
                background: rgba(248, 248, 248, 0.8);
                border-radius: 8px;
                padding: 12px;
                text-align: center;
                transition: all 0.2s ease;
            }
            
            .pm-metric-card:hover {
                background: rgba(240, 240, 240, 0.9);
            }
            
            .pm-metric-label {
                font-size: 12px;
                color: #86868b;
                margin-bottom: 4px;
            }
            
            .pm-metric-value {
                font-size: 18px;
                font-weight: 600;
                color: #1d1d1f;
                margin-bottom: 4px;
            }
            
            .pm-metric-detail {
                font-size: 10px;
                color: #86868b;
            }
            
            .pm-test-summary {
                display: flex;
                flex-direction: column;
                gap: 8px;
                margin-bottom: 16px;
            }
            
            .pm-test-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 14px;
            }
            
            .pm-test-label {
                color: #86868b;
                font-weight: 500;
            }
            
            .pm-test-value {
                color: #1d1d1f;
                font-weight: 600;
            }
            
            .pm-test-value.passed {
                color: #34c759;
            }
            
            .pm-test-value.failed {
                color: #ff3b30;
            }
            
            .pm-test-progress {
                margin-top: 12px;
            }
            
            .pm-progress-bar {
                width: 100%;
                height: 4px;
                background: #e5e5e7;
                border-radius: 2px;
                overflow: hidden;
                margin-bottom: 8px;
            }
            
            .pm-progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #007aff, #5ac8fa);
                border-radius: 2px;
                transition: width 0.3s ease;
                width: 0%;
            }
            
            .pm-progress-text {
                font-size: 12px;
                color: #86868b;
                text-align: center;
            }
            
            .pm-status-grid {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }
            
            .pm-status-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 13px;
            }
            
            .pm-status-label {
                color: #86868b;
                font-weight: 500;
            }
            
            .pm-status-value {
                color: #1d1d1f;
                font-weight: 600;
            }
            
            .pm-status-value.online {
                color: #34c759;
            }
            
            .pm-status-value.offline {
                color: #ff3b30;
            }
            
            .pm-error-section {
                background: rgba(255, 59, 48, 0.05);
                border: 1px solid rgba(255, 59, 48, 0.2);
                border-radius: 8px;
                padding: 12px;
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
            }
            
            .pm-error-message {
                flex: 1;
                font-size: 13px;
                color: #ff3b30;
                line-height: 1.4;
            }
            
            .pm-error-dismiss {
                background: none;
                border: none;
                color: #ff3b30;
                cursor: pointer;
                font-size: 14px;
                padding: 0;
                margin-left: 8px;
            }
            
            .pm-error-dismiss:hover {
                opacity: 0.7;
            }
        `;
        
        document.head.appendChild(style);
    }

    /**
     * @function bindEvents - 绑定事件
     * @description 绑定组件的各种事件
     */
    bindEvents() {
        // 刷新按钮
        const refreshBtn = this.container.querySelector('.pm-refresh-btn');
        refreshBtn.addEventListener('click', () => this.handleRefresh());

        // 测试按钮
        const testBtn = this.container.querySelector('.pm-test-btn');
        testBtn.addEventListener('click', () => this.handleRunTests());

        // 设置按钮
        const settingsBtn = this.container.querySelector('.pm-settings-btn');
        settingsBtn.addEventListener('click', () => this.handleSettings());

        // 错误消息关闭按钮
        const errorDismiss = this.container.querySelector('.pm-error-dismiss');
        errorDismiss.addEventListener('click', () => this.hideError());
    }

    /**
     * @function startMonitoring - 开始监控
     * @description 启动性能监控
     */
    startMonitoring() {
        if (this.isMonitoring) return;

        this.isMonitoring = true;

        // 立即更新一次
        this.updateMetrics();

        // 设置定时更新
        this.updateTimer = setInterval(() => {
            this.updateMetrics();
        }, this.options.updateInterval);

        // 设置自动测试
        if (this.options.enableAutoTest) {
            this.testTimer = setInterval(() => {
                this.runTests();
            }, this.options.testInterval);
        }

        // 更新状态
        this.updateStatus();

        console.log('📊 性能监控已启动');
    }

    /**
     * @function stopMonitoring - 停止监控
     * @description 停止性能监控
     */
    stopMonitoring() {
        if (!this.isMonitoring) return;

        this.isMonitoring = false;

        if (this.updateTimer) {
            clearInterval(this.updateTimer);
            this.updateTimer = null;
        }

        if (this.testTimer) {
            clearInterval(this.testTimer);
            this.testTimer = null;
        }

        console.log('📊 性能监控已停止');
    }

    /**
     * @function updateMetrics - 更新性能指标
     * @description 更新性能指标显示
     */
    async updateMetrics() {
        try {
            if (!this.performanceOptimizer) return;

            this.currentMetrics = this.performanceOptimizer.getMetrics();

            // 更新API调用指标
            const apiCalls = this.container.querySelector('.pm-api-calls');
            const apiDetail = this.container.querySelector('.pm-api-detail');
            apiCalls.textContent = this.currentMetrics.api.total;
            apiDetail.textContent = `缓存命中率: ${this.currentMetrics.api.cacheHitRate}`;

            // 更新内存使用指标
            const memoryUsage = this.container.querySelector('.pm-memory-usage');
            const memoryDetail = this.container.querySelector('.pm-memory-detail');
            memoryUsage.textContent = this.formatBytes(this.currentMetrics.memory.current);
            memoryDetail.textContent = `峰值: ${this.formatBytes(this.currentMetrics.memory.peak)}`;

            // 更新渲染性能指标
            const renderTime = this.container.querySelector('.pm-render-time');
            const renderDetail = this.container.querySelector('.pm-render-detail');
            renderTime.textContent = this.currentMetrics.render.average.toFixed(2) + ' ms';
            renderDetail.textContent = `${this.currentMetrics.render.count}次渲染`;

            // 更新运行时间
            const uptime = this.container.querySelector('.pm-uptime');
            const uptimeDetail = this.container.querySelector('.pm-uptime-detail');
            const runtime = Date.now() - this.currentMetrics.startTime;
            uptime.textContent = this.formatDuration(runtime);
            uptimeDetail.textContent = '自启动以来';

            // 触发更新回调
            if (this.options.onMetricsUpdate && typeof this.options.onMetricsUpdate === 'function') {
                this.options.onMetricsUpdate(this.currentMetrics);
            }

        } catch (error) {
            console.error('更新性能指标失败:', error);
        }
    }

    /**
     * @function updateStatus - 更新系统状态
     * @description 更新系统组件状态显示
     */
    updateStatus() {
        // 缓存管理器状态
        const cacheStatus = this.container.querySelector('.pm-cache-status');
        if (typeof getCacheManager === 'function') {
            cacheStatus.textContent = '在线';
            cacheStatus.className = 'pm-status-value online';
        } else {
            cacheStatus.textContent = '离线';
            cacheStatus.className = 'pm-status-value offline';
        }

        // 性能优化器状态
        const optimizerStatus = this.container.querySelector('.pm-optimizer-status');
        if (this.performanceOptimizer) {
            optimizerStatus.textContent = '在线';
            optimizerStatus.className = 'pm-status-value online';
        } else {
            optimizerStatus.textContent = '离线';
            optimizerStatus.className = 'pm-status-value offline';
        }

        // 测试框架状态
        const testStatus = this.container.querySelector('.pm-test-status');
        if (this.testFramework) {
            testStatus.textContent = '在线';
            testStatus.className = 'pm-status-value online';
        } else {
            testStatus.textContent = '离线';
            testStatus.className = 'pm-status-value offline';
        }

        // 监控状态
        const monitorStatus = this.container.querySelector('.pm-monitor-status');
        if (this.isMonitoring) {
            monitorStatus.textContent = '运行中';
            monitorStatus.className = 'pm-status-value online';
        } else {
            monitorStatus.textContent = '已停止';
            monitorStatus.className = 'pm-status-value offline';
        }
    }

    /**
     * @function handleRefresh - 处理刷新
     * @description 处理刷新按钮点击
     */
    async handleRefresh() {
        const refreshBtn = this.container.querySelector('.pm-refresh-btn');
        refreshBtn.classList.add('refreshing');

        try {
            await this.updateMetrics();
            this.updateStatus();
            console.log('📊 性能数据已刷新');

        } catch (error) {
            console.error('刷新性能数据失败:', error);
            this.showError('刷新失败: ' + error.message);
        } finally {
            setTimeout(() => {
                refreshBtn.classList.remove('refreshing');
            }, 1000);
        }
    }

    /**
     * @function handleRunTests - 处理运行测试
     * @description 处理测试按钮点击
     */
    async handleRunTests() {
        if (!this.testFramework) {
            this.showError('测试框架不可用');
            return;
        }

        try {
            this.showTestProgress();

            const testResults = await this.testFramework.runAllTests();
            this.testResults = testResults;

            this.updateTestResults(testResults);

            // 触发测试完成回调
            if (this.options.onTestComplete && typeof this.options.onTestComplete === 'function') {
                this.options.onTestComplete(testResults);
            }

            console.log('🧪 测试完成:', testResults);

        } catch (error) {
            console.error('运行测试失败:', error);
            this.showError('测试失败: ' + error.message);
        } finally {
            this.hideTestProgress();
        }
    }

    /**
     * @function handleSettings - 处理设置
     * @description 打开设置界面
     */
    handleSettings() {
        // 这里可以打开设置界面
        console.log('打开性能监控设置');

        // 暂时显示开发中提示
        alert('性能监控设置功能正在开发中...');
    }

    /**
     * @function showTestProgress - 显示测试进度
     * @description 显示测试进度界面
     */
    showTestProgress() {
        const progressSection = this.container.querySelector('.pm-test-progress');
        progressSection.style.display = 'block';

        this.updateTestProgress(0, '开始测试...');
    }

    /**
     * @function hideTestProgress - 隐藏测试进度
     * @description 隐藏测试进度界面
     */
    hideTestProgress() {
        const progressSection = this.container.querySelector('.pm-test-progress');

        setTimeout(() => {
            progressSection.style.display = 'none';
        }, 1000);
    }

    /**
     * @function updateTestProgress - 更新测试进度
     * @description 更新测试进度显示
     * @param {number} percentage - 进度百分比
     * @param {string} text - 进度文本
     */
    updateTestProgress(percentage, text) {
        const progressFill = this.container.querySelector('.pm-progress-fill');
        const progressText = this.container.querySelector('.pm-progress-text');

        progressFill.style.width = percentage + '%';
        progressText.textContent = text;
    }

    /**
     * @function updateTestResults - 更新测试结果
     * @description 更新测试结果显示
     * @param {Object} testResults - 测试结果
     */
    updateTestResults(testResults) {
        const summary = testResults.summary;

        // 功能测试结果
        const functionalResult = this.container.querySelector('.pm-functional-result');
        this.updateTestResultElement(functionalResult, summary.functional);

        // 兼容性测试结果
        const compatibilityResult = this.container.querySelector('.pm-compatibility-result');
        this.updateTestResultElement(compatibilityResult, summary.compatibility);

        // 性能测试结果
        const performanceResult = this.container.querySelector('.pm-performance-result');
        this.updateTestResultElement(performanceResult, summary.performance);

        // 用户体验测试结果
        const uxResult = this.container.querySelector('.pm-ux-result');
        this.updateTestResultElement(uxResult, summary.userExperience);
    }

    /**
     * @function updateTestResultElement - 更新测试结果元素
     * @description 更新单个测试结果元素
     * @param {HTMLElement} element - 结果元素
     * @param {Object} result - 测试结果
     */
    updateTestResultElement(element, result) {
        if (result.total === 0) {
            element.textContent = '未运行';
            element.className = 'pm-test-value';
        } else if (result.failed === 0) {
            element.textContent = `${result.passed}/${result.total} 通过`;
            element.className = 'pm-test-value passed';
        } else {
            element.textContent = `${result.failed}/${result.total} 失败`;
            element.className = 'pm-test-value failed';
        }
    }

    /**
     * @function showError - 显示错误信息
     * @description 显示错误消息
     * @param {string} message - 错误消息
     */
    showError(message) {
        const errorSection = this.container.querySelector('.pm-error-section');
        const errorMessage = this.container.querySelector('.pm-error-message');

        errorMessage.textContent = message;
        errorSection.style.display = 'flex';

        // 自动隐藏错误消息
        setTimeout(() => {
            this.hideError();
        }, 10000);

        // 触发错误回调
        if (this.options.onError && typeof this.options.onError === 'function') {
            this.options.onError(new Error(message));
        }
    }

    /**
     * @function hideError - 隐藏错误信息
     * @description 隐藏错误消息
     */
    hideError() {
        const errorSection = this.container.querySelector('.pm-error-section');
        errorSection.style.display = 'none';
    }

    /**
     * @function formatBytes - 格式化字节数
     * @description 将字节数格式化为可读格式
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的字符串
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';

        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * @function formatDuration - 格式化持续时间
     * @description 将毫秒数格式化为可读的持续时间
     * @param {number} ms - 毫秒数
     * @returns {string} 格式化后的字符串
     */
    formatDuration(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);

        if (hours > 0) {
            return `${hours}h${minutes % 60}m`;
        } else if (minutes > 0) {
            return `${minutes}m${seconds % 60}s`;
        } else {
            return `${seconds}s`;
        }
    }

    /**
     * @function getMetrics - 获取当前指标
     * @description 获取当前的性能指标
     * @returns {Object} 性能指标
     */
    getMetrics() {
        return this.currentMetrics;
    }

    /**
     * @function getTestResults - 获取测试结果
     * @description 获取最新的测试结果
     * @returns {Object} 测试结果
     */
    getTestResults() {
        return this.testResults;
    }

    /**
     * @function updateLanguage - 更新语言
     * @description 更新组件的显示语言
     * @param {string} language - 新的语言代码
     */
    updateLanguage(language) {
        this.options.language = language;

        // 这里可以更新界面文本的语言
        // 暂时只更新配置
        console.log('🌐 性能监控组件语言已更新:', language);
    }

    /**
     * @function destroy - 销毁组件
     * @description 清理组件资源
     */
    destroy() {
        // 停止监控
        this.stopMonitoring();

        // 清理容器
        if (this.container) {
            this.container.innerHTML = '';
            this.container.className = '';
        }

        // 清理引用
        this.performanceOptimizer = null;
        this.testFramework = null;
        this.currentMetrics = null;
        this.testResults = null;
        this.isInitialized = false;
    }
}
// #endregion

console.log('📊 AI Side Panel 性能监控组件已加载');
