/**
 * @file AI Side Panel 统一初始化管理器
 * @description 管理扩展程序各组件的初始化顺序和依赖关系，提供优雅的错误处理和降级机制
 */

// #region 全局变量和配置
let aisp_initializationState = {
    isInitialized: false,
    initializationStartTime: null,
    initializationEndTime: null,
    components: new Map(),
    errors: [],
    warnings: []
};

// 初始化配置
const AISP_INITIALIZATION_CONFIG = {
    // 超时配置
    COMPONENT_TIMEOUT: 10000,      // 单个组件初始化超时时间（毫秒）
    TOTAL_TIMEOUT: 30000,          // 总初始化超时时间（毫秒）
    
    // 重试配置
    MAX_RETRIES: 3,                // 最大重试次数
    RETRY_DELAY: 1000,             // 重试延迟（毫秒）
    
    // 依赖配置
    DEPENDENCY_CHECK_INTERVAL: 100, // 依赖检查间隔（毫秒）
    MAX_DEPENDENCY_WAIT: 5000,     // 最大依赖等待时间（毫秒）
    
    // 错误处理
    CONTINUE_ON_ERROR: true,       // 是否在错误时继续初始化其他组件
    ENABLE_GRACEFUL_DEGRADATION: true // 是否启用优雅降级
};

// 组件初始化顺序和依赖关系
const AISP_COMPONENT_DEPENDENCIES = {
    // 核心基础组件（第一层）
    'logger': {
        priority: 1,
        dependencies: [],
        required: true,
        initFunction: 'aisp_logInitialize',
        description: '日志系统'
    },
    
    'config-manager': {
        priority: 1,
        dependencies: [],
        required: true,
        initFunction: 'aisp_configInitialize',
        description: '配置管理器'
    },
    
    // 工具组件（第二层）
    'cache-manager': {
        priority: 2,
        dependencies: ['logger'],
        required: true,
        initFunction: 'getCacheManager',
        description: '缓存管理器'
    },
    
    'performance-optimizer': {
        priority: 2,
        dependencies: ['logger', 'cache-manager'],
        required: false,
        initFunction: 'aisp_initialize',
        description: '性能优化器'
    },
    
    // API组件（第三层）
    'gemini-api': {
        priority: 3,
        dependencies: ['logger', 'config-manager', 'performance-optimizer'],
        required: true,
        initFunction: 'aisp_geminiInitialize',
        description: 'Gemini API'
    },
    
    'google-drive-api': {
        priority: 3,
        dependencies: ['logger', 'config-manager'],
        required: false,
        initFunction: 'aisp_driveInitialize',
        description: 'Google Drive API'
    },
    
    // 管理器组件（第四层）
    'template-manager': {
        priority: 4,
        dependencies: ['logger', 'cache-manager'],
        required: true,
        initFunction: 'getTemplateManager',
        description: '模板管理器'
    },
    
    'language-manager': {
        priority: 4,
        dependencies: ['logger', 'config-manager'],
        required: true,
        initFunction: 'getLanguageManager',
        description: '语言管理器'
    },
    
    // UI组件（第五层）
    'chat-interface': {
        priority: 5,
        dependencies: ['logger', 'gemini-api', 'template-manager', 'language-manager'],
        required: true,
        initFunction: 'aisp_chatInitialize',
        description: '聊天界面'
    },
    
    'debug-panel': {
        priority: 5,
        dependencies: ['logger'],
        required: false,
        initFunction: 'aisp_debugInitialize',
        description: '调试面板'
    },
    
    'mindmap-renderer': {
        priority: 5,
        dependencies: ['logger'],
        required: false,
        initFunction: 'aisp_mindmapInitialize',
        description: '思维导图渲染器'
    }
};
// #endregion

// #region 主初始化函数
/**
 * @function aisp_initializeExtension - 初始化扩展程序
 * @description 统一初始化扩展程序的所有组件，按依赖关系和优先级顺序执行
 * @param {Object} options - 初始化选项
 * @returns {Promise<Object>} 初始化结果对象
 */
async function aisp_initializeExtension(options = {}) {
    const startTime = Date.now();
    aisp_initializationState.initializationStartTime = startTime;
    
    console.log('🚀 AI Side Panel 扩展程序初始化开始...');
    
    try {
        // 合并配置选项
        const config = {
            ...AISP_INITIALIZATION_CONFIG,
            ...options
        };
        
        // 按优先级分组组件
        const componentGroups = aisp_groupComponentsByPriority();
        
        // 逐层初始化组件
        for (const [priority, components] of componentGroups) {
            console.log(`📦 初始化优先级 ${priority} 组件...`);
            await aisp_initializeComponentGroup(components, config);
        }
        
        // 验证关键组件
        const validationResult = await aisp_validateCriticalComponents();
        
        // 记录初始化完成
        const endTime = Date.now();
        aisp_initializationState.initializationEndTime = endTime;
        aisp_initializationState.isInitialized = true;
        
        const duration = endTime - startTime;
        console.log(`✅ AI Side Panel 扩展程序初始化完成 (${duration}ms)`);
        
        return {
            success: true,
            duration,
            components: aisp_initializationState.components,
            errors: aisp_initializationState.errors,
            warnings: aisp_initializationState.warnings,
            validation: validationResult
        };
        
    } catch (error) {
        console.error('❌ 扩展程序初始化失败:', error);
        
        return {
            success: false,
            error: error.message,
            components: aisp_initializationState.components,
            errors: aisp_initializationState.errors,
            warnings: aisp_initializationState.warnings
        };
    }
}
// #endregion

// #region 组件分组和初始化
/**
 * @function aisp_groupComponentsByPriority - 按优先级分组组件
 * @description 将组件按优先级分组，便于分层初始化
 * @returns {Map<number, Array>} 按优先级分组的组件映射
 */
function aisp_groupComponentsByPriority() {
    const groups = new Map();
    
    for (const [componentName, config] of Object.entries(AISP_COMPONENT_DEPENDENCIES)) {
        const priority = config.priority;
        
        if (!groups.has(priority)) {
            groups.set(priority, []);
        }
        
        groups.get(priority).push({
            name: componentName,
            ...config
        });
    }
    
    // 按优先级排序
    return new Map([...groups.entries()].sort((a, b) => a[0] - b[0]));
}

/**
 * @function aisp_initializeComponentGroup - 初始化组件组
 * @description 并行初始化同一优先级的组件组
 * @param {Array} components - 组件配置数组
 * @param {Object} config - 初始化配置
 * @returns {Promise<void>}
 */
async function aisp_initializeComponentGroup(components, config) {
    const initPromises = components.map(component => 
        aisp_initializeComponent(component, config)
    );
    
    // 等待所有组件初始化完成
    await Promise.allSettled(initPromises);
}

/**
 * @function aisp_initializeComponent - 初始化单个组件
 * @description 初始化单个组件，包含依赖检查、超时控制和错误处理
 * @param {Object} component - 组件配置对象
 * @param {Object} config - 初始化配置
 * @returns {Promise<boolean>} 是否初始化成功
 */
async function aisp_initializeComponent(component, config) {
    const { name, dependencies, required, initFunction, description } = component;
    
    try {
        console.log(`🔧 初始化组件: ${description} (${name})`);
        
        // 检查依赖
        const dependenciesReady = await aisp_waitForDependencies(dependencies, config);
        if (!dependenciesReady) {
            throw new Error(`组件 ${name} 的依赖未满足`);
        }
        
        // 执行初始化
        const success = await aisp_executeComponentInitialization(name, initFunction, config);
        
        // 记录结果
        aisp_initializationState.components.set(name, {
            status: success ? 'initialized' : 'failed',
            required,
            description,
            initTime: Date.now()
        });
        
        if (success) {
            console.log(`✅ 组件初始化成功: ${description}`);
        } else {
            throw new Error(`组件 ${name} 初始化失败`);
        }
        
        return success;
        
    } catch (error) {
        console.error(`❌ 组件初始化失败: ${description} -`, error.message);
        
        // 记录错误
        const errorInfo = {
            component: name,
            description,
            error: error.message,
            required,
            timestamp: Date.now()
        };
        
        if (required) {
            aisp_initializationState.errors.push(errorInfo);
        } else {
            aisp_initializationState.warnings.push(errorInfo);
        }
        
        // 记录组件状态
        aisp_initializationState.components.set(name, {
            status: 'failed',
            required,
            description,
            error: error.message,
            initTime: Date.now()
        });
        
        // 如果是必需组件且不允许继续，则抛出错误
        if (required && !config.CONTINUE_ON_ERROR) {
            throw error;
        }
        
        return false;
    }
}
// #endregion

// #region 依赖管理和执行
/**
 * @function aisp_waitForDependencies - 等待依赖组件就绪
 * @description 等待指定的依赖组件初始化完成
 * @param {Array<string>} dependencies - 依赖组件名称数组
 * @param {Object} config - 配置对象
 * @returns {Promise<boolean>} 依赖是否全部就绪
 */
async function aisp_waitForDependencies(dependencies, config) {
    if (!dependencies || dependencies.length === 0) {
        return true;
    }

    const startTime = Date.now();
    const maxWaitTime = config.MAX_DEPENDENCY_WAIT;

    while (Date.now() - startTime < maxWaitTime) {
        const allReady = dependencies.every(dep => {
            const component = aisp_initializationState.components.get(dep);
            return component && component.status === 'initialized';
        });

        if (allReady) {
            return true;
        }

        // 等待一段时间后再检查
        await new Promise(resolve => setTimeout(resolve, config.DEPENDENCY_CHECK_INTERVAL));
    }

    // 记录未就绪的依赖
    const notReady = dependencies.filter(dep => {
        const component = aisp_initializationState.components.get(dep);
        return !component || component.status !== 'initialized';
    });

    console.warn(`⚠️ 依赖等待超时，未就绪的依赖: ${notReady.join(', ')}`);
    return false;
}

/**
 * @function aisp_executeComponentInitialization - 执行组件初始化
 * @description 执行组件的初始化函数，包含超时控制和重试机制
 * @param {string} componentName - 组件名称
 * @param {string} initFunction - 初始化函数名
 * @param {Object} config - 配置对象
 * @returns {Promise<boolean>} 是否初始化成功
 */
async function aisp_executeComponentInitialization(componentName, initFunction, config) {
    let lastError = null;

    for (let attempt = 1; attempt <= config.MAX_RETRIES; attempt++) {
        try {
            // 检查初始化函数是否存在
            if (typeof window[initFunction] !== 'function') {
                throw new Error(`初始化函数 ${initFunction} 不存在`);
            }

            // 执行初始化（带超时控制）
            const result = await aisp_executeWithTimeout(
                window[initFunction],
                [],
                config.COMPONENT_TIMEOUT
            );

            // 检查结果
            if (result === false) {
                throw new Error('初始化函数返回失败');
            }

            return true;

        } catch (error) {
            lastError = error;
            console.warn(`⚠️ 组件 ${componentName} 初始化失败 (尝试 ${attempt}/${config.MAX_RETRIES}):`, error.message);

            if (attempt < config.MAX_RETRIES) {
                const delay = config.RETRY_DELAY * attempt; // 递增延迟
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }

    throw lastError;
}

/**
 * @function aisp_executeWithTimeout - 带超时的函数执行
 * @description 执行函数并在超时时中断
 * @param {Function} fn - 要执行的函数
 * @param {Array} args - 函数参数
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {Promise<any>} 函数执行结果
 */
function aisp_executeWithTimeout(fn, args, timeout) {
    return new Promise((resolve, reject) => {
        const timer = setTimeout(() => {
            reject(new Error(`函数执行超时 (${timeout}ms)`));
        }, timeout);

        Promise.resolve(fn.apply(null, args))
            .then(result => {
                clearTimeout(timer);
                resolve(result);
            })
            .catch(error => {
                clearTimeout(timer);
                reject(error);
            });
    });
}
// #endregion

// #region 验证和状态管理
/**
 * @function aisp_validateCriticalComponents - 验证关键组件
 * @description 验证关键组件是否正确初始化并可用
 * @returns {Promise<Object>} 验证结果对象
 */
async function aisp_validateCriticalComponents() {
    const criticalComponents = ['logger', 'config-manager', 'gemini-api', 'chat-interface'];
    const validationResults = {};

    for (const componentName of criticalComponents) {
        const component = aisp_initializationState.components.get(componentName);

        if (!component) {
            validationResults[componentName] = {
                status: 'missing',
                message: '组件未找到'
            };
            continue;
        }

        if (component.status !== 'initialized') {
            validationResults[componentName] = {
                status: 'failed',
                message: component.error || '初始化失败'
            };
            continue;
        }

        // 执行功能验证
        try {
            const isWorking = await aisp_validateComponentFunction(componentName);
            validationResults[componentName] = {
                status: isWorking ? 'valid' : 'invalid',
                message: isWorking ? '功能正常' : '功能异常'
            };
        } catch (error) {
            validationResults[componentName] = {
                status: 'error',
                message: error.message
            };
        }
    }

    return validationResults;
}

/**
 * @function aisp_validateComponentFunction - 验证组件功能
 * @description 验证特定组件的功能是否正常
 * @param {string} componentName - 组件名称
 * @returns {Promise<boolean>} 功能是否正常
 */
async function aisp_validateComponentFunction(componentName) {
    switch (componentName) {
        case 'logger':
            return typeof aisp_logInfo === 'function';

        case 'config-manager':
            return typeof getConfigManager === 'function';

        case 'gemini-api':
            return typeof window.GeminiAPI !== 'undefined';

        case 'chat-interface':
            return document.querySelector('.chat-interface') !== null;

        default:
            return true; // 默认认为正常
    }
}

/**
 * @function aisp_getInitializationState - 获取初始化状态
 * @description 获取当前的初始化状态信息
 * @returns {Object} 初始化状态对象
 */
function aisp_getInitializationState() {
    return {
        ...aisp_initializationState,
        components: Object.fromEntries(aisp_initializationState.components),
        duration: aisp_initializationState.initializationEndTime
            ? aisp_initializationState.initializationEndTime - aisp_initializationState.initializationStartTime
            : null
    };
}

/**
 * @function aisp_isComponentInitialized - 检查组件是否已初始化
 * @description 检查指定组件是否已成功初始化
 * @param {string} componentName - 组件名称
 * @returns {boolean} 是否已初始化
 */
function aisp_isComponentInitialized(componentName) {
    const component = aisp_initializationState.components.get(componentName);
    return component && component.status === 'initialized';
}

/**
 * @function aisp_waitForComponent - 等待组件初始化完成
 * @description 等待指定组件初始化完成
 * @param {string} componentName - 组件名称
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {Promise<boolean>} 是否成功等待到组件初始化
 */
function aisp_waitForComponent(componentName, timeout = 10000) {
    return new Promise((resolve) => {
        const startTime = Date.now();

        const checkComponent = () => {
            if (aisp_isComponentInitialized(componentName)) {
                resolve(true);
                return;
            }

            if (Date.now() - startTime >= timeout) {
                resolve(false);
                return;
            }

            setTimeout(checkComponent, 100);
        };

        checkComponent();
    });
}
// #endregion

console.log('🔧 AI Side Panel 初始化管理器已加载');
