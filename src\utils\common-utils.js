/**
 * @file AI Side Panel 通用工具函数
 * @description 提供项目中常用的工具函数和辅助方法
 */

// #region 防抖和节流函数
/**
 * @function util_debounce - 防抖函数
 * @description 在指定时间内只执行最后一次调用
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @param {boolean} immediate - 是否立即执行
 * @returns {Function} 防抖后的函数
 */
function util_debounce(func, wait, immediate = false) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func.apply(this, args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(this, args);
    };
}

/**
 * @function util_throttle - 节流函数
 * @description 在指定时间内最多执行一次
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 时间限制（毫秒）
 * @returns {Function} 节流后的函数
 */
function util_throttle(func, limit) {
    let inThrottle;
    return function executedFunction(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}
// #endregion

// #region 字符串处理函数
/**
 * @function util_truncateText - 截断文本
 * @description 截断文本并添加省略号
 * @param {string} text - 原始文本
 * @param {number} maxLength - 最大长度
 * @param {string} suffix - 后缀（默认为'...'）
 * @returns {string} 截断后的文本
 */
function util_truncateText(text, maxLength, suffix = '...') {
    if (!text || typeof text !== 'string') return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - suffix.length) + suffix;
}

/**
 * @function util_escapeHtml - 转义HTML字符
 * @description 转义HTML中的特殊字符
 * @param {string} text - 要转义的文本
 * @returns {string} 转义后的文本
 */
function util_escapeHtml(text) {
    if (!text || typeof text !== 'string') return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * @function util_unescapeHtml - 反转义HTML字符
 * @description 反转义HTML字符
 * @param {string} html - 要反转义的HTML
 * @returns {string} 反转义后的文本
 */
function util_unescapeHtml(html) {
    if (!html || typeof html !== 'string') return '';
    const div = document.createElement('div');
    div.innerHTML = html;
    return div.textContent || div.innerText || '';
}

/**
 * @function util_generateId - 生成唯一ID
 * @description 生成基于时间戳和随机数的唯一ID
 * @param {string} prefix - ID前缀
 * @returns {string} 唯一ID
 */
function util_generateId(prefix = 'id') {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
// #endregion

// #region 日期时间函数
/**
 * @function util_formatDate - 格式化日期
 * @description 将日期格式化为指定格式
 * @param {Date|string|number} date - 日期对象、字符串或时间戳
 * @param {string} format - 格式字符串（默认：'YYYY-MM-DD HH:mm:ss'）
 * @returns {string} 格式化后的日期字符串
 */
function util_formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
    const d = new Date(date);
    if (isNaN(d.getTime())) return '';
    
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');
    
    return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
}

/**
 * @function util_getRelativeTime - 获取相对时间
 * @description 获取相对于当前时间的描述
 * @param {Date|string|number} date - 日期
 * @returns {string} 相对时间描述
 */
function util_getRelativeTime(date) {
    const now = new Date();
    const target = new Date(date);
    const diffMs = now - target;
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);
    
    if (diffSec < 60) return '刚刚';
    if (diffMin < 60) return `${diffMin}分钟前`;
    if (diffHour < 24) return `${diffHour}小时前`;
    if (diffDay < 7) return `${diffDay}天前`;
    
    return util_formatDate(target, 'MM-DD');
}
// #endregion

// #region DOM操作函数
/**
 * @function util_createElement - 创建DOM元素
 * @description 创建带有属性和内容的DOM元素
 * @param {string} tagName - 标签名
 * @param {Object} attributes - 属性对象
 * @param {string|Node|Array} content - 内容
 * @returns {Element} 创建的DOM元素
 */
function util_createElement(tagName, attributes = {}, content = '') {
    const element = document.createElement(tagName);
    
    // 设置属性
    Object.entries(attributes).forEach(([key, value]) => {
        if (key === 'className') {
            element.className = value;
        } else if (key === 'style' && typeof value === 'object') {
            Object.assign(element.style, value);
        } else {
            element.setAttribute(key, value);
        }
    });
    
    // 设置内容
    if (typeof content === 'string') {
        element.textContent = content;
    } else if (content instanceof Node) {
        element.appendChild(content);
    } else if (Array.isArray(content)) {
        content.forEach(child => {
            if (typeof child === 'string') {
                element.appendChild(document.createTextNode(child));
            } else if (child instanceof Node) {
                element.appendChild(child);
            }
        });
    }
    
    return element;
}

/**
 * @function util_findParent - 查找父元素
 * @description 向上查找匹配选择器的父元素
 * @param {Element} element - 起始元素
 * @param {string} selector - CSS选择器
 * @returns {Element|null} 匹配的父元素或null
 */
function util_findParent(element, selector) {
    let current = element.parentElement;
    while (current) {
        if (current.matches(selector)) {
            return current;
        }
        current = current.parentElement;
    }
    return null;
}

/**
 * @function util_isElementVisible - 检查元素是否可见
 * @description 检查元素是否在视口中可见
 * @param {Element} element - 要检查的元素
 * @returns {boolean} 是否可见
 */
function util_isElementVisible(element) {
    if (typeof window === 'undefined' || typeof document === 'undefined') {
        return false;
    }

    const rect = element.getBoundingClientRect();
    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
}
// #endregion

// #region 数据处理函数
/**
 * @function util_deepClone - 深度克隆对象
 * @description 深度克隆对象或数组
 * @param {any} obj - 要克隆的对象
 * @returns {any} 克隆后的对象
 */
function util_deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map(item => util_deepClone(item));
    if (typeof obj === 'object') {
        const cloned = {};
        Object.keys(obj).forEach(key => {
            cloned[key] = util_deepClone(obj[key]);
        });
        return cloned;
    }
    return obj;
}

/**
 * @function util_mergeObjects - 合并对象
 * @description 深度合并多个对象
 * @param {...Object} objects - 要合并的对象
 * @returns {Object} 合并后的对象
 */
function util_mergeObjects(...objects) {
    const result = {};
    
    objects.forEach(obj => {
        if (obj && typeof obj === 'object') {
            Object.keys(obj).forEach(key => {
                if (obj[key] && typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
                    result[key] = util_mergeObjects(result[key] || {}, obj[key]);
                } else {
                    result[key] = obj[key];
                }
            });
        }
    });
    
    return result;
}

/**
 * @function util_arrayToObject - 数组转对象
 * @description 将数组转换为以指定属性为键的对象
 * @param {Array} array - 源数组
 * @param {string} keyField - 作为键的字段名
 * @returns {Object} 转换后的对象
 */
function util_arrayToObject(array, keyField) {
    if (!Array.isArray(array)) return {};
    
    return array.reduce((obj, item) => {
        if (item && typeof item === 'object' && item[keyField]) {
            obj[item[keyField]] = item;
        }
        return obj;
    }, {});
}
// #endregion

// #region 验证函数
/**
 * @function util_isValidEmail - 验证邮箱格式
 * @description 验证邮箱地址格式是否正确
 * @param {string} email - 邮箱地址
 * @returns {boolean} 是否为有效邮箱
 */
function util_isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * @function util_isValidUrl - 验证URL格式
 * @description 验证URL格式是否正确
 * @param {string} url - URL地址
 * @returns {boolean} 是否为有效URL
 */
function util_isValidUrl(url) {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

/**
 * @function util_isEmpty - 检查值是否为空
 * @description 检查值是否为空（null、undefined、空字符串、空数组、空对象）
 * @param {any} value - 要检查的值
 * @returns {boolean} 是否为空
 */
function util_isEmpty(value) {
    if (value === null || value === undefined) return true;
    if (typeof value === 'string') return value.trim() === '';
    if (Array.isArray(value)) return value.length === 0;
    if (typeof value === 'object') return Object.keys(value).length === 0;
    return false;
}
// #endregion

// #region 错误处理函数
/**
 * @function util_safeExecute - 安全执行函数
 * @description 安全执行函数，捕获并处理错误
 * @param {Function} func - 要执行的函数
 * @param {any} defaultValue - 出错时的默认返回值
 * @param {Function} errorHandler - 错误处理函数
 * @returns {any} 函数执行结果或默认值
 */
function util_safeExecute(func, defaultValue = null, errorHandler = null) {
    try {
        return func();
    } catch (error) {
        if (errorHandler && typeof errorHandler === 'function') {
            errorHandler(error);
        } else {
            console.error('Safe execute error:', error);
        }
        return defaultValue;
    }
}

/**
 * @function util_retry - 重试函数
 * @description 重试执行异步函数
 * @param {Function} func - 要重试的异步函数
 * @param {number} maxRetries - 最大重试次数
 * @param {number} delay - 重试间隔（毫秒）
 * @returns {Promise} 执行结果
 */
async function util_retry(func, maxRetries = 3, delay = 1000) {
    let lastError;
    
    for (let i = 0; i <= maxRetries; i++) {
        try {
            return await func();
        } catch (error) {
            lastError = error;
            if (i < maxRetries) {
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }
    
    throw lastError;
}
// #endregion

console.log('AI Side Panel Common Utils 已加载');
