/**
 * @file AI Side Panel 侧边栏主脚本
 * @description 处理侧边栏的主要逻辑，包括界面初始化、用户交互、AI分析展示等
 */

// #region 日志系统初始化
// 创建侧边栏专用日志记录器
let sidepanelLogger = null;

/**
 * @function aisp_initializeSidepanelLogger - 初始化侧边栏日志系统
 * @description 创建适用于侧边栏的日志记录器
 */
function aisp_initializeSidepanelLogger() {
    // 检查全局日志系统是否可用
    if (typeof aisp_logCreateLogger === 'function') {
        sidepanelLogger = aisp_logCreateLogger('sidepanel');
    } else {
        // 创建简化的日志记录器
        sidepanelLogger = {
            error: (message, data = null) => console.error(`[AISP-SIDEPANEL] [ERROR] ${message}`, data),
            warn: (message, data = null) => console.warn(`[AISP-SIDEPANEL] [WARN] ${message}`, data),
            info: (message, data = null) => console.info(`[AISP-SIDEPANEL] [INFO] ${message}`, data),
            debug: (message, data = null) => console.log(`[AISP-SIDEPANEL] [DEBUG] ${message}`, data),
            performance: (operation, duration, data = {}) => {
                console.log(`[AISP-SIDEPANEL] [PERF] ${operation}: ${duration}ms`, data);
            },
            userAction: (action, details = {}) => {
                console.log(`[AISP-SIDEPANEL] [USER] ${action}`, details);
            },
            functionEntry: (functionName, params = {}) => {
                console.log(`[AISP-SIDEPANEL] [ENTRY] ${functionName}`, params);
            },
            functionExit: (functionName, result = {}) => {
                console.log(`[AISP-SIDEPANEL] [EXIT] ${functionName}`, result);
            },
            withTimer: async (operation, fn) => {
                const startTime = Date.now();
                try {
                    const result = await fn();
                    const duration = Date.now() - startTime;
                    sidepanelLogger.performance(operation, duration, { success: true });
                    return result;
                } catch (error) {
                    const duration = Date.now() - startTime;
                    sidepanelLogger.error(`${operation} 失败`, { error: error.message, duration });
                    throw error;
                }
            }
        };
    }
}

/**
 * @function aisp_logSidepanelUserAction - 记录侧边栏用户交互
 * @param {string} action - 交互动作
 * @param {Object} details - 交互详情
 */
function aisp_logSidepanelUserAction(action, details = {}) {
    try {
        const interactionData = {
            action,
            timestamp: Date.now(),
            context: 'sidepanel',
            url: window.location.href,
            ...details
        };

        // 发送到增强日志系统
        chrome.runtime.sendMessage({
            action: 'aisp_runtime_data_sync',
            data: {
                type: 'AISP_RUNTIME_DATA',
                dataType: 'user_action',
                timestamp: Date.now(),
                data: interactionData,
                context: 'sidepanel'
            }
        }).catch(() => {
            // 忽略发送失败
        });

        // 同时使用原有日志系统
        if (sidepanelLogger && sidepanelLogger.userAction) {
            sidepanelLogger.userAction(action, details);
        }
    } catch (error) {
        // 忽略错误
    }
}

/**
 * @function aisp_logSidepanelPerformance - 记录侧边栏性能指标
 * @param {string} operation - 操作名称
 * @param {number} duration - 持续时间
 * @param {Object} data - 附加数据
 */
function aisp_logSidepanelPerformance(operation, duration, data = {}) {
    try {
        const performanceData = {
            operation,
            duration,
            timestamp: Date.now(),
            context: 'sidepanel',
            ...data
        };

        // 发送到增强日志系统
        chrome.runtime.sendMessage({
            action: 'aisp_runtime_data_sync',
            data: {
                type: 'AISP_RUNTIME_DATA',
                dataType: 'performance',
                timestamp: Date.now(),
                data: performanceData,
                context: 'sidepanel'
            }
        }).catch(() => {
            // 忽略发送失败
        });

        // 同时使用原有日志系统
        if (sidepanelLogger && sidepanelLogger.performance) {
            sidepanelLogger.performance(operation, duration, data);
        }
    } catch (error) {
        // 忽略错误
    }
}

// 立即初始化日志系统
aisp_initializeSidepanelLogger();
// #endregion

// #region 全局变量
let aisp_currentLanguage = 'zh_CN';
let aisp_isAnalyzing = false;
let aisp_currentPageData = null;
let aisp_chatInterface = null;
let aisp_apiManager = null;
let aisp_configManager = null;
let aisp_isInitialized = false;
let aisp_messageHistory = [];
let aisp_analysisCache = new Map();
let aisp_replyGenerator = null;
let aisp_replySuggestions = null;
let aisp_templateManager = null;
let aisp_templatePopup = null;
let aisp_googleDriveAPI = null;
let aisp_knowledgeBase = null;
let aisp_cacheManager = null;
let aisp_performanceOptimizer = null;
let aisp_testFramework = null;
let aisp_performanceMonitor = null;
// #endregion

// #region 初始化
/**
 * @function aisp_initializeSidePanel - 初始化侧边栏
 * @description 设置事件监听器，加载配置，初始化界面
 */
async function aisp_initializeSidePanel() {
    if (aisp_isInitialized) return;

    const initStartTime = Date.now();

    try {
        await sidepanelLogger.functionEntry('aisp_initializeSidePanel');
        sidepanelLogger.info('AI Side Panel 侧边栏开始初始化');

        // 初始化多语言支持
        await sidepanelLogger.withTimer('多语言支持初始化', () => aisp_initializeLanguage());

        // 初始化API管理器
        await sidepanelLogger.withTimer('API管理器初始化', () => aisp_initializeAPIManager());

        // 加载配置
        await sidepanelLogger.withTimer('配置加载', () => aisp_loadConfiguration());

        // 初始化界面组件
        await sidepanelLogger.withTimer('事件监听器设置', () => aisp_setupEventListeners());

        // 初始化聊天界面
        if (typeof ui_ChatInterface !== 'undefined') {
            aisp_chatInterface = new ui_ChatInterface('aisp-message-list');
            sidepanelLogger.info('使用完整版聊天界面组件');
        } else {
            sidepanelLogger.warn('ChatInterface 组件未加载，使用简化版本');
            aisp_chatInterface = aisp_createSimpleChatInterface();
        }

        // 初始化回复语言选择器
        await sidepanelLogger.withTimer('回复语言选择器初始化', () => aisp_initializeReplyLanguageSelector());

        // 初始化回复建议组件
        await sidepanelLogger.withTimer('回复建议组件初始化', () => aisp_initializeReplySuggestions());

        // 初始化模板系统
        await sidepanelLogger.withTimer('模板系统初始化', () => aisp_initializeTemplateSystem());

        // 初始化Google Drive知识库
        await sidepanelLogger.withTimer('知识库初始化', () => aisp_initializeKnowledgeBase());

        // 初始化性能优化系统
        await sidepanelLogger.withTimer('性能系统初始化', () => aisp_initializePerformanceSystem());

        // 初始化调试面板
        await sidepanelLogger.withTimer('调试面板初始化', () => aisp_initializeDebugPanel());

        // 初始化内容卡片系统
        await sidepanelLogger.withTimer('内容卡片系统初始化', () => aisp_initializeContentCards());

        // 加载当前页面信息
        await sidepanelLogger.withTimer('页面信息加载', () => aisp_loadCurrentPageInfo());

        // 更新状态显示 - 从service worker获取最新状态
        await sidepanelLogger.withTimer('连接状态更新', () => aisp_updateConnectionStatus());

        // 自动分析当前页面（替代手动分析按钮）
        await sidepanelLogger.withTimer('自动页面分析', () => aisp_autoAnalyzeCurrentPage());

        aisp_isInitialized = true;

        // 设置定期连接状态检查
        aisp_setupConnectionStatusMonitor();

        const totalInitTime = Date.now() - initStartTime;
        sidepanelLogger.performance('侧边栏完整初始化', totalInitTime, { success: true });
        sidepanelLogger.info('AI Side Panel 侧边栏初始化完成', {
            totalTime: totalInitTime
        });

        await sidepanelLogger.functionExit('aisp_initializeSidePanel', {
            success: true,
            duration: totalInitTime
        });

    } catch (error) {
        const totalInitTime = Date.now() - initStartTime;
        sidepanelLogger.error('侧边栏初始化失败', {
            error: error.message,
            stack: error.stack,
            duration: totalInitTime
        });

        const errorMessage = typeof lang_getErrorText === 'function'
            ? lang_getErrorText('initializationFailed')
            : '初始化失败，请刷新重试';
        aisp_showError(errorMessage + ': ' + error.message);

        await sidepanelLogger.functionExit('aisp_initializeSidePanel', {
            success: false,
            error: error.message,
            duration: totalInitTime
        });
    }
}

/**
 * @function aisp_initializeLanguage - 初始化多语言支持
 * @description 加载语言资源并设置当前语言
 */
async function aisp_initializeLanguage() {
    try {
        // 检查language-manager是否可用
        if (typeof lang_initialize !== 'function') {
            console.warn('⚠️ Language Manager 未加载，跳过多语言初始化');
            return;
        }

        // 获取保存的语言设置
        const result = await chrome.storage.local.get('aisp_config');
        const config = result.aisp_config || {};
        const savedLanguage = config.currentLanguage || 'zh_CN';

        // 初始化语言管理器
        await lang_initialize(savedLanguage);

        // 添加语言变更监听器
        lang_addChangeListener((newLang, oldLang) => {
            console.log(`🌐 侧边栏语言已切换: ${oldLang} -> ${newLang}`);
            // 更新连接状态显示（使用新语言）
            aisp_updateConnectionStatus();
        });

        console.log(`✅ 侧边栏多语言支持初始化完成，当前语言: ${savedLanguage}`);
    } catch (error) {
        console.error('❌ 侧边栏多语言初始化失败:', error);
    }
}

/**
 * @function aisp_initializeReplyLanguageSelector - 初始化回复语言选择器
 * @description 初始化AI回复语言选择器组件
 */
async function aisp_initializeReplyLanguageSelector() {
    try {
        // 检查回复语言选择器组件是否可用
        if (typeof rls_initialize !== 'function') {
            console.warn('⚠️ 回复语言选择器组件未加载，跳过初始化');
            return;
        }

        // 初始化回复语言选择器
        await rls_initialize();

        // 查找语言选择器容器
        const selectorContainer = document.getElementById('aisp-reply-language-selector');
        if (!selectorContainer) {
            console.warn('⚠️ 回复语言选择器容器未找到，跳过UI初始化');
            return;
        }

        // 创建紧凑型语言选择器
        const languageSelector = rls_createCompactSelector({
            className: 'aisp-reply-language-selector',
            showIcons: true,
            onChange: (language, success) => {
                if (success) {
                    console.log(`🌐 回复语言已切换为: ${language}`);

                    // 更新回复建议组件的语言
                    if (aisp_replySuggestions && typeof aisp_replySuggestions.updateLanguage === 'function') {
                        aisp_replySuggestions.updateLanguage(language);
                    }

                    // 更新模板系统的语言
                    if (aisp_templatePopup && typeof aisp_templatePopup.updateLanguage === 'function') {
                        aisp_templatePopup.updateLanguage(language);
                    }

                    // 显示切换成功消息
                    const languageName = rls_getLanguageName(language);
                    aisp_addChatMessage(`🌐 回复语言已切换为: ${languageName}`, 'system');
                } else {
                    console.error('回复语言切换失败');
                    aisp_addChatMessage('❌ 回复语言切换失败', 'system');
                }
            }
        });

        if (languageSelector) {
            selectorContainer.appendChild(languageSelector);
            console.log('✅ 回复语言选择器UI初始化完成');
        }

        console.log('✅ 回复语言选择器初始化完成');

    } catch (error) {
        console.error('❌ 回复语言选择器初始化失败:', error);
    }
}

/**
 * @function aisp_initializeReplySuggestions - 初始化回复建议组件
 * @description 初始化智能回复建议系统
 */
async function aisp_initializeReplySuggestions() {
    try {
        // 检查回复建议组件是否可用
        if (typeof ReplySuggestions === 'undefined') {
            console.warn('⚠️ 回复建议组件未加载，跳过初始化');
            return;
        }

        // 查找回复建议容器
        const replySuggestionsContainer = document.getElementById('aisp-reply-suggestions');
        if (!replySuggestionsContainer) {
            console.warn('⚠️ 回复建议容器未找到，跳过初始化');
            return;
        }

        // 初始化回复建议组件
        aisp_replySuggestions = new ReplySuggestions(replySuggestionsContainer, {
            language: aisp_currentLanguage,
            showCopyButton: true,
            showTypeLabels: true,
            enableSelection: true,
            onReplySelect: (reply, index) => {
                console.log('选择了回复:', reply);
                // 可以在这里添加选择回复后的处理逻辑
            },
            onReplyCopy: (content) => {
                console.log('复制了回复:', content);
                aisp_addChatMessage('✅ 回复已复制到剪贴板', 'system');
            }
        });

        // 添加语言变更监听器
        if (typeof lang_addChangeListener === 'function') {
            lang_addChangeListener((newLang, oldLang) => {
                if (aisp_replySuggestions) {
                    aisp_replySuggestions.updateLanguage(newLang);
                }
            });
        }

        console.log('✅ 回复建议组件初始化完成');

    } catch (error) {
        console.error('❌ 回复建议组件初始化失败:', error);
    }
}

/**
 * @function aisp_initializeTemplateSystem - 初始化模板系统
 * @description 初始化快捷回复模板系统
 */
async function aisp_initializeTemplateSystem() {
    try {
        // 检查模板管理器是否可用
        if (typeof getTemplateManager === 'function') {
            aisp_templateManager = getTemplateManager();
            await aisp_templateManager.initialize();
        } else {
            console.warn('⚠️ 模板管理器未加载，跳过初始化');
            return;
        }

        // 检查模板弹窗组件是否可用
        if (typeof TemplatePopup === 'undefined') {
            console.warn('⚠️ 模板弹窗组件未加载，跳过初始化');
            return;
        }

        // 查找输入框
        const inputElement = document.getElementById('aisp-user-input');
        if (!inputElement) {
            console.warn('⚠️ 用户输入框未找到，跳过模板弹窗初始化');
            return;
        }

        // 初始化模板弹窗
        aisp_templatePopup = new TemplatePopup(inputElement, {
            language: aisp_currentLanguage,
            maxTemplates: 8,
            showPreview: true,
            enableSearch: true,
            enableKeyboard: true,
            autoShow: false, // 手动控制显示
            position: 'top',
            onTemplateSelect: (template) => {
                console.log('选择了模板:', template.title);
            },
            onTemplateInsert: (template) => {
                console.log('插入了模板:', template.title);
                aisp_addChatMessage(`📋 已插入模板: ${template.title}`, 'system');

                // 记录模板使用
                aisp_recordTemplateUsage(template);
            }
        });

        // 添加语言变更监听器
        if (typeof lang_addChangeListener === 'function') {
            lang_addChangeListener((newLang, oldLang) => {
                if (aisp_templatePopup) {
                    aisp_templatePopup.updateLanguage(newLang);
                }
            });
        }

        // 添加快捷键支持
        aisp_setupTemplateShortcuts();

        console.log('✅ 模板系统初始化完成');

    } catch (error) {
        console.error('❌ 模板系统初始化失败:', error);
    }
}

/**
 * @function aisp_setupTemplateShortcuts - 设置模板快捷键
 * @description 设置模板系统的快捷键
 */
function aisp_setupTemplateShortcuts() {
    const inputElement = document.getElementById('aisp-user-input');
    if (!inputElement) return;

    // 添加快捷键监听
    inputElement.addEventListener('keydown', (event) => {
        // Ctrl/Cmd + T 显示模板弹窗
        if ((event.ctrlKey || event.metaKey) && event.key === 't') {
            event.preventDefault();
            if (aisp_templatePopup) {
                aisp_templatePopup.show();
            }
        }

        // Ctrl/Cmd + Shift + T 智能预测模板
        if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'T') {
            event.preventDefault();
            aisp_showPredictedTemplates();
        }
    });
}

/**
 * @function aisp_showPredictedTemplates - 显示预测模板
 * @description 基于当前输入显示智能预测的模板
 */
async function aisp_showPredictedTemplates() {
    try {
        const inputElement = document.getElementById('aisp-user-input');
        if (!inputElement || !aisp_templateManager) return;

        const inputText = inputElement.value;
        if (!inputText.trim()) {
            // 如果没有输入，显示所有模板
            if (aisp_templatePopup) {
                aisp_templatePopup.show();
            }
            return;
        }

        // 获取页面上下文
        const pageContent = await aisp_getCurrentPageContent();
        const context = {
            pageTitle: pageContent.title,
            pageUrl: pageContent.url,
            inputText: inputText
        };

        // 智能预测模板
        const predictedTemplates = await aisp_templateManager.predictTemplates(inputText, context);

        if (predictedTemplates.length > 0) {
            console.log(`🔮 预测到 ${predictedTemplates.length} 个相关模板`);

            // 显示模板弹窗
            if (aisp_templatePopup) {
                aisp_templatePopup.show();
            }

            aisp_addChatMessage(`🔮 为您预测了 ${predictedTemplates.length} 个相关模板`, 'system');
        } else {
            aisp_addChatMessage('🔮 未找到相关模板建议', 'system');
        }

    } catch (error) {
        console.error('智能预测模板失败:', error);
        aisp_addChatMessage('❌ 模板预测失败', 'system');
    }
}

/**
 * @function aisp_recordTemplateUsage - 记录模板使用
 * @description 记录模板的使用情况，用于优化推荐
 * @param {Object} template - 使用的模板
 */
function aisp_recordTemplateUsage(template) {
    try {
        // 更新模板的使用时间
        if (aisp_templateManager) {
            aisp_templateManager.updateTemplate(template.id, {
                lastUsed: Date.now(),
                useCount: (template.useCount || 0) + 1
            });
        }

        console.log('📊 模板使用已记录:', template.title);

    } catch (error) {
        console.error('记录模板使用失败:', error);
    }
}

/**
 * @function aisp_initializeKnowledgeBase - 初始化Google Drive知识库
 * @description 初始化Google Drive知识库系统
 */
async function aisp_initializeKnowledgeBase() {
    try {
        // 检查Google Drive API是否可用
        if (typeof getGoogleDriveAPI === 'function') {
            aisp_googleDriveAPI = getGoogleDriveAPI();
            await aisp_googleDriveAPI.initialize();
        } else {
            console.warn('⚠️ Google Drive API未加载，跳过初始化');
            return;
        }

        // 检查知识库组件是否可用
        if (typeof KnowledgeBase === 'undefined') {
            console.warn('⚠️ 知识库组件未加载，跳过初始化');
            return;
        }

        // 查找知识库容器
        const knowledgeBaseContainer = document.getElementById('aisp-knowledge-base');
        if (!knowledgeBaseContainer) {
            console.warn('⚠️ 知识库容器未找到，跳过初始化');
            return;
        }

        // 初始化知识库组件
        aisp_knowledgeBase = new KnowledgeBase(knowledgeBaseContainer, {
            language: aisp_currentLanguage,
            showSyncButton: true,
            showAuthButton: true,
            showStorageInfo: true,
            autoSync: false, // 默认不自动同步
            syncInterval: 300000, // 5分钟
            onSyncComplete: (syncResult) => {
                console.log('知识库同步完成:', syncResult);
                aisp_addChatMessage(`☁️ 知识库同步完成: 模板${syncResult.templates.uploaded}个上传`, 'system');
            },
            onAuthComplete: () => {
                console.log('Google Drive认证完成');
                aisp_addChatMessage('☁️ Google Drive认证成功', 'system');
            },
            onError: (error) => {
                console.error('知识库错误:', error);
                aisp_addChatMessage(`❌ 知识库错误: ${error.message}`, 'system');
            }
        });

        // 添加语言变更监听器
        if (typeof lang_addChangeListener === 'function') {
            lang_addChangeListener((newLang, oldLang) => {
                if (aisp_knowledgeBase) {
                    aisp_knowledgeBase.updateLanguage(newLang);
                }
            });
        }

        console.log('✅ Google Drive知识库初始化完成');

    } catch (error) {
        console.error('❌ Google Drive知识库初始化失败:', error);
    }
}

/**
 * @function aisp_syncKnowledgeBase - 同步知识库
 * @description 手动触发知识库同步
 */
async function aisp_syncKnowledgeBase() {
    try {
        if (!aisp_knowledgeBase) {
            throw new Error('知识库组件未初始化');
        }

        const status = aisp_knowledgeBase.getStatus();
        if (!status.authStatus || !status.authStatus.isAuthenticated) {
            throw new Error('请先认证Google Drive');
        }

        aisp_addChatMessage('🔄 开始同步知识库...', 'system');

        // 触发同步
        await aisp_knowledgeBase.handleSync();

    } catch (error) {
        console.error('手动同步知识库失败:', error);
        aisp_addChatMessage(`❌ 同步失败: ${error.message}`, 'system');
    }
}

/**
 * @function aisp_authenticateGoogleDrive - 认证Google Drive
 * @description 手动触发Google Drive认证
 */
async function aisp_authenticateGoogleDrive() {
    try {
        if (!aisp_knowledgeBase) {
            throw new Error('知识库组件未初始化');
        }

        aisp_addChatMessage('🔐 开始Google Drive认证...', 'system');

        // 触发认证
        await aisp_knowledgeBase.handleAuth();

    } catch (error) {
        console.error('Google Drive认证失败:', error);
        aisp_addChatMessage(`❌ 认证失败: ${error.message}`, 'system');
    }
}

/**
 * @function aisp_getKnowledgeBaseStatus - 获取知识库状态
 * @description 获取Google Drive知识库状态信息
 * @returns {Object} 知识库状态
 */
function aisp_getKnowledgeBaseStatus() {
    if (!aisp_knowledgeBase) {
        return {
            available: false,
            message: '知识库组件未初始化'
        };
    }

    const status = aisp_knowledgeBase.getStatus();

    return {
        available: true,
        isInitialized: status.isInitialized,
        isAuthenticated: status.authStatus ? status.authStatus.isAuthenticated : false,
        isSyncing: status.isSyncing,
        lastSync: status.syncStatus ? status.syncStatus.lastSync : null,
        autoSyncEnabled: status.autoSyncEnabled
    };
}

/**
 * @function aisp_initializePerformanceSystem - 初始化性能优化系统
 * @description 初始化缓存管理器、性能优化器、测试框架和性能监控
 */
async function aisp_initializePerformanceSystem() {
    try {
        // 初始化缓存管理器
        if (typeof getCacheManager === 'function') {
            aisp_cacheManager = getCacheManager();
            await aisp_cacheManager.initialize();
            console.log('✅ 缓存管理器初始化完成');
        } else {
            console.warn('⚠️ 缓存管理器未加载，跳过初始化');
        }

        // 初始化性能优化器
        if (typeof getPerformanceOptimizer === 'function') {
            aisp_performanceOptimizer = getPerformanceOptimizer();
            await aisp_performanceOptimizer.initialize();
            console.log('✅ 性能优化器初始化完成');
        } else {
            console.warn('⚠️ 性能优化器未加载，跳过初始化');
        }

        // 初始化测试框架
        if (typeof getTestFramework === 'function') {
            aisp_testFramework = getTestFramework();
            await aisp_testFramework.initialize();
            console.log('✅ 测试框架初始化完成');
        } else {
            console.warn('⚠️ 测试框架未加载，跳过初始化');
        }

        // 初始化性能监控组件
        await aisp_initializePerformanceMonitor();

        // 优化现有API调用
        aisp_optimizeExistingApis();

        console.log('✅ 性能优化系统初始化完成');

    } catch (error) {
        console.error('❌ 性能优化系统初始化失败:', error);
    }
}

/**
 * @function aisp_initializePerformanceMonitor - 初始化性能监控组件
 * @description 初始化性能监控界面组件
 */
async function aisp_initializePerformanceMonitor() {
    try {
        // 检查性能监控组件是否可用
        if (typeof PerformanceMonitor === 'undefined') {
            console.warn('⚠️ 性能监控组件未加载，跳过初始化');
            return;
        }

        // 查找性能监控容器
        const performanceMonitorContainer = document.getElementById('aisp-performance-monitor');
        if (!performanceMonitorContainer) {
            console.warn('⚠️ 性能监控容器未找到，跳过初始化');
            return;
        }

        // 初始化性能监控组件
        aisp_performanceMonitor = new PerformanceMonitor(performanceMonitorContainer, {
            language: aisp_currentLanguage,
            updateInterval: 5000,      // 5秒更新间隔
            showDetailedMetrics: true,
            enableAutoTest: false,     // 默认不自动测试
            testInterval: 300000,      // 5分钟测试间隔
            onMetricsUpdate: (metrics) => {
                console.log('📊 性能指标已更新:', metrics);
            },
            onTestComplete: (testResults) => {
                console.log('🧪 测试完成:', testResults);
                aisp_addChatMessage(`🧪 测试完成: 总计${testResults.summary.functional.total + testResults.summary.compatibility.total + testResults.summary.performance.total + testResults.summary.userExperience.total}项`, 'system');
            },
            onError: (error) => {
                console.error('性能监控错误:', error);
                aisp_addChatMessage(`❌ 性能监控错误: ${error.message}`, 'system');
            }
        });

        // 添加语言变更监听器
        if (typeof lang_addChangeListener === 'function') {
            lang_addChangeListener((newLang, oldLang) => {
                if (aisp_performanceMonitor) {
                    aisp_performanceMonitor.updateLanguage(newLang);
                }
            });
        }

        console.log('✅ 性能监控组件初始化完成');

    } catch (error) {
        console.error('❌ 性能监控组件初始化失败:', error);
    }
}

/**
 * @function aisp_optimizeExistingApis - 优化现有API调用
 * @description 为现有的API调用添加性能优化
 */
function aisp_optimizeExistingApis() {
    try {
        if (!aisp_performanceOptimizer) return;

        // 优化Gemini API调用
        if (aisp_geminiApi && aisp_geminiApi.generateContent) {
            const originalGenerateContent = aisp_geminiApi.generateContent;
            aisp_geminiApi.generateContent = aisp_performanceOptimizer.optimizeApiCall(
                originalGenerateContent.bind(aisp_geminiApi),
                {
                    cacheType: 'api_responses',
                    cacheTTL: 300000, // 5分钟缓存
                    enableDeduplication: true,
                    enableRetry: true
                }
            );
        }

        // 优化Google Drive API调用
        if (aisp_googleDriveAPI && aisp_googleDriveAPI.listFiles) {
            const originalListFiles = aisp_googleDriveAPI.listFiles;
            aisp_googleDriveAPI.listFiles = aisp_performanceOptimizer.optimizeApiCall(
                originalListFiles.bind(aisp_googleDriveAPI),
                {
                    cacheType: 'file_lists',
                    cacheTTL: 180000, // 3分钟缓存
                    enableDeduplication: true
                }
            );
        }

        // 优化模板管理器调用
        if (aisp_templateManager && aisp_templateManager.getTemplates) {
            const originalGetTemplates = aisp_templateManager.getTemplates;
            aisp_templateManager.getTemplates = aisp_performanceOptimizer.optimizeApiCall(
                originalGetTemplates.bind(aisp_templateManager),
                {
                    cacheType: 'templates',
                    cacheTTL: 600000, // 10分钟缓存
                    enableDeduplication: true
                }
            );
        }

        console.log('✅ 现有API调用已优化');

    } catch (error) {
        console.error('优化现有API调用失败:', error);
    }
}

/**
 * @function aisp_runPerformanceTests - 运行性能测试
 * @description 手动触发性能测试
 */
async function aisp_runPerformanceTests() {
    try {
        if (!aisp_testFramework) {
            throw new Error('测试框架未初始化');
        }

        aisp_addChatMessage('🧪 开始运行性能测试...', 'system');

        const testResults = await aisp_testFramework.runAllTests();

        // 生成测试报告
        const report = aisp_testFramework.generateTestReport(testResults);
        console.log('📋 测试报告:\n', report);

        // 显示测试结果摘要
        const summary = testResults.summary;
        const totalTests = Object.values(summary).reduce((sum, s) => sum + s.total, 0);
        const totalPassed = Object.values(summary).reduce((sum, s) => sum + s.passed, 0);
        const totalFailed = Object.values(summary).reduce((sum, s) => sum + s.failed, 0);

        aisp_addChatMessage(`🧪 测试完成: ${totalPassed}通过/${totalTests}总计 (${totalFailed}失败)`, 'system');

        return testResults;

    } catch (error) {
        console.error('运行性能测试失败:', error);
        aisp_addChatMessage(`❌ 测试失败: ${error.message}`, 'system');
        throw error;
    }
}

/**
 * @function aisp_getPerformanceMetrics - 获取性能指标
 * @description 获取当前的性能指标
 * @returns {Object} 性能指标
 */
function aisp_getPerformanceMetrics() {
    if (!aisp_performanceOptimizer) {
        return {
            available: false,
            message: '性能优化器未初始化'
        };
    }

    const metrics = aisp_performanceOptimizer.getMetrics();

    return {
        available: true,
        metrics: metrics,
        cache: aisp_cacheManager ? aisp_cacheManager.getStats() : null
    };
}

/**
 * @function aisp_clearCache - 清空缓存
 * @description 清空所有缓存数据
 */
function aisp_clearCache() {
    try {
        if (aisp_cacheManager) {
            aisp_cacheManager.clearAll();
            aisp_addChatMessage('🧹 缓存已清空', 'system');
        } else {
            aisp_addChatMessage('⚠️ 缓存管理器不可用', 'system');
        }
    } catch (error) {
        console.error('清空缓存失败:', error);
        aisp_addChatMessage(`❌ 清空缓存失败: ${error.message}`, 'system');
    }
}

/**
 * @function aisp_initializeAPIManager - 初始化API管理器
 * @description 初始化配置管理器和API管理器
 */
async function aisp_initializeAPIManager() {
    try {
        // 初始化配置管理器
        if (typeof getConfigManager === 'function') {
            aisp_configManager = getConfigManager();
            await aisp_configManager.initialize();
        }

        // 初始化API管理器
        if (typeof getAPIManager === 'function') {
            aisp_apiManager = getAPIManager();
            await aisp_apiManager.initialize();

            if (aisp_apiManager.isReady()) {
                console.log('✅ API管理器初始化成功');
            } else {
                console.warn('⚠️ API管理器未就绪，请检查API密钥配置');
            }
        } else {
            console.warn('⚠️ API管理器未加载，某些功能可能不可用');
        }

    } catch (error) {
        console.error('❌ API管理器初始化失败:', error);
        throw error;
    }
}

/**
 * @function aisp_loadConfiguration - 加载配置
 * @description 从存储中加载用户配置和设置
 */
async function aisp_loadConfiguration() {
    try {
        const result = await chrome.storage.local.get('aisp_config');
        const config = result.aisp_config || {};
        
        // 设置语言
        aisp_currentLanguage = config.currentLanguage || 'zh_CN';
        const languageSelect = document.getElementById('aisp-language-select');
        if (languageSelect) {
            languageSelect.value = aisp_currentLanguage;
        }
        
        // 应用语言设置
        await lang_setCurrentLanguage(aisp_currentLanguage);
        
        console.log('配置加载完成:', config);
    } catch (error) {
        console.error('加载配置失败:', error);
    }
}

/**
 * @function aisp_setupEventListeners - 设置事件监听器
 * @description 为界面元素添加事件监听器
 */
function aisp_setupEventListeners() {
    // 语言切换
    const languageSelect = document.getElementById('aisp-language-select');
    if (languageSelect) {
        languageSelect.addEventListener('change', aisp_handleLanguageChange);
    }
    
    // 设置按钮
    const settingsBtn = document.getElementById('aisp-settings-btn');
    if (settingsBtn) {
        settingsBtn.addEventListener('click', aisp_openSettings);
    }
    
    // 刷新按钮
    const refreshBtn = document.getElementById('aisp-refresh-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', aisp_refreshAnalysis);
    }

    // 调试面板按钮
    const debugBtn = document.getElementById('aisp-debug-btn');
    if (debugBtn) {
        debugBtn.addEventListener('click', () => {
            if (typeof aisp_toggleDebugPanel === 'function') {
                aisp_toggleDebugPanel();
            } else {
                console.warn('调试面板功能未加载');
            }
        });
    }
    
    // 注意：移除了手动分析按钮，改为自动分析
    
    // 发送按钮
    const sendBtn = document.getElementById('aisp-send-btn');
    if (sendBtn) {
        sendBtn.addEventListener('click', aisp_handleSendMessage);
    }
    
    // 清空按钮
    const clearBtn = document.getElementById('aisp-clear-btn');
    if (clearBtn) {
        clearBtn.addEventListener('click', aisp_clearInput);
    }
    
    // 输入框回车事件
    const userInput = document.getElementById('aisp-user-input');
    if (userInput) {
        userInput.addEventListener('keydown', (event) => {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                aisp_handleSendMessage();
            }
        });
    }
    
    // 模板管理按钮
    const templateBtn = document.getElementById('aisp-template-manager-btn');
    if (templateBtn) {
        templateBtn.addEventListener('click', aisp_openTemplateManager);
    }
    
    // 知识库按钮
    const knowledgeBtn = document.getElementById('aisp-knowledge-base-btn');
    if (knowledgeBtn) {
        knowledgeBtn.addEventListener('click', aisp_openKnowledgeBase);
    }

    // API连接测试按钮
    const testConnectionBtn = document.getElementById('aisp-test-connection-btn');
    if (testConnectionBtn) {
        testConnectionBtn.addEventListener('click', aisp_testConnectionManually);
    }

    // 模态框关闭
    const modalClose = document.getElementById('aisp-modal-close');
    const modalOverlay = document.getElementById('aisp-modal-overlay');
    const modalCancel = document.getElementById('aisp-modal-cancel');
    
    if (modalClose) modalClose.addEventListener('click', aisp_closeModal);
    if (modalCancel) modalCancel.addEventListener('click', aisp_closeModal);
    if (modalOverlay) {
        modalOverlay.addEventListener('click', (event) => {
            if (event.target === modalOverlay) {
                aisp_closeModal();
            }
        });
    }

    // 设置Chrome runtime消息监听器
    if (chrome.runtime && chrome.runtime.onMessage) {
        chrome.runtime.onMessage.addListener(aisp_handleRuntimeMessage);
        sidepanelLogger.info('Chrome runtime消息监听器已设置');
    }

    // 页面可见性变化监听器
    if (typeof document.addEventListener === 'function') {
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                // 页面变为可见时，更新连接状态
                setTimeout(() => {
                    aisp_updateConnectionStatus();
                }, 100);
            }
        });
        sidepanelLogger.info('页面可见性监听器已设置');
    }
}

/**
 * @function aisp_handleRuntimeMessage - 处理来自service worker的消息
 * @description 处理API连接状态更新等消息
 * @param {Object} message - 消息对象
 * @param {Object} sender - 发送者信息
 * @param {Function} sendResponse - 响应函数
 */
function aisp_handleRuntimeMessage(message, sender, sendResponse) {
    try {
        switch (message.action) {
            case 'api_connection_status':
                // API连接状态更新
                sidepanelLogger.info('收到API连接状态更新', {
                    connected: message.connected,
                    timestamp: message.timestamp
                });
                
                // 更新连接状态显示
                aisp_updateConnectionStatus();
                
                // 显示状态通知
                aisp_showConnectionStatusNotification(message.connected, message.details);
                break;
                
            default:
                // 忽略未知消息类型
                break;
        }
    } catch (error) {
        sidepanelLogger.error('处理runtime消息失败', {
            error: error.message,
            message: message
        });
    }
}

/**
 * @function aisp_showConnectionStatusNotification - 显示连接状态通知
 * @description 在界面上显示API连接状态变化的通知
 * @param {boolean} connected - 是否连接成功
 * @param {Object} details - 连接详情
 */
function aisp_showConnectionStatusNotification(connected, details) {
    try {
        // 如果聊天界面可用，在其中显示状态消息
        if (aisp_chatInterface && typeof aisp_chatInterface.addSystemMessage === 'function') {
            const statusMessage = connected 
                ? `✅ Gemini API 连接成功 (响应时间: ${details?.responseTime || 'N/A'}ms)`
                : `❌ Gemini API 连接失败: ${details?.error || '未知错误'}`;
                
            aisp_chatInterface.addSystemMessage(statusMessage);
        } else {
            // 备用方案：在控制台显示
            const statusMessage = connected ? 'API连接成功' : 'API连接失败';
            sidepanelLogger.info(statusMessage, details);
        }
        
        // 更新连接状态图标
        const statusElement = document.getElementById('aisp-connection-status');
        if (statusElement) {
            statusElement.classList.add('aisp-status-updated');
            setTimeout(() => {
                statusElement.classList.remove('aisp-status-updated');
            }, 2000);
        }
        
    } catch (error) {
        sidepanelLogger.error('显示连接状态通知失败', { error: error.message });
    }
}

/**
 * @function aisp_testConnectionManually - 手动测试API连接
 * @description 用户点击测试按钮时手动测试API连接
 */
async function aisp_testConnectionManually() {
    const testBtn = document.getElementById('aisp-test-connection-btn');
    const statusElement = document.getElementById('aisp-connection-status');
    
    try {
        sidepanelLogger.userAction('manual_connection_test');
        
        // 显示测试中状态
        if (testBtn) {
            testBtn.classList.add('aisp-testing');
            testBtn.disabled = true;
        }
        
        if (statusElement) {
            const statusText = statusElement.querySelector('.aisp-status-text');
            if (statusText) {
                statusText.textContent = '连接测试中...';
            }
        }
        
        // 发送测试连接请求到service worker
        const response = await chrome.runtime.sendMessage({ action: 'test_api_connection' });
        
        if (response?.success) {
            const apiStatus = response.data;
            sidepanelLogger.info('手动连接测试完成', {
                connected: apiStatus.connected,
                responseTime: apiStatus.responseTime
            });
            
            // 显示测试结果通知
            if (aisp_chatInterface && typeof aisp_chatInterface.addSystemMessage === 'function') {
                const message = apiStatus.connected 
                    ? `✅ 连接测试成功！响应时间: ${apiStatus.responseTime || 'N/A'}ms`
                    : `❌ 连接测试失败: ${apiStatus.error || '未知错误'}`;
                aisp_chatInterface.addSystemMessage(message);
            }
            
        } else {
            sidepanelLogger.error('手动连接测试失败', { error: response?.error });
            
            if (aisp_chatInterface && typeof aisp_chatInterface.addSystemMessage === 'function') {
                aisp_chatInterface.addSystemMessage(`❌ 连接测试失败: ${response?.error || '未知错误'}`);
            }
        }
        
    } catch (error) {
        sidepanelLogger.error('手动连接测试异常', { error: error.message });
        
        if (aisp_chatInterface && typeof aisp_chatInterface.addSystemMessage === 'function') {
            aisp_chatInterface.addSystemMessage(`❌ 连接测试异常: ${error.message}`);
        }
        
    } finally {
        // 恢复按钮状态
        if (testBtn) {
            testBtn.classList.remove('aisp-testing');
            testBtn.disabled = false;
        }
        
        // 更新连接状态显示
        setTimeout(() => {
            aisp_updateConnectionStatus();
        }, 500);
    }
}

/**
 * @function aisp_setupConnectionStatusMonitor - 设置连接状态监控
 * @description 定期检查API连接状态，确保状态显示的准确性
 */
function aisp_setupConnectionStatusMonitor() {
    // 每30秒检查一次连接状态
    setInterval(async () => {
        try {
            await aisp_updateConnectionStatus();
        } catch (error) {
            sidepanelLogger.debug('定期连接状态检查失败', { error: error.message });
        }
    }, 30000); // 30秒间隔

    sidepanelLogger.info('连接状态监控已启动', { interval: '30秒' });
}

/**
 * @function aisp_debugConnectionStatus - 调试连接状态
 * @description 在控制台显示详细的连接状态信息，用于调试
 */
async function aisp_debugConnectionStatus() {
    try {
        console.group('🔍 AI Side Panel - API连接状态调试');
        
        // 检查硬编码API密钥状态
        if (typeof getApiKeyStatus === 'function') {
            const keyStatus = getApiKeyStatus();
            console.log('📋 API密钥状态:', keyStatus);
        }
        
        // 检查API管理器状态
        if (aisp_apiManager) {
            console.log('🔧 API管理器状态:', {
                initialized: aisp_apiManager.initialized,
                isReady: aisp_apiManager.isReady()
            });
        }
        
        // 获取存储的连接状态
        const response = await chrome.runtime.sendMessage({ action: 'get_api_status' });
        if (response?.success) {
            console.log('💾 存储的连接状态:', response.data);
        }
        
        // 执行实时连接测试
        console.log('🔄 执行实时连接测试...');
        const testResponse = await chrome.runtime.sendMessage({ action: 'test_api_connection' });
        if (testResponse?.success) {
            console.log('✅ 实时测试结果:', testResponse.data);
        } else {
            console.log('❌ 实时测试失败:', testResponse?.error);
        }
        
        console.groupEnd();
        
    } catch (error) {
        console.error('🐛 调试连接状态时发生错误:', error);
    }
}

// 在全局作用域中暴露调试函数
if (typeof window !== 'undefined') {
    window.aisp_debugConnectionStatus = aisp_debugConnectionStatus;
}
// #endregion

// #region 页面信息处理
/**
 * @function aisp_loadCurrentPageInfo - 加载当前页面信息
 * @description 获取并显示当前标签页的信息
 */
async function aisp_loadCurrentPageInfo() {
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tab) {
            const pageTitle = document.getElementById('aisp-page-title');
            if (pageTitle) {
                pageTitle.textContent = tab.title || '未知页面';
            }
            
            // 存储当前页面数据
            aisp_currentPageData = {
                url: tab.url,
                title: tab.title,
                tabId: tab.id
            };
        }
    } catch (error) {
        console.error('加载页面信息失败:', error);
    }
}

/**
 * @function aisp_updateConnectionStatus - 更新连接状态
 * @description 更新底部状态栏的连接状态显示，从service worker获取实时状态
 */
async function aisp_updateConnectionStatus() {
    const statusElement = document.getElementById('aisp-connection-status');
    const statusDot = statusElement?.querySelector('.aisp-status-dot');

    if (statusElement && statusDot) {
        try {
            // 首先从service worker获取实时API状态
            const response = await chrome.runtime.sendMessage({ action: 'get_api_status' });
            
            if (response?.success && response.data) {
                const apiStatus = response.data;
                const isConnected = apiStatus.connected;
                
                let statusText = '';
                let tooltip = '';
                
                if (isConnected) {
                    statusText = `已连接 (${apiStatus.model?.replace('gemini-', 'Gemini ') || 'Gemini 2.5'})`;
                    tooltip = `连接正常 | 响应时间: ${apiStatus.responseTime || 'N/A'}ms | 最后检查: ${new Date(apiStatus.lastChecked).toLocaleTimeString()}`;
                } else {
                    statusText = '连接失败';
                    tooltip = `错误: ${apiStatus.error || '未知错误'} | 最后检查: ${new Date(apiStatus.lastChecked).toLocaleTimeString()}`;
                }
                
                // 更新状态点的样式
                statusDot.className = `aisp-status-dot ${isConnected ? 'aisp-status-connected' : 'aisp-status-disconnected'}`;
                
                // 更新状态文本
                const statusTextElement = statusElement.querySelector('.aisp-status-text');
                if (statusTextElement) {
                    statusTextElement.textContent = statusText;
                } else {
                    const textNode = Array.from(statusElement.childNodes).find(node => node.nodeType === Node.TEXT_NODE);
                    if (textNode) {
                        textNode.textContent = statusText;
                    }
                }
                
                // 设置提示信息
                statusElement.title = tooltip;
                
                sidepanelLogger.debug('API连接状态已更新', {
                    connected: isConnected,
                    responseTime: apiStatus.responseTime,
                    model: apiStatus.model
                });
                
            } else {
                // 如果无法获取状态，回退到硬编码检查
                await aisp_fallbackConnectionCheck(statusElement, statusDot);
            }

        } catch (error) {
            sidepanelLogger.error('获取API连接状态失败，使用备用检查', { error: error.message });
            await aisp_fallbackConnectionCheck(statusElement, statusDot);
        }
    }
}

/**
 * @function aisp_fallbackConnectionCheck - 备用连接状态检查
 * @description 当无法从service worker获取状态时的备用检查方法
 * @param {HTMLElement} statusElement - 状态元素
 * @param {HTMLElement} statusDot - 状态点元素
 */
async function aisp_fallbackConnectionCheck(statusElement, statusDot) {
    try {
        // 检查硬编码的API密钥状态
        let isConnected = false;
        let statusText = '未连接';

        if (typeof getApiKeyStatus === 'function') {
            const keyStatus = getApiKeyStatus();
            isConnected = keyStatus.gemini.available;
            statusText = isConnected ? '配置已就绪' : '需要配置API密钥';

            // 如果API管理器也已就绪，显示更详细的状态
            if (aisp_apiManager && aisp_apiManager.isReady()) {
                statusText = '配置已就绪 (未测试)';
            }
        }

        statusDot.className = `aisp-status-dot ${isConnected ? 'aisp-status-warning' : 'aisp-status-disconnected'}`;

        // 更新状态文本
        const statusTextElement = statusElement.querySelector('.aisp-status-text');
        if (statusTextElement) {
            statusTextElement.textContent = statusText;
        } else {
            const textNode = Array.from(statusElement.childNodes).find(node => node.nodeType === Node.TEXT_NODE);
            if (textNode) {
                textNode.textContent = statusText;
            }
        }

        statusElement.title = isConnected ? '配置已加载，但未进行连接测试' : '请配置API密钥';

    } catch (error) {
        sidepanelLogger.error('备用连接状态检查失败', { error: error.message });
        statusDot.className = 'aisp-status-dot aisp-status-disconnected';

        const statusTextElement = statusElement.querySelector('.aisp-status-text');
        if (statusTextElement) {
            statusTextElement.textContent = '状态未知';
        }
    }
}

/**
 * @function aisp_cacheConnectionStatus - 缓存连接状态
 * @description 缓存API连接状态，避免重复检查
 * @param {boolean} isConnected - 连接状态
 * @param {number|null} responseTime - 响应时间
 * @param {string|null} error - 错误信息
 */
function aisp_cacheConnectionStatus(isConnected, responseTime = null, error = null) {
    try {
        const statusCache = {
            isConnected,
            responseTime,
            error,
            timestamp: Date.now(),
            ttl: 30000 // 缓存30秒
        };

        // 存储到内存缓存
        if (!window.aisp_statusCache) {
            window.aisp_statusCache = new Map();
        }
        window.aisp_statusCache.set('api_connection', statusCache);

        sidepanelLogger.debug('API状态已缓存', statusCache);

    } catch (error) {
        sidepanelLogger.warn('缓存连接状态失败', { error: error.message });
    }
}

/**
 * @function aisp_getCachedConnectionStatus - 获取缓存的连接状态
 * @description 获取缓存的API连接状态，如果过期则返回null
 * @returns {Object|null} 缓存的状态或null
 */
function aisp_getCachedConnectionStatus() {
    try {
        if (!window.aisp_statusCache) return null;

        const cached = window.aisp_statusCache.get('api_connection');
        if (!cached) return null;

        // 检查是否过期
        const now = Date.now();
        if (now - cached.timestamp > cached.ttl) {
            window.aisp_statusCache.delete('api_connection');
            return null;
        }

        return cached;

    } catch (error) {
        sidepanelLogger.warn('获取缓存状态失败', { error: error.message });
        return null;
    }
}
// #endregion

// #region 事件处理函数
/**
 * @function aisp_handleLanguageChange - 处理语言切换
 * @param {Event} event - 事件对象
 */
async function aisp_handleLanguageChange(event) {
    try {
        const newLanguage = event.target.value;
        aisp_currentLanguage = newLanguage;
        
        // 保存语言设置
        const result = await chrome.storage.local.get('aisp_config');
        const config = result.aisp_config || {};
        config.currentLanguage = newLanguage;
        await chrome.storage.local.set({ 'aisp_config': config });
        
        // 应用语言设置
        await lang_setCurrentLanguage(newLanguage);
        
        // 更新界面文本
        aisp_updateUILanguage();
        
        console.log('语言已切换到:', newLanguage);
    } catch (error) {
        console.error('切换语言失败:', error);
        aisp_showError('语言切换失败');
    }
}

/**
 * @function aisp_openSettings - 打开设置页面
 */
function aisp_openSettings() {
    chrome.tabs.create({
        url: chrome.runtime.getURL('src/settings/settings.html')
    });
}

/**
 * @function aisp_refreshAnalysis - 刷新分析
 */
async function aisp_refreshAnalysis() {
    if (aisp_isAnalyzing) return;
    
    await aisp_analyzeCurrentPage();
}

/**
 * @function aisp_analyzeCurrentPage - 分析当前页面
 * @description 触发对当前页面的AI分析
 */
async function aisp_analyzeCurrentPage() {
    if (aisp_isAnalyzing) return;

    const startTime = Date.now();

    try {
        aisp_isAnalyzing = true;
        aisp_showLoading(true);

        // 记录页面分析开始
        aisp_logSidepanelUserAction('analyze_page_start', {
            language: aisp_currentLanguage,
            url: window.location.href
        });

        // 隐藏之前的分析结果
        const resultContainer = document.getElementById('aisp-analysis-result');
        if (resultContainer) {
            resultContainer.style.display = 'none';
        }

        // 检查API管理器是否就绪
        if (!aisp_apiManager || !aisp_apiManager.isReady()) {
            throw new Error('AI服务未就绪，请检查API密钥配置');
        }

        // 获取当前页面内容
        const pageContent = await aisp_getCurrentPageContent();

        // 检查缓存
        const cacheKey = aisp_generateCacheKey(pageContent);
        if (aisp_analysisCache.has(cacheKey)) {
            const cachedResult = aisp_analysisCache.get(cacheKey);
            aisp_displayAnalysisResult(cachedResult);
            aisp_addChatMessage('📄 已显示缓存的分析结果', 'system');
            return;
        }

        // 使用流式API分析内容
        aisp_addChatMessage('🔍 正在分析当前页面...', 'system');

        // 创建流式消息
        let streamMessage = null;
        if (aisp_chatInterface && typeof aisp_chatInterface.addStreamMessage === 'function') {
            streamMessage = aisp_chatInterface.addStreamMessage('ai', {
                isAnalysis: true
            });
        }

        // 检查是否支持流式分析
        let analysisResult;
        if (aisp_apiManager.geminiAPI && typeof aisp_apiManager.geminiAPI.analyzeContentStream === 'function') {
            analysisResult = await aisp_apiManager.geminiAPI.analyzeContentStream({
                text: pageContent.text,
                url: pageContent.url,
                title: pageContent.title,
                structuredContent: pageContent.structuredContent,
                metadata: pageContent.metadata
            }, (chunk, fullText) => {
                // 流式更新回调
                if (streamMessage) {
                    streamMessage.updateContent(fullText, false);
                }
            });
        } else {
            // 回退到标准分析
            analysisResult = await aisp_apiManager.analyzeContent({
                text: pageContent.text,
                url: pageContent.url,
                title: pageContent.title,
                structuredContent: pageContent.structuredContent,
                metadata: pageContent.metadata
            });
        }

        // 完成流式消息
        if (streamMessage) {
            const currentText = streamMessage.element.querySelector('.aisp-message-text')?.textContent || '';
            streamMessage.updateContent(currentText, true);
        }

        // 缓存结果
        aisp_analysisCache.set(cacheKey, analysisResult);

        // 显示分析结果
        aisp_displayAnalysisResult(analysisResult);

        // 记录分析成功
        aisp_logSidepanelUserAction('analyze_page_success', {
            duration: Date.now() - startTime,
            hasSummary: !!analysisResult.summary,
            hasKeyPoints: !!(analysisResult.keyPoints && analysisResult.keyPoints.length > 0),
            hasReplySuggestions: !!(analysisResult.replySuggestions && analysisResult.replySuggestions.length > 0)
        });

        // 添加成功消息
        aisp_addChatMessage('✅ 页面分析完成', 'system');

    } catch (error) {
        console.error('分析页面失败:', error);
        const errorMessage = error.userMessage || error.message || '分析失败';

        // 记录分析失败
        aisp_logSidepanelUserAction('analyze_page_failed', {
            error: errorMessage,
            duration: Date.now() - startTime
        });

        aisp_showError('页面分析失败: ' + errorMessage);
    } finally {
        aisp_isAnalyzing = false;
        aisp_showLoading(false);

        // 记录性能指标
        aisp_logSidepanelPerformance('analyze_page', Date.now() - startTime, {
            success: !error
        });
    }
}

/**
 * @function aisp_handleSendMessage - 处理发送消息
 */
async function aisp_handleSendMessage() {
    const userInput = document.getElementById('aisp-user-input');
    if (!userInput) return;

    const message = userInput.value.trim();
    if (!message) return;

    const startTime = Date.now();

    try {
        // 记录用户发送消息的交互
        aisp_logSidepanelUserAction('send_message', {
            messageLength: message.length,
            hasPageContent: !!aisp_currentPageData,
            language: aisp_currentLanguage
        });

        // 清空输入框
        userInput.value = '';

        // 显示加载状态
        aisp_showLoading(true);

        // 使用新的用户输入处理函数
        await aisp_handleUserInput(message);

        // 记录发送成功
        aisp_logSidepanelUserAction('send_message_success', {
            messageLength: message.length,
            duration: Date.now() - startTime
        });

    } catch (error) {
        console.error('发送消息失败:', error);

        // 记录发送失败
        aisp_logSidepanelUserAction('send_message_failed', {
            error: error.message,
            messageLength: message.length,
            duration: Date.now() - startTime
        });

        aisp_showError('消息发送失败: ' + error.message);
    } finally {
        aisp_showLoading(false);

        // 记录性能指标
        aisp_logSidepanelPerformance('send_message', Date.now() - startTime, {
            messageLength: message.length
        });
    }
}

/**
 * @function aisp_clearInput - 清空输入框
 */
function aisp_clearInput() {
    const userInput = document.getElementById('aisp-user-input');
    if (userInput) {
        userInput.value = '';
        userInput.focus();
    }
}

/**
 * @function aisp_openTemplateManager - 打开模板管理器
 */
function aisp_openTemplateManager() {
    aisp_showModal('模板管理', '模板管理功能正在开发中...', [
        { text: '关闭', action: aisp_closeModal }
    ]);
}

/**
 * @function aisp_openKnowledgeBase - 打开知识库
 */
function aisp_openKnowledgeBase() {
    aisp_showModal('知识库', '知识库功能正在开发中...', [
        { text: '关闭', action: aisp_closeModal }
    ]);
}
// #endregion

// #region UI辅助函数
/**
 * @function aisp_showLoading - 显示/隐藏加载状态
 * @param {boolean} show - 是否显示加载状态
 */
function aisp_showLoading(show) {
    const loadingElement = document.getElementById('aisp-loading');
    if (loadingElement) {
        loadingElement.style.display = show ? 'flex' : 'none';
    }
}

/**
 * @function aisp_displayAnalysisResult - 显示分析结果
 * @param {Object} result - 分析结果数据
 */
function aisp_displayAnalysisResult(result) {
    try {
        // 使用新的内容卡片系统显示分析结果
        if (typeof aisp_addContentCard === 'function') {
            // 添加页面摘要卡片
            if (result.summary) {
                aisp_addContentCard({
                    id: `summary-${Date.now()}`,
                    type: 'summary',
                    title: '页面摘要',
                    content: result.summary,
                    icon: '📄',
                    actions: [
                        {
                            icon: '📋',
                            title: '复制摘要',
                            onClick: () => aisp_copyToClipboard(result.summary)
                        }
                    ]
                });
            }

            // 添加关键要点卡片
            if (result.keyPoints && result.keyPoints.length > 0) {
                aisp_addContentCard({
                    id: `keypoints-${Date.now()}`,
                    type: 'keypoints',
                    title: '关键要点',
                    content: result.keyPoints,
                    icon: '🎯',
                    actions: [
                        {
                            icon: '📋',
                            title: '复制要点',
                            onClick: () => aisp_copyToClipboard(result.keyPoints.join('\n'))
                        }
                    ]
                });
            }

            // 添加思维导图卡片
            if (result.keyPoints || result.summary) {
                const mindmapData = aisp_generateMindMapData(result);
                aisp_addContentCard({
                    id: `mindmap-${Date.now()}`,
                    type: 'mindmap',
                    title: '思维导图',
                    content: mindmapData,
                    icon: '🧠',
                    actions: [
                        {
                            icon: '🔍',
                            title: '全屏查看',
                            onClick: () => aisp_showFullscreenMindMap(mindmapData)
                        }
                    ]
                });
            }

            // 添加回复建议卡片
            if (result.replySuggestions && result.replySuggestions.length > 0) {
                aisp_addContentCard({
                    id: `replies-${Date.now()}`,
                    type: 'replies',
                    title: '智能回复建议',
                    content: result.replySuggestions,
                    icon: '💬',
                    actions: [
                        {
                            icon: '🔄',
                            title: '刷新建议',
                            onClick: () => aisp_refreshReplySuggestions()
                        }
                    ]
                });
            }

        } else {
            // 后备方案：使用传统方式显示
            aisp_displayAnalysisResultLegacy(result);
        }

    } catch (error) {
        console.error('显示分析结果失败:', error);
        aisp_displayAnalysisResultLegacy(result);
    }
}

/**
 * @function aisp_displayAnalysisResultLegacy - 传统方式显示分析结果
 * @param {Object} result - 分析结果数据
 */
function aisp_displayAnalysisResultLegacy(result) {
    const resultContainer = document.getElementById('aisp-analysis-result');
    if (!resultContainer) return;

    // 更新总结内容
    const summaryContent = document.getElementById('aisp-summary-content');
    if (summaryContent) {
        summaryContent.textContent = result.summary || '暂无总结内容';
    }

    // 更新关键要点
    const keypointsContent = document.getElementById('aisp-keypoints-content');
    if (keypointsContent) {
        if (result.keyPoints && result.keyPoints.length > 0) {
            keypointsContent.innerHTML = result.keyPoints.map(point =>
                `<div class="aisp-keypoint">• ${point}</div>`
            ).join('');
        } else {
            keypointsContent.textContent = '暂无关键要点';
        }
    }

    // 显示结果容器
    resultContainer.style.display = 'block';
}

/**
 * @function aisp_generateMindMapData - 生成思维导图数据
 * @param {Object} analysisResult - 分析结果
 * @returns {Object} 思维导图数据
 */
function aisp_generateMindMapData(analysisResult) {
    if (typeof ui_MindMapRenderer !== 'undefined') {
        const renderer = new ui_MindMapRenderer('temp', {});
        return renderer.generateMindMapFromAnalysis(analysisResult);
    }

    // 简化的思维导图数据
    return {
        nodes: [
            { id: 'root', label: '页面分析', isRoot: true },
            { id: 'summary', label: '摘要' },
            { id: 'keypoints', label: '要点' }
        ],
        links: [
            { source: 'root', target: 'summary' },
            { source: 'root', target: 'keypoints' }
        ]
    };
}

/**
 * @function aisp_copyToClipboard - 复制到剪贴板
 * @param {string} text - 要复制的文本
 */
async function aisp_copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        aisp_addChatMessage('✅ 内容已复制到剪贴板', 'system');
    } catch (error) {
        console.error('复制失败:', error);
        aisp_addChatMessage('❌ 复制失败', 'system');
    }
}

/**
 * @function aisp_showFullscreenMindMap - 显示全屏思维导图
 * @param {Object} mindmapData - 思维导图数据
 */
function aisp_showFullscreenMindMap(mindmapData) {
    // 这里可以实现全屏思维导图显示
    console.log('显示全屏思维导图:', mindmapData);
}

/**
 * @function aisp_refreshReplySuggestions - 刷新回复建议
 */
async function aisp_refreshReplySuggestions() {
    try {
        aisp_addChatMessage('🔄 正在刷新回复建议...', 'system');
        // 这里可以重新生成回复建议
    } catch (error) {
        console.error('刷新回复建议失败:', error);
        aisp_addChatMessage('❌ 刷新失败', 'system');
    }
}

/**
 * @function aisp_showError - 显示错误消息
 * @param {string} message - 错误消息
 */
function aisp_showError(message) {
    if (aisp_chatInterface) {
        aisp_chatInterface.addMessage(`❌ ${message}`, 'system');
    }
}

/**
 * @function aisp_showModal - 显示模态框
 * @param {string} title - 标题
 * @param {string} content - 内容
 * @param {Array} actions - 操作按钮数组
 */
function aisp_showModal(title, content, actions = []) {
    const modalOverlay = document.getElementById('aisp-modal-overlay');
    const modalTitle = document.getElementById('aisp-modal-title');
    const modalContent = document.getElementById('aisp-modal-content');
    
    if (modalOverlay && modalTitle && modalContent) {
        modalTitle.textContent = title;
        modalContent.innerHTML = content;
        modalOverlay.style.display = 'flex';
    }
}

/**
 * @function aisp_closeModal - 关闭模态框
 */
function aisp_closeModal() {
    const modalOverlay = document.getElementById('aisp-modal-overlay');
    if (modalOverlay) {
        modalOverlay.style.display = 'none';
    }
}

/**
 * @function aisp_updateUILanguage - 更新界面语言
 * @description 根据当前语言设置更新界面文本
 */
function aisp_updateUILanguage() {
    // 这里将在多语言模块完成后实现
    console.log('更新界面语言到:', aisp_currentLanguage);
}
// #endregion

// #region 脚本启动
// 等待DOM加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', aisp_initializeSidePanel);
} else {
    aisp_initializeSidePanel();
}

console.log('AI Side Panel Sidepanel Script 已加载');
// #endregion

// #region 新增辅助函数

/**
 * @function aisp_getCurrentPageContent - 获取当前页面内容
 * @description 从content script获取当前页面的内容数据
 * @returns {Promise<Object>} 页面内容数据
 */
async function aisp_getCurrentPageContent() {
    try {
        // 获取当前活动标签页
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

        if (!tab) {
            throw new Error('无法获取当前标签页');
        }

        // 向content script请求页面内容
        const response = await chrome.tabs.sendMessage(tab.id, {
            action: 'get_page_content'
        });

        if (response && response.success) {
            return response.data;
        } else {
            throw new Error(response?.error || '无法获取页面内容');
        }

    } catch (error) {
        console.error('获取页面内容失败:', error);

        // 返回基础页面信息作为后备
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        return {
            text: '无法获取页面内容，请刷新页面后重试',
            url: tab?.url || '',
            title: tab?.title || '未知页面',
            structuredContent: {},
            metadata: {}
        };
    }
}

/**
 * @function aisp_generateCacheKey - 生成缓存键
 * @description 基于页面内容生成缓存键
 * @param {Object} pageContent - 页面内容
 * @returns {string} 缓存键
 */
function aisp_generateCacheKey(pageContent) {
    const content = pageContent.text + pageContent.url + pageContent.title;
    // 使用支持UTF-8的base64编码方法
    return aisp_utf8ToBase64(content).substring(0, 32);
}

/**
 * @function aisp_utf8ToBase64 - UTF-8字符串转Base64
 * @description 支持中文和特殊字符的base64编码
 * @param {string} str - 要编码的字符串
 * @returns {string} Base64编码结果
 */
function aisp_utf8ToBase64(str) {
    try {
        // 使用TextEncoder将UTF-8字符串转换为字节数组
        const encoder = new TextEncoder();
        const bytes = encoder.encode(str);

        // 将字节数组转换为二进制字符串
        let binaryString = '';
        for (let i = 0; i < bytes.length; i++) {
            binaryString += String.fromCharCode(bytes[i]);
        }

        // 使用btoa编码二进制字符串
        return btoa(binaryString);
    } catch (error) {
        console.warn('UTF-8 Base64编码失败，使用简化方法:', error);
        // 后备方案：使用简单的哈希算法
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash).toString(36);
    }
}

/**
 * @function aisp_addChatMessage - 添加聊天消息
 * @description 向聊天界面添加消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 ('user', 'ai', 'system')
 */
function aisp_addChatMessage(message, type = 'system') {
    if (aisp_chatInterface && typeof aisp_chatInterface.addMessage === 'function') {
        aisp_chatInterface.addMessage(message, type);
    } else {
        // 简化版本的消息显示
        const messageList = document.getElementById('aisp-message-list');
        if (messageList) {
            const messageElement = document.createElement('div');
            messageElement.className = `aisp-message aisp-message-${type}`;
            messageElement.innerHTML = `
                <div class="aisp-message-content">${message}</div>
                <div class="aisp-message-time">${new Date().toLocaleTimeString()}</div>
            `;
            messageList.appendChild(messageElement);
            messageList.scrollTop = messageList.scrollHeight;
        }
    }

    // 保存到消息历史
    aisp_messageHistory.push({
        message: message,
        type: type,
        timestamp: Date.now()
    });
}

/**
 * @function aisp_createSimpleChatInterface - 创建简化的聊天界面
 * @description 当ChatInterface组件不可用时的后备方案
 * @returns {Object} 简化的聊天界面对象
 */
function aisp_createSimpleChatInterface() {
    return {
        addMessage: (message, type) => {
            aisp_addChatMessage(message, type);
        },
        clear: () => {
            const messageList = document.getElementById('aisp-message-list');
            if (messageList) {
                messageList.innerHTML = '';
            }
            aisp_messageHistory = [];
        }
    };
}

/**
 * @function aisp_checkInitializationStatus - 检查初始化状态
 * @description 检查各组件的详细状态，返回完整的状态报告
 * @returns {Object} 包含各组件状态的详细报告
 */
function aisp_checkInitializationStatus() {
    const status = {
        overall: false,
        components: {
            apiManager: {
                exists: !!aisp_apiManager,
                ready: aisp_apiManager ? aisp_apiManager.isReady() : false,
                details: aisp_apiManager ? 'API管理器已初始化' : 'API管理器未初始化'
            },
            chatInterface: {
                exists: !!aisp_chatInterface,
                ready: aisp_chatInterface ? (typeof aisp_chatInterface.addMessage === 'function') : false,
                details: aisp_chatInterface ? '聊天界面已初始化' : '聊天界面未初始化'
            },
            apiKeys: {
                exists: typeof getApiKeyStatus === 'function',
                ready: false,
                details: '未检查'
            },
            logger: {
                exists: !!sidepanelLogger,
                ready: sidepanelLogger ? (typeof sidepanelLogger.info === 'function') : false,
                details: sidepanelLogger ? '日志系统已初始化' : '日志系统未初始化'
            }
        },
        timestamp: Date.now()
    };

    // 检查API密钥状态
    if (status.components.apiKeys.exists) {
        try {
            const keyStatus = getApiKeyStatus();
            status.components.apiKeys.ready = keyStatus && keyStatus.gemini && keyStatus.gemini.available;
            status.components.apiKeys.details = status.components.apiKeys.ready
                ? 'API密钥配置正确'
                : 'API密钥未配置或无效';
        } catch (error) {
            status.components.apiKeys.details = `API密钥检查失败: ${error.message}`;
        }
    } else {
        status.components.apiKeys.details = 'API密钥检查函数不存在';
    }

    // 计算总体状态
    const readyComponents = Object.values(status.components).filter(comp => comp.ready).length;
    const totalComponents = Object.keys(status.components).length;
    status.overall = readyComponents === totalComponents;
    status.readyCount = readyComponents;
    status.totalCount = totalComponents;

    return status;
}

/**
 * @function aisp_autoAnalyzeCurrentPage - 自动分析当前页面
 * @description 在侧边栏初始化时自动分析当前页面，使用指数退避重试机制
 */
async function aisp_autoAnalyzeCurrentPage() {
    const maxRetries = 5;
    const baseDelay = 500; // 初始延迟500ms
    let retryCount = 0;

    sidepanelLogger.info('开始自动页面分析');

    while (retryCount <= maxRetries) {
        try {
            // 检查初始化状态
            const initStatus = aisp_checkInitializationStatus();

            sidepanelLogger.debug('组件状态检查', {
                attempt: retryCount + 1,
                maxRetries: maxRetries + 1,
                status: initStatus
            });

            if (initStatus.overall) {
                // 所有组件就绪，开始分析
                sidepanelLogger.info('所有组件就绪，开始自动页面分析');
                aisp_addChatMessage('🔍 正在自动分析当前页面...', 'system');

                // 执行页面分析
                await aisp_analyzeCurrentPage();

                sidepanelLogger.info('自动页面分析完成');
                return; // 成功完成，退出函数

            } else {
                // 组件未就绪，记录详细状态
                const notReadyComponents = Object.entries(initStatus.components)
                    .filter(([name, comp]) => !comp.ready)
                    .map(([name, comp]) => `${name}: ${comp.details}`)
                    .join(', ');

                sidepanelLogger.warn('组件未就绪，准备重试', {
                    attempt: retryCount + 1,
                    notReadyComponents,
                    readyCount: initStatus.readyCount,
                    totalCount: initStatus.totalCount
                });

                if (retryCount === maxRetries) {
                    // 最后一次尝试失败
                    throw new Error(`组件初始化超时，未就绪组件: ${notReadyComponents}`);
                }

                // 计算延迟时间（指数退避）
                const delay = baseDelay * Math.pow(2, retryCount);
                sidepanelLogger.info(`等待${delay}ms后重试`, {
                    retryCount: retryCount + 1,
                    maxRetries: maxRetries + 1
                });

                await new Promise(resolve => setTimeout(resolve, delay));
                retryCount++;
            }

        } catch (error) {
            sidepanelLogger.error('自动页面分析失败', {
                error: error.message,
                attempt: retryCount + 1,
                maxRetries: maxRetries + 1
            });

            if (retryCount === maxRetries) {
                // 所有重试都失败了
                aisp_addChatMessage('❌ 自动分析失败，组件初始化超时', 'system');
                aisp_addChatMessage('🔄 您可以点击下方按钮手动重试', 'system');

                // 添加手动重试按钮
                aisp_addManualRetryButton();
                return;
            }

            // 继续重试
            const delay = baseDelay * Math.pow(2, retryCount);
            await new Promise(resolve => setTimeout(resolve, delay));
            retryCount++;
        }
    }
}

/**
 * @function aisp_addManualRetryButton - 添加手动重试按钮
 * @description 在自动分析失败后，提供手动重试选项
 */
function aisp_addManualRetryButton() {
    try {
        // 检查是否已经有重试按钮
        if (document.getElementById('aisp-manual-retry-btn')) {
            return;
        }

        const messageList = document.getElementById('aisp-message-list');
        if (!messageList) return;

        const retryContainer = document.createElement('div');
        retryContainer.className = 'aisp-message aisp-message-system';
        retryContainer.innerHTML = `
            <div class="aisp-message-content">
                <div class="aisp-quick-actions">
                    <button id="aisp-manual-retry-btn" class="aisp-btn aisp-btn-primary">
                        <span class="aisp-btn-icon">🔄</span>
                        <span>手动重试分析</span>
                    </button>
                </div>
            </div>
        `;

        messageList.appendChild(retryContainer);

        // 添加事件监听器
        const retryBtn = document.getElementById('aisp-manual-retry-btn');
        if (retryBtn) {
            retryBtn.addEventListener('click', async () => {
                retryBtn.disabled = true;
                retryBtn.textContent = '正在重试...';

                try {
                    await aisp_autoAnalyzeCurrentPage();
                    // 成功后移除重试按钮
                    retryContainer.remove();
                } catch (error) {
                    retryBtn.disabled = false;
                    retryBtn.innerHTML = '<span class="aisp-btn-icon">🔄</span><span>重试失败，再次尝试</span>';
                }
            });
        }

        // 自动滚动到底部
        messageList.scrollTop = messageList.scrollHeight;

    } catch (error) {
        sidepanelLogger.error('添加手动重试按钮失败', { error: error.message });
    }
}

/**
 * @function aisp_handleUserInput - 处理用户输入
 * @description 处理用户在输入框中的消息
 * @param {string} userInput - 用户输入的内容
 */
async function aisp_handleUserInput(userInput) {
    if (!userInput.trim()) return;

    try {
        // 添加用户消息
        aisp_addChatMessage(userInput, 'user');

        // 检查API管理器
        if (!aisp_apiManager || !aisp_apiManager.isReady()) {
            aisp_addChatMessage('❌ AI服务未就绪，请检查API密钥配置', 'system');
            return;
        }

        // 显示正在处理的消息
        aisp_addChatMessage('🤔 正在思考...', 'system');

        // 获取当前页面内容作为上下文
        const pageContent = await aisp_getCurrentPageContent();
        const context = {
            pageTitle: pageContent.title,
            pageUrl: pageContent.url,
            pageContent: pageContent.text.substring(0, 500),
            previousMessages: aisp_messageHistory.slice(-3).map(msg => msg.message)
        };

        // 并行处理：生成AI回复和回复建议
        const [aiReply, replySuggestions] = await Promise.allSettled([
            // 生成AI回复
            aisp_apiManager.generateReplies(`当前页面：${context.pageTitle}\n内容摘要：${context.pageContent}...\n\n用户问题：${userInput}`, {
                type: 'helpful',
                tone: 'friendly'
            }),
            // 生成回复建议
            aisp_generateReplySuggestions(userInput, context)
        ]);

        // 处理AI回复
        if (aiReply.status === 'fulfilled' && aiReply.value && aiReply.value.length > 0) {
            aisp_addChatMessage(aiReply.value[0], 'ai');
        } else {
            aisp_addChatMessage('抱歉，我暂时无法回答这个问题。', 'ai');
        }

        // 处理回复建议（如果失败也不影响主要功能）
        if (replySuggestions.status === 'rejected') {
            console.warn('回复建议生成失败:', replySuggestions.reason);
        }

    } catch (error) {
        console.error('处理用户输入失败:', error);
        const errorMessage = error.userMessage || error.message || '处理失败';
        aisp_addChatMessage('❌ ' + errorMessage, 'system');
    }
}

/**
 * @function aisp_generateReplySuggestions - 生成回复建议
 * @description 为用户输入生成智能回复建议
 * @param {string} userInput - 用户输入
 * @param {Object} context - 上下文信息
 */
async function aisp_generateReplySuggestions(userInput, context) {
    try {
        if (!aisp_replySuggestions) {
            console.warn('回复建议组件未初始化');
            return;
        }

        // 生成回复建议
        const replies = await aisp_replySuggestions.generateReplies(userInput, context, {
            language: aisp_currentLanguage,
            maxLength: 200,
            includeEmoji: true
        });

        if (replies && replies.length > 0) {
            aisp_addChatMessage(`💬 已为您生成 ${replies.length} 个回复建议`, 'system');
        }

        return replies;

    } catch (error) {
        console.error('生成回复建议失败:', error);
        throw error;
    }
}

// #endregion
