/**
 * @file API密钥配置模板文件
 * @description 硬编码的API密钥配置模板，适用于内部自用场景
 * @instructions 
 * 1. 复制此文件为 api-keys.js
 * 2. 将下方的占位符替换为实际的API密钥
 * 3. 保存文件并重新加载扩展
 */

// #region API密钥配置

/**
 * Google Gemini API 密钥
 * @description 请将您的Gemini API密钥填入下方
 * @type {string}
 * @example 'AIzaSyDxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
 */
const GEMINI_API_KEY = 'YOUR_GEMINI_API_KEY_HERE';

/**
 * Google Drive API 密钥（如果需要）
 * @description 用于Google Drive集成功能
 * @type {string}
 * @example 'AIzaSyDxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
 */
const GOOGLE_DRIVE_API_KEY = 'YOUR_DRIVE_API_KEY_HERE';

/**
 * 其他API密钥配置
 * @description 预留的其他服务API密钥
 */
const OTHER_API_KEYS = {
    // 预留给其他可能的API服务
    backup_service: '',
    analytics_service: '',
    translation_service: ''
};

// #endregion

// #region 配置验证

/**
 * @function validateHardcodedApiKeys - 验证硬编码的API密钥
 * @description 检查API密钥是否已正确配置
 * @returns {Object} 验证结果
 */
function validateHardcodedApiKeys() {
    const validation = {
        gemini: {
            configured: false,
            valid: false,
            message: ''
        },
        drive: {
            configured: false,
            valid: false,
            message: ''
        }
    };

    // 验证Gemini API密钥
    if (!GEMINI_API_KEY || GEMINI_API_KEY === 'YOUR_GEMINI_API_KEY_HERE') {
        validation.gemini.message = '请在 src/config/api-keys.js 中配置有效的 Gemini API 密钥';
    } else if (!GEMINI_API_KEY.startsWith('AIza') || GEMINI_API_KEY.length !== 39) {
        validation.gemini.configured = true;
        validation.gemini.message = 'Gemini API 密钥格式无效，应以 AIza 开头且长度为 39 字符';
    } else {
        validation.gemini.configured = true;
        validation.gemini.valid = true;
        validation.gemini.message = 'Gemini API 密钥配置正确';
    }

    // 验证Google Drive API密钥（可选）
    if (!GOOGLE_DRIVE_API_KEY || GOOGLE_DRIVE_API_KEY === 'YOUR_DRIVE_API_KEY_HERE') {
        validation.drive.message = 'Google Drive API 密钥未配置（可选）';
    } else if (!GOOGLE_DRIVE_API_KEY.startsWith('AIza') || GOOGLE_DRIVE_API_KEY.length !== 39) {
        validation.drive.configured = true;
        validation.drive.message = 'Google Drive API 密钥格式无效';
    } else {
        validation.drive.configured = true;
        validation.drive.valid = true;
        validation.drive.message = 'Google Drive API 密钥配置正确';
    }

    return validation;
}

/**
 * @function getApiKeyStatus - 获取API密钥状态
 * @description 获取所有API密钥的配置状态
 * @returns {Object} API密钥状态信息
 */
function getApiKeyStatus() {
    const validation = validateHardcodedApiKeys();
    
    return {
        gemini: {
            available: validation.gemini.valid,
            key: validation.gemini.valid ? GEMINI_API_KEY : null,
            status: validation.gemini.message
        },
        drive: {
            available: validation.drive.valid,
            key: validation.drive.valid ? GOOGLE_DRIVE_API_KEY : null,
            status: validation.drive.message
        },
        lastChecked: new Date().toISOString()
    };
}

// #endregion

// #region 配置说明

/**
 * 配置步骤说明：
 * 
 * 1. 获取 Gemini API 密钥：
 *    - 访问 https://makersuite.google.com/app/apikey
 *    - 登录您的 Google 账户
 *    - 创建新的 API 密钥
 *    - 复制生成的密钥
 * 
 * 2. 配置 API 密钥：
 *    - 将上方 GEMINI_API_KEY 的值替换为您的实际密钥
 *    - 确保密钥格式正确（以 AIza 开头，长度 39 字符）
 *    - 保存文件
 * 
 * 3. 重新加载扩展：
 *    - 在 Chrome 扩展管理页面重新加载扩展
 *    - 检查控制台是否显示 "✅ Gemini API密钥配置正确"
 * 
 * 4. 测试功能：
 *    - 打开任意网页
 *    - 点击扩展图标
 *    - 尝试使用 AI 分析功能
 * 
 * 注意事项：
 * - 请妥善保管您的 API 密钥
 * - 不要将包含真实密钥的文件提交到代码仓库
 * - 定期检查 API 使用量和配额
 * - 如发现密钥泄露，请立即重新生成
 */

// #endregion

// #region 安全提醒

/**
 * 安全提醒和最佳实践
 * 
 * 1. 文件安全：
 *    - 此文件包含敏感的API密钥信息
 *    - 请勿将此文件提交到公共代码仓库
 *    - 建议将此文件添加到 .gitignore 中
 * 
 * 2. 密钥管理：
 *    - 定期轮换API密钥
 *    - 监控API密钥的使用情况
 *    - 设置适当的API配额限制
 * 
 * 3. 访问控制：
 *    - 限制API密钥的访问权限
 *    - 只在必要的环境中使用
 *    - 定期审查密钥使用日志
 * 
 * 4. 备份策略：
 *    - 保留API密钥的安全备份
 *    - 准备应急替换方案
 *    - 文档化密钥管理流程
 */

// #endregion

// #region 导出

/**
 * 导出API密钥和相关函数
 */
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        GEMINI_API_KEY,
        GOOGLE_DRIVE_API_KEY,
        OTHER_API_KEYS,
        validateHardcodedApiKeys,
        getApiKeyStatus
    };
} else {
    // 浏览器环境
    window.GEMINI_API_KEY = GEMINI_API_KEY;
    window.GOOGLE_DRIVE_API_KEY = GOOGLE_DRIVE_API_KEY;
    window.OTHER_API_KEYS = OTHER_API_KEYS;
    window.validateHardcodedApiKeys = validateHardcodedApiKeys;
    window.getApiKeyStatus = getApiKeyStatus;
}

// #endregion

// 启动时验证API密钥
console.log('API密钥配置模板文件已加载');
console.log('请按照文件中的说明配置您的API密钥');

const keyStatus = getApiKeyStatus();
if (keyStatus.gemini.available) {
    console.log('✅ Gemini API密钥配置正确');
} else {
    console.warn('⚠️ 请配置 Gemini API 密钥:', keyStatus.gemini.status);
}

if (keyStatus.drive.available) {
    console.log('✅ Google Drive API密钥配置正确');
} else {
    console.log('ℹ️ Google Drive API密钥状态:', keyStatus.drive.status);
}
