/**
 * @file 调试面板组件
 * @description 提供实时日志查看和系统状态监控功能
 */

// #region 调试面板管理器

/**
 * @class DebugPanelManager - 调试面板管理器
 * @description 管理侧边栏中的调试面板功能
 */
class DebugPanelManager {
    constructor() {
        this.isVisible = false;
        this.logBuffer = [];
        this.maxLogEntries = 500;
        this.currentFilter = 'ALL';
        this.autoScroll = true;
        this.currentTab = 'logs'; // 当前选中的标签页
        this.refreshInterval = null;

        this.init();
    }

    /**
     * @function init - 初始化调试面板
     */
    init() {
        this.createDebugPanel();
        this.setupEventListeners();
        this.startLogSync();
        
        console.log('[DEBUG-PANEL] 调试面板已初始化');
    }

    /**
     * @function createDebugPanel - 创建调试面板DOM结构
     */
    createDebugPanel() {
        // 创建调试面板容器
        const debugPanel = document.createElement('div');
        debugPanel.id = 'aisp-debug-panel';
        debugPanel.className = 'aisp-debug-panel';
        debugPanel.style.display = 'none';
        
        debugPanel.innerHTML = `
            <div class="aisp-debug-header">
                <h4 class="aisp-debug-title">
                    <span class="aisp-debug-icon">🔧</span>
                    运行时监控
                </h4>
                <div class="aisp-debug-controls">
                    <button id="aisp-debug-dev-mode" class="aisp-debug-btn" title="开发模式">
                        🛠️
                    </button>
                    <button id="aisp-debug-clear" class="aisp-debug-btn" title="清空数据">
                        🗑️
                    </button>
                    <button id="aisp-debug-export" class="aisp-debug-btn" title="导出数据">
                        📥
                    </button>
                    <button id="aisp-debug-close" class="aisp-debug-btn" title="关闭">
                        ✕
                    </button>
                </div>
            </div>
            <div class="aisp-debug-tabs">
                <button class="aisp-debug-tab active" data-tab="logs">📋 日志</button>
                <button class="aisp-debug-tab" data-tab="api">🌐 API</button>
                <button class="aisp-debug-tab" data-tab="stream">📡 流式</button>
                <button class="aisp-debug-tab" data-tab="performance">⚡ 性能</button>
                <button class="aisp-debug-tab" data-tab="memory">💾 内存</button>
            </div>
            <div class="aisp-debug-content">
                <!-- 日志标签页 -->
                <div id="aisp-debug-tab-logs" class="aisp-debug-tab-content active">
                    <div class="aisp-debug-tab-header">
                        <select id="aisp-debug-filter" class="aisp-debug-filter">
                            <option value="ALL">全部</option>
                            <option value="ERROR">错误</option>
                            <option value="WARN">警告</option>
                            <option value="INFO">信息</option>
                            <option value="DEBUG">调试</option>
                        </select>
                        <input type="text" id="aisp-debug-search" class="aisp-debug-search" placeholder="搜索日志...">
                    </div>
                    <div id="aisp-debug-logs" class="aisp-debug-logs">
                        <!-- 日志条目将在这里动态插入 -->
                    </div>
                </div>

                <!-- API标签页 -->
                <div id="aisp-debug-tab-api" class="aisp-debug-tab-content">
                    <div id="aisp-debug-api-stats" class="aisp-debug-stats">
                        <div class="aisp-debug-stat">
                            <span class="aisp-debug-stat-label">总请求:</span>
                            <span id="aisp-debug-total-requests" class="aisp-debug-stat-value">0</span>
                        </div>
                        <div class="aisp-debug-stat">
                            <span class="aisp-debug-stat-label">成功率:</span>
                            <span id="aisp-debug-success-rate" class="aisp-debug-stat-value">0%</span>
                        </div>
                        <div class="aisp-debug-stat">
                            <span class="aisp-debug-stat-label">平均响应:</span>
                            <span id="aisp-debug-avg-response" class="aisp-debug-stat-value">0ms</span>
                        </div>
                    </div>
                    <div id="aisp-debug-api-list" class="aisp-debug-data-list">
                        <!-- API调用列表 -->
                    </div>
                </div>

                <!-- 流式传输标签页 -->
                <div id="aisp-debug-tab-stream" class="aisp-debug-tab-content">
                    <div id="aisp-debug-stream-stats" class="aisp-debug-stats">
                        <div class="aisp-debug-stat">
                            <span class="aisp-debug-stat-label">总数据块:</span>
                            <span id="aisp-debug-total-chunks" class="aisp-debug-stat-value">0</span>
                        </div>
                        <div class="aisp-debug-stat">
                            <span class="aisp-debug-stat-label">活跃流:</span>
                            <span id="aisp-debug-active-streams" class="aisp-debug-stat-value">0</span>
                        </div>
                    </div>
                    <div id="aisp-debug-stream-list" class="aisp-debug-data-list">
                        <!-- 流式数据列表 -->
                    </div>
                </div>

                <!-- 性能标签页 -->
                <div id="aisp-debug-tab-performance" class="aisp-debug-tab-content">
                    <div id="aisp-debug-performance-chart" class="aisp-debug-chart">
                        <!-- 性能图表 -->
                    </div>
                    <div id="aisp-debug-performance-list" class="aisp-debug-data-list">
                        <!-- 性能数据列表 -->
                    </div>
                </div>

                <!-- 内存标签页 -->
                <div id="aisp-debug-tab-memory" class="aisp-debug-tab-content">
                    <div id="aisp-debug-memory-chart" class="aisp-debug-chart">
                        <!-- 内存图表 -->
                    </div>
                    <div id="aisp-debug-memory-list" class="aisp-debug-data-list">
                        <!-- 内存数据列表 -->
                    </div>
                </div>
            </div>
            <div class="aisp-debug-footer">
                <label class="aisp-debug-checkbox">
                    <input type="checkbox" id="aisp-debug-autoscroll" checked>
                    自动滚动
                </label>
                <label class="aisp-debug-checkbox">
                    <input type="checkbox" id="aisp-debug-realtime" checked>
                    实时同步
                </label>
                <span id="aisp-debug-log-count" class="aisp-debug-log-count">0 条记录</span>
            </div>
        `;

        // 插入到侧边栏主容器中
        const mainContainer = document.getElementById('aisp-main');
        if (mainContainer) {
            mainContainer.appendChild(debugPanel);
        }
    }

    /**
     * @function setupEventListeners - 设置事件监听器
     */
    setupEventListeners() {
        // 标签页切换
        const tabButtons = document.querySelectorAll('.aisp-debug-tab');
        tabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // 过滤器变化
        const filterSelect = document.getElementById('aisp-debug-filter');
        if (filterSelect) {
            filterSelect.addEventListener('change', (e) => {
                this.currentFilter = e.target.value;
                this.refreshCurrentTab();
            });
        }

        // 搜索功能
        const searchInput = document.getElementById('aisp-debug-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchLogs(e.target.value);
            });
        }

        // 开发模式切换
        const devModeBtn = document.getElementById('aisp-debug-dev-mode');
        if (devModeBtn) {
            devModeBtn.addEventListener('click', () => {
                this.toggleDevMode();
            });
        }

        // 清空数据
        const clearBtn = document.getElementById('aisp-debug-clear');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearCurrentTabData();
            });
        }

        // 导出数据
        const exportBtn = document.getElementById('aisp-debug-export');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportCurrentTabData();
            });
        }

        // 关闭面板
        const closeBtn = document.getElementById('aisp-debug-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.hide();
            });
        }

        // 自动滚动切换
        const autoScrollCheckbox = document.getElementById('aisp-debug-autoscroll');
        if (autoScrollCheckbox) {
            autoScrollCheckbox.addEventListener('change', (e) => {
                this.autoScroll = e.target.checked;
            });
        }

        // 实时同步切换
        const realtimeCheckbox = document.getElementById('aisp-debug-realtime');
        if (realtimeCheckbox) {
            realtimeCheckbox.addEventListener('change', (e) => {
                this.toggleRealtimeSync(e.target.checked);
            });
        }

        // 监听运行时数据更新消息
        window.addEventListener('message', (event) => {
            if (event.data.type === 'AISP_RUNTIME_DATA') {
                this.handleRuntimeDataUpdate(event.data);
            } else if (event.data.type === 'AISP_LOG_UPDATE') {
                this.addLogEntry(event.data.payload);
            }
        });

        // 监听Chrome扩展消息
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
                if (message.action === 'aisp_runtime_data_sync') {
                    this.handleRuntimeDataUpdate(message.data);
                }
            });
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            // Ctrl+Shift+D 切换调试面板
            if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                e.preventDefault();
                this.toggle();
            }
            // Ctrl+Shift+C 清空当前标签页数据
            if (e.ctrlKey && e.shiftKey && e.key === 'C') {
                e.preventDefault();
                this.clearCurrentTabData();
            }
        });
    }

    /**
     * @function startLogSync - 开始日志同步
     */
    startLogSync() {
        // 定期同步运行时数据
        this.refreshInterval = setInterval(() => {
            this.syncRuntimeData();
        }, 2000);
    }

    /**
     * @function syncRuntimeData - 同步运行时数据
     */
    syncRuntimeData() {
        if (typeof aisp_getRuntimeData === 'function') {
            const runtimeData = aisp_getRuntimeData();
            this.updateRuntimeDisplay(runtimeData);
        }

        // 同步日志数据
        if (typeof aisp_getLogBuffer === 'function') {
            const logs = aisp_getLogBuffer({ limit: 50 });
            logs.forEach(log => this.addLogEntry(log));
        }
    }

    /**
     * @function switchTab - 切换标签页
     * @param {string} tabName - 标签页名称
     */
    switchTab(tabName) {
        // 更新标签按钮状态
        document.querySelectorAll('.aisp-debug-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新内容区域
        document.querySelectorAll('.aisp-debug-tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`aisp-debug-tab-${tabName}`).classList.add('active');

        this.currentTab = tabName;
        this.refreshCurrentTab();
    }

    /**
     * @function refreshCurrentTab - 刷新当前标签页
     */
    refreshCurrentTab() {
        switch (this.currentTab) {
            case 'logs':
                this.refreshLogDisplay();
                break;
            case 'api':
                this.refreshAPIDisplay();
                break;
            case 'stream':
                this.refreshStreamDisplay();
                break;
            case 'performance':
                this.refreshPerformanceDisplay();
                break;
            case 'memory':
                this.refreshMemoryDisplay();
                break;
        }
    }

    /**
     * @function handleRuntimeDataUpdate - 处理运行时数据更新
     * @param {Object} data - 运行时数据
     */
    handleRuntimeDataUpdate(data) {
        if (!this.isVisible) return;

        switch (data.dataType) {
            case 'api_request':
            case 'api_response':
                if (this.currentTab === 'api') {
                    this.refreshAPIDisplay();
                }
                break;
            case 'stream_chunk':
                if (this.currentTab === 'stream') {
                    this.refreshStreamDisplay();
                }
                break;
            case 'performance':
                if (this.currentTab === 'performance') {
                    this.refreshPerformanceDisplay();
                }
                break;
            case 'memory':
                if (this.currentTab === 'memory') {
                    this.refreshMemoryDisplay();
                }
                break;
        }
    }

    /**
     * @function toggleDevMode - 切换开发模式
     */
    toggleDevMode() {
        if (typeof aisp_logSetConfig === 'function') {
            const currentConfig = aisp_logGetConfig();
            const newDevMode = !currentConfig.enableDevMode;

            aisp_logSetConfig({
                enableDevMode: newDevMode,
                enableRuntimeMonitoring: newDevMode,
                enableRealtimeSync: newDevMode
            });

            const devModeBtn = document.getElementById('aisp-debug-dev-mode');
            if (devModeBtn) {
                devModeBtn.style.backgroundColor = newDevMode ? '#007AFF' : '';
                devModeBtn.title = newDevMode ? '开发模式 (已启用)' : '开发模式';
            }

            console.log(`[DEBUG-PANEL] 开发模式${newDevMode ? '已启用' : '已禁用'}`);
        }
    }

    /**
     * @function toggleRealtimeSync - 切换实时同步
     * @param {boolean} enabled - 是否启用
     */
    toggleRealtimeSync(enabled) {
        if (typeof aisp_logSetConfig === 'function') {
            aisp_logSetConfig({ enableRealtimeSync: enabled });
            console.log(`[DEBUG-PANEL] 实时同步${enabled ? '已启用' : '已禁用'}`);
        }
    }

    /**
     * @function searchLogs - 搜索日志
     * @param {string} query - 搜索查询
     */
    searchLogs(query) {
        const logsContainer = document.getElementById('aisp-debug-logs');
        if (!logsContainer) return;

        const logElements = logsContainer.querySelectorAll('.aisp-debug-log-entry');
        logElements.forEach(element => {
            const message = element.querySelector('.aisp-debug-log-message').textContent;
            const isMatch = !query || message.toLowerCase().includes(query.toLowerCase());
            element.style.display = isMatch ? 'block' : 'none';
        });
    }

    /**
     * @function show - 显示调试面板
     */
    show() {
        const panel = document.getElementById('aisp-debug-panel');
        if (panel) {
            panel.style.display = 'block';
            this.isVisible = true;
            this.refreshCurrentTab();
            this.syncRuntimeData();
        }
    }

    /**
     * @function hide - 隐藏调试面板
     */
    hide() {
        const panel = document.getElementById('aisp-debug-panel');
        if (panel) {
            panel.style.display = 'none';
            this.isVisible = false;
        }

        // 停止刷新定时器
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    /**
     * @function toggle - 切换调试面板显示状态
     */
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    /**
     * @function addLogEntry - 添加日志条目
     * @param {Object} logEntry - 日志条目
     */
    addLogEntry(logEntry) {
        // 避免重复添加
        const exists = this.logBuffer.some(entry => 
            entry.timestamp === logEntry.timestamp && 
            entry.message === logEntry.message
        );
        
        if (exists) return;

        this.logBuffer.push(logEntry);
        
        // 限制缓冲区大小
        if (this.logBuffer.length > this.maxLogEntries) {
            this.logBuffer = this.logBuffer.slice(-this.maxLogEntries);
        }

        // 如果面板可见，更新显示
        if (this.isVisible) {
            this.appendLogToDisplay(logEntry);
            this.updateLogCount();
        }
    }

    /**
     * @function appendLogToDisplay - 将日志条目添加到显示区域
     * @param {Object} logEntry - 日志条目
     */
    appendLogToDisplay(logEntry) {
        // 检查过滤器
        if (this.currentFilter !== 'ALL' && logEntry.level !== this.currentFilter) {
            return;
        }

        const logsContainer = document.getElementById('aisp-debug-logs');
        if (!logsContainer) return;

        const logElement = this.createLogElement(logEntry);
        logsContainer.appendChild(logElement);

        // 自动滚动到底部
        if (this.autoScroll) {
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }

        // 限制显示的日志数量
        const logElements = logsContainer.children;
        if (logElements.length > 200) {
            logsContainer.removeChild(logElements[0]);
        }
    }

    /**
     * @function createLogElement - 创建日志元素
     * @param {Object} logEntry - 日志条目
     * @returns {HTMLElement} 日志元素
     */
    createLogElement(logEntry) {
        const logDiv = document.createElement('div');
        logDiv.className = `aisp-debug-log-entry aisp-debug-log-${logEntry.level.toLowerCase()}`;
        
        const timestamp = new Date(logEntry.timestamp).toLocaleTimeString();
        const levelIcon = this.getLevelIcon(logEntry.level);
        
        logDiv.innerHTML = `
            <div class="aisp-debug-log-header">
                <span class="aisp-debug-log-time">${timestamp}</span>
                <span class="aisp-debug-log-level">${levelIcon} ${logEntry.level}</span>
            </div>
            <div class="aisp-debug-log-message">${this.escapeHtml(logEntry.message)}</div>
            ${logEntry.data ? `<div class="aisp-debug-log-data">${this.formatLogData(logEntry.data)}</div>` : ''}
        `;

        // 点击展开/收起详细信息
        logDiv.addEventListener('click', () => {
            logDiv.classList.toggle('expanded');
        });

        return logDiv;
    }

    /**
     * @function getLevelIcon - 获取日志级别图标
     * @param {string} level - 日志级别
     * @returns {string} 图标
     */
    getLevelIcon(level) {
        const icons = {
            ERROR: '❌',
            WARN: '⚠️',
            INFO: 'ℹ️',
            DEBUG: '🔍'
        };
        return icons[level] || '📝';
    }

    /**
     * @function formatLogData - 格式化日志数据
     * @param {Object} data - 日志数据
     * @returns {string} 格式化后的数据
     */
    formatLogData(data) {
        try {
            return `<pre>${JSON.stringify(data, null, 2)}</pre>`;
        } catch (error) {
            return `<pre>${String(data)}</pre>`;
        }
    }

    /**
     * @function escapeHtml - 转义HTML字符
     * @param {string} text - 原始文本
     * @returns {string} 转义后的文本
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * @function refreshLogDisplay - 刷新日志显示
     */
    refreshLogDisplay() {
        const logsContainer = document.getElementById('aisp-debug-logs');
        if (!logsContainer) return;

        // 清空现有显示
        logsContainer.innerHTML = '';

        // 过滤并显示日志
        const filteredLogs = this.currentFilter === 'ALL' 
            ? this.logBuffer 
            : this.logBuffer.filter(log => log.level === this.currentFilter);

        filteredLogs.slice(-200).forEach(log => {
            const logElement = this.createLogElement(log);
            logsContainer.appendChild(logElement);
        });

        // 滚动到底部
        if (this.autoScroll) {
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }

        this.updateLogCount();
    }

    /**
     * @function updateLogCount - 更新日志计数显示
     */
    updateLogCount() {
        const countElement = document.getElementById('aisp-debug-log-count');
        if (countElement) {
            const filteredCount = this.currentFilter === 'ALL' 
                ? this.logBuffer.length 
                : this.logBuffer.filter(log => log.level === this.currentFilter).length;
            countElement.textContent = `${filteredCount} 条日志`;
        }
    }

    /**
     * @function refreshAPIDisplay - 刷新API显示
     */
    refreshAPIDisplay() {
        if (typeof aisp_getRuntimeData !== 'function') return;

        const runtimeData = aisp_getRuntimeData({ type: 'api' });
        const apiCalls = runtimeData.apiCalls || [];

        // 更新统计信息
        const totalRequests = document.getElementById('aisp-debug-total-requests');
        const successRate = document.getElementById('aisp-debug-success-rate');
        const avgResponse = document.getElementById('aisp-debug-avg-response');

        if (totalRequests) totalRequests.textContent = apiCalls.length;

        const completedCalls = apiCalls.filter(call => call.completed);
        const successfulCalls = completedCalls.filter(call => call.response?.success);
        const successRateValue = completedCalls.length > 0 ?
            Math.round((successfulCalls.length / completedCalls.length) * 100) : 0;

        if (successRate) successRate.textContent = `${successRateValue}%`;

        const avgResponseTime = runtimeData.systemStats?.averageResponseTime || 0;
        if (avgResponse) avgResponse.textContent = `${avgResponseTime}ms`;

        // 更新API调用列表
        const apiList = document.getElementById('aisp-debug-api-list');
        if (apiList) {
            apiList.innerHTML = '';

            apiCalls.slice(-20).reverse().forEach(call => {
                const callElement = this.createAPICallElement(call);
                apiList.appendChild(callElement);
            });
        }
    }

    /**
     * @function refreshStreamDisplay - 刷新流式传输显示
     */
    refreshStreamDisplay() {
        if (typeof aisp_getRuntimeData !== 'function') return;

        const runtimeData = aisp_getRuntimeData({ type: 'stream' });
        const streamChunks = runtimeData.streamChunks || [];

        // 更新统计信息
        const totalChunks = document.getElementById('aisp-debug-total-chunks');
        const activeStreams = document.getElementById('aisp-debug-active-streams');

        if (totalChunks) totalChunks.textContent = streamChunks.length;

        // 计算活跃流数量
        const uniqueStreams = new Set(streamChunks.map(chunk => chunk.streamId));
        if (activeStreams) activeStreams.textContent = uniqueStreams.size;

        // 更新流式数据列表
        const streamList = document.getElementById('aisp-debug-stream-list');
        if (streamList) {
            streamList.innerHTML = '';

            streamChunks.slice(-20).reverse().forEach(chunk => {
                const chunkElement = this.createStreamChunkElement(chunk);
                streamList.appendChild(chunkElement);
            });
        }
    }

    /**
     * @function refreshPerformanceDisplay - 刷新性能显示
     */
    refreshPerformanceDisplay() {
        if (typeof aisp_getRuntimeData !== 'function') return;

        const runtimeData = aisp_getRuntimeData({ type: 'performance' });
        const performanceMetrics = runtimeData.performanceMetrics || [];

        // 更新性能图表（简化版本）
        const chartContainer = document.getElementById('aisp-debug-performance-chart');
        if (chartContainer) {
            this.renderPerformanceChart(performanceMetrics);
        }

        // 更新性能数据列表
        const performanceList = document.getElementById('aisp-debug-performance-list');
        if (performanceList) {
            performanceList.innerHTML = '';

            performanceMetrics.slice(-10).reverse().forEach(metric => {
                const metricElement = this.createPerformanceMetricElement(metric);
                performanceList.appendChild(metricElement);
            });
        }
    }

    /**
     * @function refreshMemoryDisplay - 刷新内存显示
     */
    refreshMemoryDisplay() {
        if (typeof aisp_getRuntimeData !== 'function') return;

        const runtimeData = aisp_getRuntimeData({ type: 'memory' });
        const memoryUsage = runtimeData.memoryUsage || [];

        // 更新内存图表（简化版本）
        const chartContainer = document.getElementById('aisp-debug-memory-chart');
        if (chartContainer) {
            this.renderMemoryChart(memoryUsage);
        }

        // 更新内存数据列表
        const memoryList = document.getElementById('aisp-debug-memory-list');
        if (memoryList) {
            memoryList.innerHTML = '';

            memoryUsage.slice(-10).reverse().forEach(usage => {
                const usageElement = this.createMemoryUsageElement(usage);
                memoryList.appendChild(usageElement);
            });
        }
    }

    /**
     * @function updateRuntimeDisplay - 更新运行时显示
     * @param {Object} runtimeData - 运行时数据
     */
    updateRuntimeDisplay(runtimeData) {
        if (!this.isVisible) return;

        // 根据当前标签页更新相应显示
        switch (this.currentTab) {
            case 'api':
                this.refreshAPIDisplay();
                break;
            case 'stream':
                this.refreshStreamDisplay();
                break;
            case 'performance':
                this.refreshPerformanceDisplay();
                break;
            case 'memory':
                this.refreshMemoryDisplay();
                break;
        }
    }

    /**
     * @function createAPICallElement - 创建API调用元素
     * @param {Object} call - API调用数据
     * @returns {HTMLElement} API调用元素
     */
    createAPICallElement(call) {
        const callDiv = document.createElement('div');
        callDiv.className = `aisp-debug-data-item ${call.completed ? (call.response?.success ? 'success' : 'error') : 'pending'}`;

        const timestamp = new Date(call.timestamp).toLocaleTimeString();
        const duration = call.duration ? `${call.duration}ms` : '进行中...';
        const status = call.response ? call.response.status : '等待中';

        callDiv.innerHTML = `
            <div class="aisp-debug-data-header">
                <span class="aisp-debug-data-time">${timestamp}</span>
                <span class="aisp-debug-data-status">${status}</span>
                <span class="aisp-debug-data-duration">${duration}</span>
            </div>
            <div class="aisp-debug-data-content">
                <strong>${call.apiName}</strong> ${call.method} ${call.url}
            </div>
        `;

        return callDiv;
    }

    /**
     * @function createStreamChunkElement - 创建流式数据块元素
     * @param {Object} chunk - 流式数据块
     * @returns {HTMLElement} 数据块元素
     */
    createStreamChunkElement(chunk) {
        const chunkDiv = document.createElement('div');
        chunkDiv.className = `aisp-debug-data-item ${chunk.isComplete ? 'complete' : 'streaming'}`;

        const timestamp = new Date(chunk.timestamp).toLocaleTimeString();
        const content = chunk.content ? chunk.content.substring(0, 100) + '...' : '';

        chunkDiv.innerHTML = `
            <div class="aisp-debug-data-header">
                <span class="aisp-debug-data-time">${timestamp}</span>
                <span class="aisp-debug-data-status">块 #${chunk.chunkIndex}</span>
                <span class="aisp-debug-data-size">${chunk.chunkSize} bytes</span>
            </div>
            <div class="aisp-debug-data-content">
                <strong>流ID:</strong> ${chunk.streamId}<br>
                <strong>内容:</strong> ${content}
            </div>
        `;

        return chunkDiv;
    }

    /**
     * @function createPerformanceMetricElement - 创建性能指标元素
     * @param {Object} metric - 性能指标
     * @returns {HTMLElement} 性能指标元素
     */
    createPerformanceMetricElement(metric) {
        const metricDiv = document.createElement('div');
        metricDiv.className = 'aisp-debug-data-item';

        const timestamp = new Date(metric.timestamp).toLocaleTimeString();
        const memoryUsage = metric.memory ?
            `${Math.round(metric.memory.usedJSHeapSize / 1024 / 1024)}MB` : 'N/A';

        metricDiv.innerHTML = `
            <div class="aisp-debug-data-header">
                <span class="aisp-debug-data-time">${timestamp}</span>
                <span class="aisp-debug-data-status">内存: ${memoryUsage}</span>
            </div>
            <div class="aisp-debug-data-content">
                <strong>导航:</strong> ${metric.navigation?.type || 'N/A'}<br>
                <strong>加载时间:</strong> ${metric.timing?.loadComplete || 'N/A'}ms
            </div>
        `;

        return metricDiv;
    }

    /**
     * @function createMemoryUsageElement - 创建内存使用元素
     * @param {Object} usage - 内存使用数据
     * @returns {HTMLElement} 内存使用元素
     */
    createMemoryUsageElement(usage) {
        const usageDiv = document.createElement('div');
        const usagePercent = usage.heap?.usagePercent || 0;
        usageDiv.className = `aisp-debug-data-item ${usagePercent > 80 ? 'warning' : ''}`;

        const timestamp = new Date(usage.timestamp).toLocaleTimeString();
        const usedMB = usage.heap ? Math.round(usage.heap.used / 1024 / 1024) : 0;
        const totalMB = usage.heap ? Math.round(usage.heap.total / 1024 / 1024) : 0;

        usageDiv.innerHTML = `
            <div class="aisp-debug-data-header">
                <span class="aisp-debug-data-time">${timestamp}</span>
                <span class="aisp-debug-data-status">${usagePercent}%</span>
                <span class="aisp-debug-data-size">${usedMB}/${totalMB}MB</span>
            </div>
            <div class="aisp-debug-data-content">
                <strong>DOM节点:</strong> ${usage.domNodes || 'N/A'}<br>
                <strong>事件监听器:</strong> ${usage.eventListeners || 'N/A'}
            </div>
        `;

        return usageDiv;
    }

    /**
     * @function renderPerformanceChart - 渲染性能图表
     * @param {Array} metrics - 性能指标数组
     */
    renderPerformanceChart(metrics) {
        const chartContainer = document.getElementById('aisp-debug-performance-chart');
        if (!chartContainer || metrics.length === 0) return;

        // 简化的图表实现
        const latest = metrics.slice(-10);
        const maxMemory = Math.max(...latest.map(m => m.memory?.usedJSHeapSize || 0));

        let chartHTML = '<div class="aisp-debug-simple-chart">';
        latest.forEach((metric, index) => {
            const height = maxMemory > 0 ?
                Math.round((metric.memory?.usedJSHeapSize || 0) / maxMemory * 100) : 0;
            chartHTML += `<div class="aisp-debug-chart-bar" style="height: ${height}%" title="${new Date(metric.timestamp).toLocaleTimeString()}"></div>`;
        });
        chartHTML += '</div>';

        chartContainer.innerHTML = chartHTML;
    }

    /**
     * @function renderMemoryChart - 渲染内存图表
     * @param {Array} usage - 内存使用数组
     */
    renderMemoryChart(usage) {
        const chartContainer = document.getElementById('aisp-debug-memory-chart');
        if (!chartContainer || usage.length === 0) return;

        // 简化的图表实现
        const latest = usage.slice(-10);

        let chartHTML = '<div class="aisp-debug-simple-chart">';
        latest.forEach((mem, index) => {
            const height = mem.heap?.usagePercent || 0;
            const color = height > 80 ? '#ff4444' : height > 60 ? '#ff8800' : '#00ff00';
            chartHTML += `<div class="aisp-debug-chart-bar" style="height: ${height}%; background-color: ${color}" title="${new Date(mem.timestamp).toLocaleTimeString()}: ${height}%"></div>`;
        });
        chartHTML += '</div>';

        chartContainer.innerHTML = chartHTML;
    }

    /**
     * @function clearCurrentTabData - 清空当前标签页数据
     */
    clearCurrentTabData() {
        switch (this.currentTab) {
            case 'logs':
                this.clearLogs();
                break;
            case 'api':
                if (typeof aisp_clearRuntimeData === 'function') {
                    aisp_clearRuntimeData('api');
                }
                this.refreshAPIDisplay();
                break;
            case 'stream':
                if (typeof aisp_clearRuntimeData === 'function') {
                    aisp_clearRuntimeData('stream');
                }
                this.refreshStreamDisplay();
                break;
            case 'performance':
                if (typeof aisp_clearRuntimeData === 'function') {
                    aisp_clearRuntimeData('performance');
                }
                this.refreshPerformanceDisplay();
                break;
            case 'memory':
                if (typeof aisp_clearRuntimeData === 'function') {
                    aisp_clearRuntimeData('memory');
                }
                this.refreshMemoryDisplay();
                break;
        }
    }

    /**
     * @function exportCurrentTabData - 导出当前标签页数据
     */
    exportCurrentTabData() {
        let data = null;
        let filename = '';

        switch (this.currentTab) {
            case 'logs':
                this.exportLogs();
                return;
            case 'api':
                if (typeof aisp_getRuntimeData === 'function') {
                    data = aisp_getRuntimeData({ type: 'api' });
                    filename = 'aisp-api-data';
                }
                break;
            case 'stream':
                if (typeof aisp_getRuntimeData === 'function') {
                    data = aisp_getRuntimeData({ type: 'stream' });
                    filename = 'aisp-stream-data';
                }
                break;
            case 'performance':
                if (typeof aisp_getRuntimeData === 'function') {
                    data = aisp_getRuntimeData({ type: 'performance' });
                    filename = 'aisp-performance-data';
                }
                break;
            case 'memory':
                if (typeof aisp_getRuntimeData === 'function') {
                    data = aisp_getRuntimeData({ type: 'memory' });
                    filename = 'aisp-memory-data';
                }
                break;
        }

        if (data) {
            const jsonData = JSON.stringify(data, null, 2);
            const blob = new Blob([jsonData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `${filename}-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            URL.revokeObjectURL(url);
        }
    }

    /**
     * @function clearLogs - 清空日志
     */
    clearLogs() {
        this.logBuffer = [];
        this.refreshLogDisplay();

        // 同时清空运行时日志数据
        if (typeof aisp_clearRuntimeData === 'function') {
            aisp_clearRuntimeData();
        }
    }

    /**
     * @function exportLogs - 导出日志
     */
    exportLogs() {
        const logs = this.currentFilter === 'ALL' 
            ? this.logBuffer 
            : this.logBuffer.filter(log => log.level === this.currentFilter);

        const logText = logs.map(log => {
            const timestamp = new Date(log.timestamp).toISOString();
            const data = log.data ? `\n${JSON.stringify(log.data, null, 2)}` : '';
            return `[${timestamp}] [${log.level}] ${log.message}${data}`;
        }).join('\n\n');

        const blob = new Blob([logText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `aisp-logs-${new Date().toISOString().split('T')[0]}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);
    }
}

// 创建全局调试面板实例
let debugPanelManager = null;

/**
 * @function aisp_initializeDebugPanel - 初始化调试面板
 */
function aisp_initializeDebugPanel() {
    if (!debugPanelManager) {
        debugPanelManager = new DebugPanelManager();
    }
}

/**
 * @function aisp_showDebugPanel - 显示调试面板
 */
function aisp_showDebugPanel() {
    if (debugPanelManager) {
        debugPanelManager.show();
    }
}

/**
 * @function aisp_hideDebugPanel - 隐藏调试面板
 */
function aisp_hideDebugPanel() {
    if (debugPanelManager) {
        debugPanelManager.hide();
    }
}

/**
 * @function aisp_toggleDebugPanel - 切换调试面板
 */
function aisp_toggleDebugPanel() {
    if (debugPanelManager) {
        debugPanelManager.toggle();
    }
}

// #endregion

console.log('调试面板组件已加载');
