/**
 * @file AI回复语言选择器组件
 * @description 为AI回复建议和模板系统提供独立的语言选择功能
 */

// #region 全局变量
let rls_currentReplyLanguage = 'zh_CN';
let rls_changeListeners = [];
let rls_isInitialized = false;

// 支持的回复语言
const REPLY_LANGUAGES = {
    'zh_CN': '中文',
    'en_US': 'English'
};

// 语言选择器实例
let rls_selectorInstance = null;
// #endregion

// #region 初始化
/**
 * @function rls_initialize - 初始化回复语言选择器
 * @description 初始化回复语言选择器组件
 * @returns {Promise<boolean>} 是否初始化成功
 */
async function rls_initialize() {
    if (rls_isInitialized) return true;
    
    try {
        console.log('🌐 回复语言选择器初始化中...');
        
        // 从存储中恢复语言设置
        const savedLanguage = await rls_loadLanguageFromStorage();
        if (savedLanguage && REPLY_LANGUAGES[savedLanguage]) {
            rls_currentReplyLanguage = savedLanguage;
        }
        
        rls_isInitialized = true;
        console.log('✅ 回复语言选择器初始化完成，当前语言:', rls_currentReplyLanguage);
        return true;
        
    } catch (error) {
        console.error('❌ 回复语言选择器初始化失败:', error);
        return false;
    }
}

/**
 * @function rls_loadLanguageFromStorage - 从存储中加载语言设置
 * @description 从Chrome存储中加载用户的回复语言偏好
 * @returns {Promise<string>} 保存的语言代码
 */
async function rls_loadLanguageFromStorage() {
    try {
        if (typeof chrome !== 'undefined' && chrome.storage) {
            const result = await chrome.storage.local.get(['aisp_reply_language']);
            return result.aisp_reply_language || 'zh_CN';
        }
        
        // 后备方案：使用localStorage
        return localStorage.getItem('aisp_reply_language') || 'zh_CN';
    } catch (error) {
        console.warn('加载回复语言设置失败:', error);
        return 'zh_CN';
    }
}

/**
 * @function rls_saveLanguageToStorage - 保存语言设置到存储
 * @description 将用户的回复语言偏好保存到Chrome存储
 * @param {string} language - 语言代码
 * @returns {Promise<boolean>} 是否保存成功
 */
async function rls_saveLanguageToStorage(language) {
    try {
        if (typeof chrome !== 'undefined' && chrome.storage) {
            await chrome.storage.local.set({ aisp_reply_language: language });
        } else {
            // 后备方案：使用localStorage
            localStorage.setItem('aisp_reply_language', language);
        }
        
        console.log('✅ 回复语言设置已保存:', language);
        return true;
    } catch (error) {
        console.error('❌ 保存回复语言设置失败:', error);
        return false;
    }
}
// #endregion

// #region 核心功能
/**
 * @function rls_setReplyLanguage - 设置回复语言
 * @description 设置AI回复生成的语言
 * @param {string} language - 语言代码
 * @param {boolean} saveToStorage - 是否保存到存储
 * @returns {Promise<boolean>} 是否设置成功
 */
async function rls_setReplyLanguage(language, saveToStorage = true) {
    if (!REPLY_LANGUAGES[language]) {
        console.warn('不支持的回复语言:', language);
        return false;
    }
    
    if (language === rls_currentReplyLanguage) {
        return true;
    }
    
    const previousLanguage = rls_currentReplyLanguage;
    rls_currentReplyLanguage = language;
    
    // 保存到存储
    if (saveToStorage) {
        await rls_saveLanguageToStorage(language);
    }
    
    // 通知监听器
    rls_notifyLanguageChange(language, previousLanguage);
    
    console.log(`🌐 回复语言已切换: ${previousLanguage} -> ${language}`);
    return true;
}

/**
 * @function rls_getCurrentReplyLanguage - 获取当前回复语言
 * @description 获取当前设置的AI回复语言
 * @returns {string} 当前回复语言代码
 */
function rls_getCurrentReplyLanguage() {
    return rls_currentReplyLanguage;
}

/**
 * @function rls_getSupportedLanguages - 获取支持的回复语言
 * @description 获取所有支持的回复语言列表
 * @returns {Object} 支持的语言对象
 */
function rls_getSupportedLanguages() {
    return { ...REPLY_LANGUAGES };
}

/**
 * @function rls_getLanguageName - 获取语言名称
 * @description 根据语言代码获取显示名称
 * @param {string} language - 语言代码
 * @returns {string} 语言显示名称
 */
function rls_getLanguageName(language) {
    return REPLY_LANGUAGES[language] || language;
}
// #endregion

// #region 事件监听
/**
 * @function rls_addChangeListener - 添加语言变更监听器
 * @description 添加回复语言变更时的回调函数
 * @param {Function} listener - 监听器函数 (newLang, oldLang) => void
 */
function rls_addChangeListener(listener) {
    if (typeof listener === 'function') {
        rls_changeListeners.push(listener);
    }
}

/**
 * @function rls_removeChangeListener - 移除语言变更监听器
 * @description 移除指定的语言变更监听器
 * @param {Function} listener - 要移除的监听器函数
 */
function rls_removeChangeListener(listener) {
    const index = rls_changeListeners.indexOf(listener);
    if (index > -1) {
        rls_changeListeners.splice(index, 1);
    }
}

/**
 * @function rls_notifyLanguageChange - 通知语言变更
 * @description 通知所有监听器回复语言已变更
 * @param {string} newLanguage - 新语言
 * @param {string} oldLanguage - 旧语言
 */
function rls_notifyLanguageChange(newLanguage, oldLanguage) {
    rls_changeListeners.forEach(listener => {
        try {
            listener(newLanguage, oldLanguage);
        } catch (error) {
            console.error('回复语言变更监听器执行失败:', error);
        }
    });
}
// #endregion

// #region UI组件
/**
 * @function rls_createLanguageSelector - 创建语言选择器
 * @description 创建一个回复语言选择下拉框
 * @param {Object} options - 选项配置
 * @returns {HTMLElement} 语言选择器容器元素
 */
function rls_createLanguageSelector(options = {}) {
    if (typeof document === 'undefined') return null;
    
    const {
        className = 'rls-language-selector',
        id = 'rls-language-selector',
        label = '回复语言',
        showLabel = true,
        compact = false,
        onChange = null
    } = options;
    
    // 创建容器
    const container = document.createElement('div');
    container.className = `${className}-container ${compact ? 'compact' : ''}`;
    
    // 创建标签
    if (showLabel) {
        const labelElement = document.createElement('label');
        labelElement.className = `${className}-label`;
        labelElement.textContent = label;
        labelElement.setAttribute('for', id);
        container.appendChild(labelElement);
    }
    
    // 创建选择器
    const select = document.createElement('select');
    select.className = className;
    select.id = id;
    
    // 添加选项
    Object.entries(REPLY_LANGUAGES).forEach(([code, name]) => {
        const option = document.createElement('option');
        option.value = code;
        option.textContent = name;
        option.selected = code === rls_currentReplyLanguage;
        select.appendChild(option);
    });
    
    // 添加变更事件监听器
    select.addEventListener('change', async (event) => {
        const newLanguage = event.target.value;
        const success = await rls_setReplyLanguage(newLanguage);
        
        if (!success) {
            // 如果切换失败，恢复到之前的选择
            event.target.value = rls_currentReplyLanguage;
        }
        
        if (onChange && typeof onChange === 'function') {
            onChange(newLanguage, success);
        }
    });
    
    container.appendChild(select);
    
    // 保存实例引用
    rls_selectorInstance = { container, select };
    
    return container;
}

/**
 * @function rls_updateSelector - 更新语言选择器
 * @description 更新现有的语言选择器状态
 */
function rls_updateSelector() {
    if (rls_selectorInstance && rls_selectorInstance.select) {
        rls_selectorInstance.select.value = rls_currentReplyLanguage;
    }
}

/**
 * @function rls_createCompactSelector - 创建紧凑型语言选择器
 * @description 创建一个紧凑型的语言切换按钮组
 * @param {Object} options - 选项配置
 * @returns {HTMLElement} 紧凑型选择器元素
 */
function rls_createCompactSelector(options = {}) {
    if (typeof document === 'undefined') return null;
    
    const {
        className = 'rls-compact-selector',
        showIcons = true,
        onChange = null
    } = options;
    
    const container = document.createElement('div');
    container.className = className;
    
    // 语言图标映射
    const languageIcons = {
        'zh_CN': '🇨🇳',
        'en_US': '🇺🇸'
    };
    
    Object.entries(REPLY_LANGUAGES).forEach(([code, name]) => {
        const button = document.createElement('button');
        button.className = `${className}-btn ${code === rls_currentReplyLanguage ? 'active' : ''}`;
        button.type = 'button';
        button.title = name;
        button.dataset.language = code;
        
        if (showIcons) {
            button.innerHTML = `${languageIcons[code] || '🌐'} <span>${name}</span>`;
        } else {
            button.textContent = name;
        }
        
        button.addEventListener('click', async () => {
            const success = await rls_setReplyLanguage(code);
            
            if (success) {
                // 更新按钮状态
                container.querySelectorAll(`.${className}-btn`).forEach(btn => {
                    btn.classList.remove('active');
                });
                button.classList.add('active');
            }
            
            if (onChange && typeof onChange === 'function') {
                onChange(code, success);
            }
        });
        
        container.appendChild(button);
    });
    
    return container;
}
// #endregion

// #region 自动初始化
// 如果在浏览器环境中，自动初始化
if (typeof window !== 'undefined') {
    // 延迟初始化，确保DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            rls_initialize();
        });
    } else {
        rls_initialize();
    }
}
// #endregion

console.log('AI回复语言选择器组件已加载');
