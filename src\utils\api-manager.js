/**
 * @file API管理器
 * @description 统一管理所有API调用，包括Gemini API的初始化和使用
 */

// #region API管理器类

/**
 * @class APIManager - API管理器
 * @description 统一管理和协调所有API调用
 */
class APIManager {
    /**
     * @constructor
     */
    constructor() {
        this.geminiAPI = null;
        this.configManager = null;
        this.initialized = false;
        this.initializationPromise = null;
        
        console.log('API管理器创建');
    }

    /**
     * @function initialize - 初始化API管理器
     * @description 初始化配置管理器和Gemini API
     * @returns {Promise<void>}
     */
    async initialize() {
        if (this.initialized) return;
        
        // 如果正在初始化，返回现有的Promise
        if (this.initializationPromise) {
            return this.initializationPromise;
        }
        
        this.initializationPromise = this._doInitialize();
        return this.initializationPromise;
    }

    /**
     * @function _doInitialize - 执行初始化
     * @description 实际的初始化逻辑
     * @returns {Promise<void>}
     * @private
     */
    async _doInitialize() {
        try {
            console.log('开始初始化API管理器...');
            
            // 初始化配置管理器
            this.configManager = getConfigManager();
            await this.configManager.initialize();
            
            // 初始化Gemini API
            await this._initializeGeminiAPI();
            
            // 设置配置变化监听
            this._setupConfigListeners();
            
            this.initialized = true;
            console.log('API管理器初始化完成');
            
        } catch (error) {
            console.error('API管理器初始化失败:', error);
            this.initializationPromise = null;
            throw error;
        }
    }

    /**
     * @function _initializeGeminiAPI - 初始化Gemini API
     * @description 使用硬编码的API密钥初始化Gemini API
     * @returns {Promise<void>}
     * @private
     */
    async _initializeGeminiAPI() {
        try {
            // 获取硬编码的API密钥状态
            const keyStatus = getApiKeyStatus();

            if (!keyStatus.gemini.available) {
                console.error('Gemini API密钥未正确配置:', keyStatus.gemini.status);
                console.error('请在 src/config/api-keys.js 中配置有效的API密钥');
                return;
            }

            const apiKey = keyStatus.gemini.key;
            console.log('使用硬编码的Gemini API密钥');

            // 获取性能配置
            const performanceSettings = await this.configManager.get(CONFIG_KEYS.PERFORMANCE_SETTINGS);

            // 创建Gemini API实例
            this.geminiAPI = createGeminiAPI(apiKey, {
                MAX_CACHE_SIZE: performanceSettings?.maxCacheSize || 150,
                DEFAULT_TIMEOUT: performanceSettings?.requestTimeout || 45000,
                MAX_RETRIES: performanceSettings?.retryAttempts || 3,
                MAX_INPUT_LENGTH: performanceSettings?.maxInputLength || 100000,
                MAX_OUTPUT_LENGTH: performanceSettings?.maxOutputLength || 8000,
                RATE_LIMIT_PER_MINUTE: performanceSettings?.rateLimit || 100
            });

            // 测试连接
            const isConnected = await this.geminiAPI.testConnection();
            if (isConnected) {
                console.log('✅ Gemini API连接成功');
            } else {
                console.warn('⚠️ Gemini API连接测试失败，但将继续尝试使用');
            }

        } catch (error) {
            console.error('初始化Gemini API失败:', error);
            this.geminiAPI = null;
        }
    }

    /**
     * @function _setupConfigListeners - 设置配置监听器
     * @description 监听配置变化并更新API设置
     * @private
     */
    _setupConfigListeners() {
        // 监听性能设置变化
        this.configManager.addListener(CONFIG_KEYS.PERFORMANCE_SETTINGS, async (newSettings) => {
            if (this.geminiAPI) {
                this.geminiAPI.updateConfig(newSettings);
                console.log('Gemini API配置已更新');
            }
        });

        // 注意：由于使用硬编码API密钥，不再监听密钥变化
        // 如需更换API密钥，请直接修改 src/config/api-keys.js 文件并重新加载扩展
    }

    /**
     * @function isReady - 检查API是否就绪
     * @description 检查Gemini API是否已初始化并可用
     * @returns {boolean} 是否就绪
     */
    isReady() {
        return this.initialized && this.geminiAPI !== null;
    }

    /**
     * @function ensureReady - 确保API就绪
     * @description 确保API已初始化，如果未初始化则抛出错误
     * @throws {Error} API未就绪时抛出错误
     * @private
     */
    _ensureReady() {
        if (!this.isReady()) {
            throw new Error('API管理器未就绪，请先调用initialize()方法');
        }
    }

    // #region 内容分析方法

    /**
     * @function analyzeContent - 分析内容
     * @description 对页面内容进行AI分析
     * @param {Object} contentData - 内容数据
     * @param {Object} options - 分析选项
     * @returns {Promise<Object>} 分析结果
     */
    async analyzeContent(contentData, options = {}) {
        await this.initialize();
        this._ensureReady();
        
        try {
            // 获取用户语言设置
            const userLanguage = await this.configManager.get(CONFIG_KEYS.USER_LANGUAGE, 'zh_CN');
            
            const analysisOptions = {
                language: userLanguage,
                ...options
            };
            
            return await this.geminiAPI.analyzeContent(contentData, analysisOptions);
            
        } catch (error) {
            console.error('内容分析失败:', error);
            throw this._formatError(error);
        }
    }

    /**
     * @function summarizeText - 文本摘要
     * @description 生成文本摘要
     * @param {string} text - 输入文本
     * @param {Object} options - 摘要选项
     * @returns {Promise<string>} 摘要文本
     */
    async summarizeText(text, options = {}) {
        await this.initialize();
        this._ensureReady();
        
        try {
            const userLanguage = await this.configManager.get(CONFIG_KEYS.USER_LANGUAGE, 'zh_CN');
            
            const summaryOptions = {
                language: userLanguage,
                ...options
            };
            
            return await this.geminiAPI.summarizeText(text, summaryOptions);
            
        } catch (error) {
            console.error('文本摘要失败:', error);
            throw this._formatError(error);
        }
    }

    /**
     * @function extractKeyPoints - 提取关键要点
     * @description 从内容中提取关键要点
     * @param {string} content - 输入内容
     * @param {Object} options - 提取选项
     * @returns {Promise<Array>} 要点列表
     */
    async extractKeyPoints(content, options = {}) {
        await this.initialize();
        this._ensureReady();
        
        try {
            const userLanguage = await this.configManager.get(CONFIG_KEYS.USER_LANGUAGE, 'zh_CN');
            
            const extractOptions = {
                language: userLanguage,
                ...options
            };
            
            return await this.geminiAPI.extractKeyPoints(content, extractOptions);
            
        } catch (error) {
            console.error('要点提取失败:', error);
            throw this._formatError(error);
        }
    }

    /**
     * @function generateReplies - 生成回复建议
     * @description 基于上下文生成回复建议
     * @param {string} context - 上下文内容
     * @param {Object} options - 生成选项
     * @returns {Promise<Array>} 回复建议列表
     */
    async generateReplies(context, options = {}) {
        await this.initialize();
        this._ensureReady();
        
        try {
            const userLanguage = await this.configManager.get(CONFIG_KEYS.USER_LANGUAGE, 'zh_CN');
            const replyStyle = await this.configManager.get(CONFIG_KEYS.REPLY_STYLE, 'professional');
            
            const replyOptions = {
                language: userLanguage,
                type: replyStyle,
                ...options
            };
            
            return await this.geminiAPI.generateReplies(context, replyOptions);
            
        } catch (error) {
            console.error('回复生成失败:', error);
            throw this._formatError(error);
        }
    }

    /**
     * @function analyzeSentiment - 情感分析
     * @description 分析文本的情感倾向
     * @param {string} content - 输入内容
     * @param {Object} options - 分析选项
     * @returns {Promise<Object>} 情感分析结果
     */
    async analyzeSentiment(content, options = {}) {
        await this.initialize();
        this._ensureReady();
        
        try {
            const userLanguage = await this.configManager.get(CONFIG_KEYS.USER_LANGUAGE, 'zh_CN');
            
            const sentimentOptions = {
                language: userLanguage,
                ...options
            };
            
            return await this.geminiAPI.analyzeSentiment(content, sentimentOptions);
            
        } catch (error) {
            console.error('情感分析失败:', error);
            throw this._formatError(error);
        }
    }

    /**
     * @function translateContent - 翻译内容
     * @description 翻译内容到目标语言
     * @param {string} content - 输入内容
     * @param {string} targetLanguage - 目标语言
     * @param {Object} options - 翻译选项
     * @returns {Promise<string>} 翻译结果
     */
    async translateContent(content, targetLanguage, options = {}) {
        await this.initialize();
        this._ensureReady();
        
        try {
            return await this.geminiAPI.translateContent(content, targetLanguage, options);
            
        } catch (error) {
            console.error('内容翻译失败:', error);
            throw this._formatError(error);
        }
    }

    // #endregion

    // #region 管理方法

    /**
     * @function getStats - 获取统计信息
     * @description 获取API使用统计信息
     * @returns {Object|null} 统计信息
     */
    getStats() {
        if (!this.geminiAPI) {
            return null;
        }
        
        return this.geminiAPI.getStats();
    }

    /**
     * @function clearCache - 清空缓存
     * @description 清空API缓存
     */
    clearCache() {
        if (this.geminiAPI) {
            this.geminiAPI.clearCache();
        }
    }

    /**
     * @function getApiKeyInfo - 获取API密钥信息
     * @description 获取当前使用的API密钥状态信息
     * @returns {Object} API密钥状态信息
     */
    getApiKeyInfo() {
        return getApiKeyStatus();
    }

    /**
     * @function reinitializeAPI - 重新初始化API
     * @description 重新初始化Gemini API（用于API密钥更新后）
     * @returns {Promise<void>}
     */
    async reinitializeAPI() {
        console.log('重新初始化Gemini API...');
        await this._initializeGeminiAPI();
    }

    /**
     * @function _formatError - 格式化错误
     * @description 格式化API错误为用户友好的消息
     * @param {Error} error - 原始错误
     * @returns {Error} 格式化后的错误
     * @private
     */
    _formatError(error) {
        const userFriendlyMessages = {
            [ERROR_TYPES.AUTH_ERROR]: '请检查API密钥是否正确',
            [ERROR_TYPES.RATE_LIMIT_ERROR]: '请求过于频繁，请稍后重试',
            [ERROR_TYPES.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
            [ERROR_TYPES.TIMEOUT_ERROR]: '请求超时，请稍后重试',
            [ERROR_TYPES.CONTENT_ERROR]: '内容格式错误，请检查输入',
            [ERROR_TYPES.API_ERROR]: 'AI服务暂时不可用，请稍后重试'
        };
        
        if (error.type && userFriendlyMessages[error.type]) {
            error.userMessage = userFriendlyMessages[error.type];
        } else {
            error.userMessage = '处理请求时发生错误，请稍后重试';
        }
        
        return error;
    }

    /**
     * @function testConnection - 测试API连接
     * @description 测试Gemini API连接状态
     * @returns {Promise<Object>} 连接测试结果
     */
    async testConnection() {
        try {
            await this.initialize();
            
            if (!this.isReady()) {
                return {
                    success: false,
                    error: 'API管理器未就绪',
                    timestamp: Date.now()
                };
            }

            const startTime = Date.now();
            const isConnected = await this.geminiAPI.testConnection();
            const responseTime = Date.now() - startTime;

            if (isConnected) {
                // 获取API统计信息
                const stats = this.geminiAPI.getStats();
                
                return {
                    success: true,
                    responseTime: responseTime,
                    model: this.geminiAPI.config.MODEL_NAME,
                    stats: stats,
                    timestamp: Date.now()
                };
            } else {
                return {
                    success: false,
                    error: 'API连接测试失败',
                    responseTime: responseTime,
                    timestamp: Date.now()
                };
            }

        } catch (error) {
            console.error('API连接测试过程中发生错误:', error);
            return {
                success: false,
                error: error.message || '未知错误',
                timestamp: Date.now()
            };
        }
    }

    // #endregion
}

// #endregion

// #region 单例实例

/**
 * API管理器单例实例
 */
let apiManagerInstance = null;

/**
 * @function getAPIManager - 获取API管理器实例
 * @description 获取API管理器的单例实例
 * @returns {APIManager} API管理器实例
 */
function getAPIManager() {
    if (!apiManagerInstance) {
        apiManagerInstance = new APIManager();
    }
    return apiManagerInstance;
}

// #endregion

// #region 导出

// 导出
if (typeof self !== 'undefined' && typeof importScripts === 'function') {
    // Service Worker环境
    self.APIManager = APIManager;
    self.getAPIManager = getAPIManager;
} else if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        APIManager,
        getAPIManager
    };
} else if (typeof window !== 'undefined') {
    // 浏览器环境
    window.APIManager = APIManager;
    window.getAPIManager = getAPIManager;
}

// #endregion

console.log('API管理器模块已加载');
