/**
 * @file AI Side Panel 性能优化工具
 * @description 提供API调用优化、内存使用优化、界面渲染优化等功能
 */

// #region 全局变量
let aisp_isInitialized = false;
let aisp_cacheManager = null;
let aisp_requestQueue = new Map();
let aisp_performanceMetrics = {
    apiCalls: { total: 0, cached: 0, failed: 0 },
    memoryUsage: { peak: 0, current: 0 },
    renderTime: { total: 0, count: 0, average: 0 },
    startTime: Date.now()
};

// 性能配置
const AISP_PERFORMANCE_CONFIG = {
    // API优化配置
    API_OPTIMIZATION: {
        enableCaching: true,
        enableDeduplication: true,
        enableBatching: true,
        requestTimeout: 30000,
        retryAttempts: 3,
        retryDelay: 1000
    },
    // 内存优化配置
    MEMORY_OPTIMIZATION: {
        enableGarbageCollection: true,
        memoryCheckInterval: 60000,
        memoryThreshold: 50 * 1024 * 1024, // 50MB
        cleanupInterval: 300000 // 5分钟
    },
    // 渲染优化配置
    RENDER_OPTIMIZATION: {
        enableVirtualScrolling: true,
        enableDebouncing: true,
        debounceDelay: 300,
        throttleDelay: 100,
        maxRenderTime: 16 // 60fps
    }
};

// 请求去重映射
const AISP_REQUEST_DEDUPLICATION = new Map();

// 防抖和节流函数映射
const AISP_DEBOUNCED_FUNCTIONS = new Map();
const AISP_THROTTLED_FUNCTIONS = new Map();
// #endregion

// #region 初始化
/**
 * @function aisp_initialize - 初始化性能优化器
 * @description 初始化性能优化工具，包括缓存管理器、内存监控和性能监控
 * @returns {Promise<boolean>} 是否初始化成功
 */
async function aisp_initialize() {
    if (aisp_isInitialized) return true;

    try {
        console.log('⚡ 性能优化器初始化中...');

        // 获取缓存管理器
        if (typeof getCacheManager === 'function') {
            aisp_cacheManager = getCacheManager();
            await aisp_cacheManager.initialize();
        } else {
            console.warn('⚠️ 缓存管理器不可用');
        }

        // 启动内存监控
        if (AISP_PERFORMANCE_CONFIG.MEMORY_OPTIMIZATION.enableGarbageCollection) {
            aisp_startMemoryMonitoring();
        }

        // 启动性能监控
        aisp_startPerformanceMonitoring();

        aisp_isInitialized = true;
        console.log('✅ 性能优化器初始化完成');
        return true;

    } catch (error) {
        console.error('❌ 性能优化器初始化失败:', error);
        return false;
    }
}
// #endregion

// #region API优化
/**
 * @function aisp_optimizeApiCall - 优化API调用
 * @description 为API调用添加缓存、去重、重试等优化处理
 * @param {Function} apiFunction - API函数
 * @param {Object} options - 优化选项配置
 * @returns {Function} 优化后的API函数
 */
function aisp_optimizeApiCall(apiFunction, options = {}) {
    const {
        cacheType = 'api_responses',
        cacheKey = 'default',
        cacheTTL = null,
        enableDeduplication = AISP_PERFORMANCE_CONFIG.API_OPTIMIZATION.enableDeduplication,
        enableRetry = true,
        timeout = AISP_PERFORMANCE_CONFIG.API_OPTIMIZATION.requestTimeout
    } = options;

    return async function(...args) {
        const startTime = performance.now();

        try {
            // 生成请求标识
            const requestId = aisp_generateRequestId(apiFunction.name, args);

            // 检查缓存
            if (aisp_cacheManager && AISP_PERFORMANCE_CONFIG.API_OPTIMIZATION.enableCaching) {
                const cached = aisp_cacheManager.get(cacheType, requestId);
                if (cached !== null) {
                    aisp_performanceMetrics.apiCalls.cached++;
                    console.log(`🎯 API缓存命中: ${apiFunction.name}`);
                    return cached;
                }
            }

            // 请求去重
            if (enableDeduplication && AISP_REQUEST_DEDUPLICATION.has(requestId)) {
                console.log(`🔄 API请求去重: ${apiFunction.name}`);
                return await AISP_REQUEST_DEDUPLICATION.get(requestId);
            }

            // 创建请求Promise
            const requestPromise = aisp_executeWithTimeout(apiFunction, args, timeout);

            // 添加到去重映射
            if (enableDeduplication) {
                AISP_REQUEST_DEDUPLICATION.set(requestId, requestPromise);
            }

            // 执行请求
            const result = await requestPromise;

            // 缓存结果
            if (aisp_cacheManager && result !== null) {
                aisp_cacheManager.set(cacheType, requestId, result, cacheTTL);
            }

            // 更新统计
            aisp_performanceMetrics.apiCalls.total++;

            return result;

        } catch (error) {
            aisp_performanceMetrics.apiCalls.failed++;

            // 重试机制
            if (enableRetry && options.retryCount < AISP_PERFORMANCE_CONFIG.API_OPTIMIZATION.retryAttempts) {
                console.log(`🔄 API重试: ${apiFunction.name} (${options.retryCount + 1}/${AISP_PERFORMANCE_CONFIG.API_OPTIMIZATION.retryAttempts})`);

                await aisp_delay(AISP_PERFORMANCE_CONFIG.API_OPTIMIZATION.retryDelay * Math.pow(2, options.retryCount));

                return await aisp_optimizeApiCall(apiFunction, {
                    ...options,
                    retryCount: (options.retryCount || 0) + 1
                }).apply(this, args);
            }

            throw error;

        } finally {
            // 清理去重映射
            if (enableDeduplication) {
                const requestId = aisp_generateRequestId(apiFunction.name, args);
                AISP_REQUEST_DEDUPLICATION.delete(requestId);
            }

            // 记录执行时间
            const executionTime = performance.now() - startTime;
            console.log(`⏱️ API执行时间: ${apiFunction.name} - ${executionTime.toFixed(2)}ms`);
        }
    };
}

/**
 * @function aisp_executeWithTimeout - 带超时的执行
 * @description 为函数执行添加超时控制，防止长时间阻塞
 * @param {Function} fn - 要执行的函数
 * @param {Array} args - 函数参数数组
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {Promise} 执行结果Promise
 */
function aisp_executeWithTimeout(fn, args, timeout) {
    return new Promise((resolve, reject) => {
        const timer = setTimeout(() => {
            reject(new Error(`函数执行超时: ${fn.name} (${timeout}ms)`));
        }, timeout);

        Promise.resolve(fn.apply(this, args))
            .then(result => {
                clearTimeout(timer);
                resolve(result);
            })
            .catch(error => {
                clearTimeout(timer);
                reject(error);
            });
    });
}

/**
 * @function aisp_generateRequestId - 生成请求ID
 * @description 根据函数名和参数生成唯一的请求标识符
 * @param {string} functionName - 函数名称
 * @param {Array} args - 参数数组
 * @returns {string} 唯一的请求ID
 */
function aisp_generateRequestId(functionName, args) {
    const argsString = JSON.stringify(args);
    const hash = aisp_simpleHash(functionName + argsString);
    return `${functionName}_${hash}`;
}

/**
 * @function aisp_simpleHash - 简单哈希函数
 * @description 生成字符串的简单哈希值，用于生成唯一标识
 * @param {string} str - 输入字符串
 * @returns {string} 哈希值（36进制字符串）
 */
function aisp_simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
}

/**
 * @function aisp_delay - 延迟函数
 * @description 创建指定时间的延迟Promise，用于重试机制
 * @param {number} ms - 延迟毫秒数
 * @returns {Promise} 延迟Promise
 */
function aisp_delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
// #endregion

// #region 内存优化
/**
 * @function aisp_startMemoryMonitoring - 启动内存监控
 * @description 启动内存使用监控和定期清理机制
 */
function aisp_startMemoryMonitoring() {
    setInterval(() => {
        aisp_checkMemoryUsage();
    }, AISP_PERFORMANCE_CONFIG.MEMORY_OPTIMIZATION.memoryCheckInterval);

    setInterval(() => {
        aisp_performMemoryCleanup();
    }, AISP_PERFORMANCE_CONFIG.MEMORY_OPTIMIZATION.cleanupInterval);

    console.log('🧠 内存监控已启动');
}

/**
 * @function aisp_checkMemoryUsage - 检查内存使用
 * @description 检查当前内存使用情况，超过阈值时触发清理
 */
function aisp_checkMemoryUsage() {
    if (performance.memory) {
        const memoryInfo = performance.memory;
        aisp_performanceMetrics.memoryUsage.current = memoryInfo.usedJSHeapSize;
        aisp_performanceMetrics.memoryUsage.peak = Math.max(
            aisp_performanceMetrics.memoryUsage.peak,
            memoryInfo.usedJSHeapSize
        );

        // 检查是否超过阈值
        if (memoryInfo.usedJSHeapSize > AISP_PERFORMANCE_CONFIG.MEMORY_OPTIMIZATION.memoryThreshold) {
            console.warn('⚠️ 内存使用超过阈值，触发清理');
            aisp_performMemoryCleanup();
        }
    }
}

/**
 * @function aisp_performMemoryCleanup - 执行内存清理
 * @description 执行内存清理操作，包括缓存清理和垃圾回收
 */
function aisp_performMemoryCleanup() {
    try {
        // 清理过期缓存
        if (aisp_cacheManager) {
            // 这里可以添加更智能的缓存清理逻辑
        }

        // 清理请求去重映射
        AISP_REQUEST_DEDUPLICATION.clear();

        // 建议垃圾回收
        if (typeof window !== 'undefined' && window.gc) {
            window.gc();
        }

        console.log('🧹 内存清理完成');

    } catch (error) {
        console.error('内存清理失败:', error);
    }
}
// #endregion

// #region 渲染优化
/**
 * @function aisp_debounce - 防抖函数
 * @description 创建防抖函数，避免频繁执行，提高性能
 * @param {Function} func - 要防抖的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @param {string} key - 函数标识键
 * @returns {Function} 防抖后的函数
 */
function aisp_debounce(func, delay = AISP_PERFORMANCE_CONFIG.RENDER_OPTIMIZATION.debounceDelay, key = null) {
    const funcKey = key || func.name || 'anonymous';

    if (AISP_DEBOUNCED_FUNCTIONS.has(funcKey)) {
        return AISP_DEBOUNCED_FUNCTIONS.get(funcKey);
    }

    let timeoutId;
    const debouncedFunc = function(...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };

    AISP_DEBOUNCED_FUNCTIONS.set(funcKey, debouncedFunc);
    return debouncedFunc;
}

/**
 * @function aisp_throttle - 节流函数
 * @description 创建节流函数，限制执行频率，防止过度渲染
 * @param {Function} func - 要节流的函数
 * @param {number} delay - 节流间隔（毫秒）
 * @param {string} key - 函数标识键
 * @returns {Function} 节流后的函数
 */
function aisp_throttle(func, delay = AISP_PERFORMANCE_CONFIG.RENDER_OPTIMIZATION.throttleDelay, key = null) {
    const funcKey = key || func.name || 'anonymous';

    if (AISP_THROTTLED_FUNCTIONS.has(funcKey)) {
        return AISP_THROTTLED_FUNCTIONS.get(funcKey);
    }

    let lastExecTime = 0;
    const throttledFunc = function(...args) {
        const now = Date.now();
        if (now - lastExecTime >= delay) {
            lastExecTime = now;
            return func.apply(this, args);
        }
    };

    AISP_THROTTLED_FUNCTIONS.set(funcKey, throttledFunc);
    return throttledFunc;
}

/**
 * @function aisp_optimizeRender - 优化渲染函数
 * @description 为渲染函数添加性能优化，包括防抖、节流和性能分析
 * @param {Function} renderFunc - 渲染函数
 * @param {Object} options - 优化选项配置
 * @returns {Function} 优化后的渲染函数
 */
function aisp_optimizeRender(renderFunc, options = {}) {
    const {
        enableDebouncing = AISP_PERFORMANCE_CONFIG.RENDER_OPTIMIZATION.enableDebouncing,
        debounceDelay = AISP_PERFORMANCE_CONFIG.RENDER_OPTIMIZATION.debounceDelay,
        enableThrottling = false,
        throttleDelay = AISP_PERFORMANCE_CONFIG.RENDER_OPTIMIZATION.throttleDelay,
        enableProfiling = true
    } = options;

    let optimizedFunc = renderFunc;

    // 添加性能分析
    if (enableProfiling) {
        optimizedFunc = aisp_profileFunction(optimizedFunc, 'render');
    }

    // 添加防抖
    if (enableDebouncing) {
        optimizedFunc = aisp_debounce(optimizedFunc, debounceDelay, renderFunc.name);
    }

    // 添加节流
    if (enableThrottling) {
        optimizedFunc = aisp_throttle(optimizedFunc, throttleDelay, renderFunc.name);
    }

    return optimizedFunc;
}

/**
 * @function aisp_profileFunction - 函数性能分析
 * @description 为函数添加性能分析，记录执行时间和错误
 * @param {Function} func - 要分析的函数
 * @param {string} category - 分析类别（如'render', 'api'等）
 * @returns {Function} 带性能分析的函数
 */
function aisp_profileFunction(func, category = 'general') {
    return function(...args) {
        const startTime = performance.now();

        try {
            const result = func.apply(this, args);

            // 如果是Promise，等待完成后记录时间
            if (result && typeof result.then === 'function') {
                return result.finally(() => {
                    const endTime = performance.now();
                    aisp_recordExecutionTime(func.name, endTime - startTime, category);
                });
            } else {
                const endTime = performance.now();
                aisp_recordExecutionTime(func.name, endTime - startTime, category);
                return result;
            }

        } catch (error) {
            const endTime = performance.now();
            aisp_recordExecutionTime(func.name, endTime - startTime, category, true);
            throw error;
        }
    };
}

/**
 * @function aisp_recordExecutionTime - 记录执行时间
 * @description 记录函数执行时间到性能指标，用于性能监控
 * @param {string} functionName - 函数名称
 * @param {number} executionTime - 执行时间（毫秒）
 * @param {string} category - 类别（如'render', 'api'等）
 * @param {boolean} isError - 是否出错
 */
function aisp_recordExecutionTime(functionName, executionTime, category, isError = false) {
    if (category === 'render') {
        aisp_performanceMetrics.renderTime.total += executionTime;
        aisp_performanceMetrics.renderTime.count++;
        aisp_performanceMetrics.renderTime.average =
            aisp_performanceMetrics.renderTime.total / aisp_performanceMetrics.renderTime.count;
    }

    // 检查是否超过最大渲染时间
    if (category === 'render' && executionTime > AISP_PERFORMANCE_CONFIG.RENDER_OPTIMIZATION.maxRenderTime) {
        console.warn(`⚠️ 渲染时间过长: ${functionName} - ${executionTime.toFixed(2)}ms`);
    }

    if (!isError) {
        console.log(`⏱️ ${category}执行时间: ${functionName} - ${executionTime.toFixed(2)}ms`);
    }
}

/**
 * @function aisp_requestAnimationFrame - 优化的动画帧请求
 * @description 使用requestAnimationFrame优化动画，并记录执行时间
 * @param {Function} callback - 动画回调函数
 * @returns {number} 动画帧请求ID
 */
function aisp_requestAnimationFrame(callback) {
    return requestAnimationFrame(() => {
        const startTime = performance.now();
        callback();
        const endTime = performance.now();
        aisp_recordExecutionTime('animationFrame', endTime - startTime, 'render');
    });
}
// #endregion

// #region 性能监控
/**
 * @function aisp_startPerformanceMonitoring - 启动性能监控
 * @description 启动性能指标监控，包括页面性能和定期指标收集
 */
function aisp_startPerformanceMonitoring() {
    // 监控页面性能
    if (typeof PerformanceObserver !== 'undefined') {
        try {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    aisp_handlePerformanceEntry(entry);
                }
            });

            observer.observe({ entryTypes: ['measure', 'navigation', 'resource'] });
            console.log('📊 性能监控已启动');

        } catch (error) {
            console.warn('性能监控启动失败:', error);
        }
    }

    // 定期收集性能指标
    setInterval(() => {
        aisp_collectPerformanceMetrics();
    }, 30000); // 30秒收集一次
}

/**
 * @function aisp_handlePerformanceEntry - 处理性能条目
 * @description 处理性能监控条目，记录各种性能指标
 * @param {PerformanceEntry} entry - 性能条目对象
 */
function aisp_handlePerformanceEntry(entry) {
    switch (entry.entryType) {
        case 'measure':
            console.log(`📏 性能测量: ${entry.name} - ${entry.duration.toFixed(2)}ms`);
            break;
        case 'navigation':
            console.log(`🧭 导航性能: 加载时间 ${entry.loadEventEnd - entry.navigationStart}ms`);
            break;
        case 'resource':
            if (entry.duration > 1000) { // 超过1秒的资源加载
                console.warn(`⚠️ 资源加载缓慢: ${entry.name} - ${entry.duration.toFixed(2)}ms`);
            }
            break;
    }
}

/**
 * @function aisp_collectPerformanceMetrics - 收集性能指标
 * @description 收集当前的性能指标并检查阈值
 */
function aisp_collectPerformanceMetrics() {
    const metrics = aisp_getPerformanceMetrics();

    // 检查性能阈值
    if (metrics.memory.current > AISP_PERFORMANCE_CONFIG.MEMORY_OPTIMIZATION.memoryThreshold) {
        console.warn('⚠️ 内存使用过高:', aisp_formatBytes(metrics.memory.current));
    }

    if (metrics.render.average > AISP_PERFORMANCE_CONFIG.RENDER_OPTIMIZATION.maxRenderTime) {
        console.warn('⚠️ 平均渲染时间过长:', metrics.render.average.toFixed(2) + 'ms');
    }

    // 记录到控制台（可以扩展为发送到监控服务）
    console.log('📊 性能指标:', {
        运行时间: aisp_formatDuration(Date.now() - metrics.startTime),
        API调用: `${metrics.api.total}次 (缓存命中率: ${metrics.api.cacheHitRate})`,
        内存使用: aisp_formatBytes(metrics.memory.current),
        平均渲染时间: metrics.render.average.toFixed(2) + 'ms'
    });
}

/**
 * @function aisp_getPerformanceMetrics - 获取性能指标
 * @description 获取当前的性能指标，包括API、内存、渲染等统计信息
 * @returns {Object} 性能指标对象
 */
function aisp_getPerformanceMetrics() {
    const cacheStats = aisp_cacheManager ? aisp_cacheManager.getStats() : null;

    return {
        startTime: aisp_performanceMetrics.startTime,
        api: {
            total: aisp_performanceMetrics.apiCalls.total,
            cached: aisp_performanceMetrics.apiCalls.cached,
            failed: aisp_performanceMetrics.apiCalls.failed,
            cacheHitRate: aisp_performanceMetrics.apiCalls.total > 0 ?
                ((aisp_performanceMetrics.apiCalls.cached / aisp_performanceMetrics.apiCalls.total) * 100).toFixed(1) + '%' : '0%'
        },
        memory: {
            current: aisp_performanceMetrics.memoryUsage.current,
            peak: aisp_performanceMetrics.memoryUsage.peak
        },
        render: {
            total: aisp_performanceMetrics.renderTime.total,
            count: aisp_performanceMetrics.renderTime.count,
            average: aisp_performanceMetrics.renderTime.average
        },
        cache: cacheStats,
        isInitialized: aisp_isInitialized
    };
}

/**
 * @function aisp_formatBytes - 格式化字节数
 * @description 将字节数格式化为可读格式（B, KB, MB, GB）
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的字符串
 */
function aisp_formatBytes(bytes) {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * @function aisp_formatDuration - 格式化持续时间
 * @description 将毫秒数格式化为可读的持续时间（小时、分钟、秒）
 * @param {number} ms - 毫秒数
 * @returns {string} 格式化后的时间字符串
 */
function aisp_formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
        return `${hours}小时${minutes % 60}分钟`;
    } else if (minutes > 0) {
        return `${minutes}分钟${seconds % 60}秒`;
    } else {
        return `${seconds}秒`;
    }
}

/**
 * @function getPerformanceOptimizer - 获取性能优化器实例
 * @description 获取全局性能优化器实例，提供统一的API接口
 * @returns {Object} 性能优化器API对象
 */
function getPerformanceOptimizer() {
    return {
        // 初始化
        initialize: aisp_initialize,

        // API优化
        optimizeApiCall: aisp_optimizeApiCall,

        // 渲染优化
        debounce: aisp_debounce,
        throttle: aisp_throttle,
        optimizeRender: aisp_optimizeRender,
        requestAnimationFrame: aisp_requestAnimationFrame,

        // 性能分析
        profileFunction: aisp_profileFunction,
        getMetrics: aisp_getPerformanceMetrics,

        // 工具函数
        formatBytes: aisp_formatBytes,
        formatDuration: aisp_formatDuration,

        // 常量
        PERFORMANCE_CONFIG: AISP_PERFORMANCE_CONFIG
    };
}
// #endregion

console.log('⚡ AI Side Panel 性能优化器已加载');
